"""
AI Content Generator for Channel Content Repurposer

This module handles interactions with AI services for content generation:
- Rewriting content using OpenAI's GPT models
- Generating creative variations of content
- Optimizing content for different platforms
"""

import os
import json
import time
from openai import OpenAI


class AIContentGenerator:
    """Handles content generation using AI services"""
    
    def __init__(self, api_key=None):
        """
        Initialize the AI Content Generator
        
        Args:
            api_key (str, optional): OpenAI API key
        """
        self.api_key = api_key
        self.client = None
        
        if api_key:
            self.client = OpenAI(api_key=api_key)
    
    def set_api_key(self, api_key):
        """
        Set or update the OpenAI API key
        
        Args:
            api_key (str): OpenAI API key
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.api_key = api_key
            self.client = OpenAI(api_key=api_key)
            return True
        except Exception as e:
            print(f"Error setting API key: {str(e)}")
            return False
    
    def rewrite_content(self, original_content, video_title=None, style="standard", 
                        model="gpt-3.5-turbo", max_tokens=2000, temperature=0.7):
        """
        Rewrite content using OpenAI's GPT models
        
        Args:
            original_content (str): Original content to rewrite
            video_title (str, optional): Title of the video for context
            style (str, optional): Style of rewriting (standard, creative, formal, casual)
            model (str, optional): OpenAI model to use
            max_tokens (int, optional): Maximum tokens in the response
            temperature (float, optional): Creativity level (0.0-1.0)
            
        Returns:
            str: Rewritten content or None if failed
        """
        if not self.client:
            print("OpenAI client not initialized. Please set API key first.")
            return None
        
        # Truncate content if too long (to avoid token limits)
        content_limit = 12000 if "gpt-4" in model else 3000
        truncated_content = original_content[:content_limit]
        
        # Prepare system message based on style
        system_messages = {
            "standard": "You are a helpful assistant that rewrites video content while maintaining the same information and key points.",
            "creative": "You are a creative writer who transforms video content into engaging, unique narratives while preserving the core information.",
            "formal": "You are a professional editor who rewrites video content in a formal, authoritative tone while maintaining accuracy.",
            "casual": "You are a conversational writer who rewrites video content in a friendly, casual tone that feels natural and engaging."
        }
        
        system_message = system_messages.get(style, system_messages["standard"])
        
        # Prepare the prompt
        title_context = f" titled \"{video_title}\"" if video_title else ""
        
        prompt = f"""
        I have a transcript from a YouTube video{title_context}.
        Please rewrite this content in your own words, maintaining the same information and key points,
        but making it more engaging and well-structured. The content should be suitable for a new video.
        
        Here's the original transcript:
        
        {truncated_content}
        
        Please provide only the rewritten content without any explanations or notes.
        """
        
        try:
            # Call the OpenAI API
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            # Extract the rewritten content
            rewritten_content = response.choices[0].message.content.strip()
            return rewritten_content
            
        except Exception as e:
            print(f"Error rewriting content: {str(e)}")
            return None
    
    def generate_title(self, content, original_title=None, model="gpt-3.5-turbo"):
        """
        Generate an engaging title for the content
        
        Args:
            content (str): Content to generate a title for
            original_title (str, optional): Original title for reference
            model (str, optional): OpenAI model to use
            
        Returns:
            str: Generated title or None if failed
        """
        if not self.client:
            print("OpenAI client not initialized. Please set API key first.")
            return None
        
        # Truncate content if too long
        truncated_content = content[:1000]
        
        # Prepare the prompt
        original_context = f"\nThe original title was: \"{original_title}\"" if original_title else ""
        
        prompt = f"""
        Please create an engaging, click-worthy title for a YouTube video based on the following content.
        The title should be catchy but not clickbait, accurately represent the content, and be optimized for search.
        Keep it under 60 characters if possible.{original_context}
        
        Content:
        {truncated_content}
        
        Return only the title without quotes or any additional text.
        """
        
        try:
            # Call the OpenAI API
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "You are a YouTube content specialist who creates engaging titles."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=60,
                temperature=0.7
            )
            
            # Extract the title
            title = response.choices[0].message.content.strip()
            
            # Remove quotes if present
            title = title.strip('"\'')
            
            return title
            
        except Exception as e:
            print(f"Error generating title: {str(e)}")
            return None
    
    def generate_description(self, content, title=None, model="gpt-3.5-turbo"):
        """
        Generate a YouTube video description
        
        Args:
            content (str): Content to generate a description for
            title (str, optional): Video title for context
            model (str, optional): OpenAI model to use
            
        Returns:
            str: Generated description or None if failed
        """
        if not self.client:
            print("OpenAI client not initialized. Please set API key first.")
            return None
        
        # Truncate content if too long
        truncated_content = content[:1500]
        
        # Prepare the prompt
        title_context = f"for a video titled \"{title}\"" if title else ""
        
        prompt = f"""
        Please create an engaging YouTube video description {title_context} based on the following content.
        The description should:
        1. Summarize the key points in the first 2-3 sentences
        2. Include relevant keywords naturally
        3. Be between 100-200 words
        4. End with a call to action
        
        Content:
        {truncated_content}
        
        Return only the description without any additional text.
        """
        
        try:
            # Call the OpenAI API
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "You are a YouTube content specialist who creates engaging video descriptions."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )
            
            # Extract the description
            description = response.choices[0].message.content.strip()
            return description
            
        except Exception as e:
            print(f"Error generating description: {str(e)}")
            return None
    
    def generate_tags(self, content, title=None, model="gpt-3.5-turbo"):
        """
        Generate YouTube tags for the video
        
        Args:
            content (str): Content to generate tags for
            title (str, optional): Video title for context
            model (str, optional): OpenAI model to use
            
        Returns:
            list: List of tags or None if failed
        """
        if not self.client:
            print("OpenAI client not initialized. Please set API key first.")
            return None
        
        # Truncate content if too long
        truncated_content = content[:1000]
        
        # Prepare the prompt
        title_context = f"for a video titled \"{title}\"" if title else ""
        
        prompt = f"""
        Please create a list of relevant YouTube tags {title_context} based on the following content.
        The tags should:
        1. Include a mix of specific and broad keywords
        2. Be relevant to the content
        3. Include some long-tail keywords
        4. Be between 5-15 tags total
        
        Content:
        {truncated_content}
        
        Return the tags as a comma-separated list without any additional text.
        """
        
        try:
            # Call the OpenAI API
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "You are a YouTube SEO specialist who creates effective video tags."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.7
            )
            
            # Extract and process the tags
            tags_text = response.choices[0].message.content.strip()
            tags = [tag.strip() for tag in tags_text.split(',')]
            return tags
            
        except Exception as e:
            print(f"Error generating tags: {str(e)}")
            return None
