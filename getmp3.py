import os
import subprocess
import sys

def download_audio():
    try:
        # Check if yt-dlp is installed
        try:
            subprocess.run(["yt-dlp", "--version"], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("yt-dlp is not installed or not in PATH.")
            install = input("Would you like to install yt-dlp now? (y/n): ")
            if install.lower() == 'y':
                print("Installing yt-dlp...")
                subprocess.run([sys.executable, "-m", "pip", "install", "yt-dlp"], check=True)
                print("yt-dlp installed successfully!")
            else:
                print("Please install yt-dlp manually and try again.")
                return

        # Get YouTube URL from user
        video_url = input("Enter the YouTube video URL: ")
        print(f"Attempting to download from: {video_url}")

        # Get output filename from user (optional)
        output_name = input("Enter output filename (leave blank for default): ")

        if output_name:
            if not output_name.endswith('.mp3'):
                output_name = output_name.split('.')[0] + '.mp3'
        else:
            output_name = "output.mp3"

        print(f"Output will be saved as: {output_name}")
        print("Starting download...")

        # Use the direct command-line approach that doesn't require ffmpeg for extraction
        command = [
            "yt-dlp",
            "--no-check-certificate",  # Skip HTTPS certificate validation
            "--no-playlist",           # Download only the video, not the playlist
            "--ignore-errors",         # Continue on download errors
            "--no-warnings",           # Suppress warnings
            "--format", "bestaudio",   # Get best audio quality
            "--output", output_name,   # Set output filename
            video_url
        ]

        # Run the command and capture output
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        # Print output in real-time
        for line in process.stdout:
            print(line.strip())

        # Wait for the process to complete
        process.wait()

        # Check if the download was successful
        if process.returncode == 0:
            print(f"Download completed successfully! File saved as: {output_name}")
        else:
            print("Download failed.")
            print("Error details:")
            for line in process.stderr:
                print(line.strip())

            print("\nTrying alternative method...")

            # Try a simpler command that just downloads the best audio
            alt_command = [
                "yt-dlp",
                "--no-check-certificate",
                video_url,
                "-o", output_name
            ]

            print("Running alternative command...")
            alt_process = subprocess.run(alt_command, capture_output=True, text=True)

            if alt_process.returncode == 0:
                print(f"Alternative download method succeeded! File saved as: {output_name}")
            else:
                print("All download methods failed.")
                print("Error details:")
                print(alt_process.stderr)
                print("\nPlease try installing ffmpeg or using a different YouTube URL.")

    except Exception as e:
        print(f"An error occurred: {str(e)}")

if __name__ == "__main__":
    download_audio()
