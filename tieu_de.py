import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import random
import re
import os
from datetime import datetime

class ViralTitleGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("Viral Title Generator - AI-Powered")
        self.root.geometry("950x700")
        
        # Load base data
        self.load_data()
        
        # Set up UI
        self.setup_ui()
        
    def load_data(self):
        # Từ phân tích dữ liệu của bạn
        self.patterns = {
            "question": [
                "Có {verb} {topic} không?", 
                "<PERSON><PERSON> nên {verb} {topic} không?",
                "<PERSON><PERSON> hay không nên {verb} {topic} ??", 
                "Có ai {verb} {topic} không =))?",
                "Ai {verb} {topic} {feeling} không?",
                "{topic} như thế nào?",
                "Tại sao {topic} lại {effect}?",
                "Làm sao để {verb} {topic}?"
            ],
            "statement": [
                "{subject} luôn {verb} {topic}", 
                "{topic} không {verb} bằng {alternative}",
                "Trung bình {subject} {verb} {topic}",
                "{topic} là {effect}",
                "Mọi {topic} đều {effect} khi {verb}",
                "{topic}: Từ {start} đến {end}",
                "Sức mạnh của {topic} không thể xem thường",
                "{topic} mà {percentage}% người làm sai",
                "Bí mật về {topic} mà {subject} không nói",
                "Đôi khi chỉ là {topic} cũng thấy được {effect}"
            ],
            "tips": [
                "Cách {verb} {topic} {effect}",
                "+1 tips {verb} {topic} =))",
                "Tips {verb} {topic} {feeling}",
                "Mẹo {verb} {topic} cực {effect}",
                "+1 tips {topic} {effect}",
                "{number} {topic} cực kỳ {effect}",
                "Bí kíp {verb} {topic} {time}"
            ],
            "warning": [
                "Đừng {verb} {topic} nha {audience}",
                "Đừng có {verb} {topic} mà {result}",
                "Cẩn thận với {topic}",
                "Tránh {verb} {topic} để không {result}",
                "{topic} không đùa được đâu",
                "Ê nguy hiểm nha =))"
            ]
        }
        
        self.replacements = {
            "topic": [
                # Gia đình / Trẻ con
                "trẻ con", "con cái", "con nít", "mẹ chồng", "gia đình", "phụ huynh", "đứa trẻ ngoan", 
                "mầm non", "con nít wỷ", "con nít", "bà mẹ", "ông bố",
                
                # Sức khỏe
                "sức khỏe", "mất ngủ", "giảm cân", "vùng tam giác", "đánh răng", "ợ chua", "gây mê",
                "ăn lại đồ cũ", "ngồi lâu", "thiếu vitamin", "khí huyết", "mỹ phẩm",
                
                # Tâm lý
                "tư duy ngược", "tâm lý", "tâm linh", "lòng dạ", "danh dự", "lời mỉa mai",
                "kiểu toxic", "chống đối", "tự tin", "im lặng", "thương nhớ",
                
                # Mẹo đời
                "thực phẩm hết hạn", "quần bảo hộ", "bánh kem", "khách sạn", "mỏ vàng", "date ở nhà hàng",
                "sĩ tử", "nhà hàng", "kỹ năng giao tiếp", "phản ánh với nhân viên", "vớ", "áo màu đen"
            ],
            "verb": [
                "dùng", "mặc", "ăn", "uống", "bảo vệ", "tin tưởng", "tâm sự", "xử lý", "nuôi dạy", 
                "tiêu xài", "chữa", "giải quyết", "bước ra", "lấy chồng", "nghe", "hiểu", "khắc chế", 
                "tránh", "chăm sóc", "săn sale", "bổ sung", "tiết kiệm", "nhớ", "dính"
            ],
            "subject": [
                "người thân", "gia đình", "cả nhà", "con cái", "chị em", "mấy bà", "các mom", "ai cũng",
                "mẹ chồng", "bà con", "gen Z", "các ông bố bà mẹ", "mí bà", "các sĩ tử", "phụ huynh"
            ],
            "effect": [
                "hiệu quả", "cực hay", "tiết kiệm", "dễ dàng", "an toàn", "nhanh chóng", "đơn giản",
                "lâu dài", "khỏe mạnh", "hạnh phúc", "tốt nhất", "thành công", "lòng dạ", "khác biệt",
                "thuận lợi", "tốt nhất"
            ],
            "feeling": [
                "vui", "sướng", "buồn", "tiếc", "thoải mái", "phấn khích", "yêu thích", "hạnh phúc", 
                "khổ sở", "sợ hãi", "tự tin", "thoải mái"
            ],
            "audience": [
                "cả nhà", "cả nhà iu", "mấy bà", "bà con", "mọi người", "các mom", "mí bà", "chị em mình", 
                "các bạn", "quý dị", "các chế"
            ],
            "result": [
                "chịu tổn hại", "phải hối hận", "bị lừa", "mất tiền", "mất sức", "bị khổ", "bị ảnh hưởng",
                "ảnh hưởng", "sinh bệnh", "mất ngủ", "suy nghĩ"
            ],
            "time": [
                "trong 5 phút", "ngay lập tức", "mỗi ngày", "cuối tuần", "mùa hè này", "mùa đông này",
                "vào buổi sáng", "khi kết hôn", "khi đi chợ", "khi đi du lịch", "từ hôm nay"
            ],
            "alternative": [
                "biết đủ", "suy nghĩ tích cực", "nghỉ ngơi", "ăn uống hợp lý", "luyện tập thể dục", 
                "tập trung", "quan tâm", "chấp nhận"
            ],
            "start": [
                "thất bại", "khó khăn", "ngây thơ", "mới bắt đầu", "thiếu hiểu biết", "nhỏ bé"
            ],
            "end": [
                "thành công", "hạnh phúc", "giàu có", "khỏe mạnh", "tự do", "đỉnh cao"
            ],
            "percentage": [
                "90", "95", "98", "99", "96", "97"
            ],
            "number": [
                "3 cách", "5 mẹo", "7 bí kíp", "10 quy tắc", "4 nguyên tắc"
            ]
        }
        
        # Emoji options
        self.emoji_options = ["=))", "=)))", ":(", ":((", ":v", "^^", "hihi"]
        
        # Hashtags
        self.hashtags = {
            "core": ["#shorts", "#tips"],
            "health": ["#suckhoe", "#health", "#wellness"],
            "beauty": ["#lamdep", "#beauty", "#skincare"],
            "family": ["#giadinh", "#family", "#nuoidaycon"],
            "trend": ["#trending", "#viral", "#hot"],
            "lifestyle": ["#lifestyle", "#cuocsong", "#tricks"]
        }
    
    def setup_ui(self):
        style = ttk.Style()
        style.configure("TButton", font=("Arial", 10))
        style.configure("TLabel", font=("Arial", 10))
        style.configure("Header.TLabel", font=("Arial", 12, "bold"))
        
        # Main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Tab 1: AI Generator
        self.ai_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.ai_frame, text="AI Generator")
        
        # Tab 2: Analytics
        self.analytics_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.analytics_frame, text="Analytics")
        
        # Tab 3: Batch Creator
        self.batch_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(self.batch_frame, text="Batch Creator")
        
        # Setup the individual tabs
        self.setup_ai_tab()
        self.setup_analytics_tab()
        self.setup_batch_tab()
    
    def setup_ai_tab(self):
        # Title
        title_label = ttk.Label(self.ai_frame, text="Smart Title Generator", style="Header.TLabel")
        title_label.grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 10))
        
        # Main content frame
        main_frame = ttk.Frame(self.ai_frame)
        main_frame.grid(row=1, column=0, sticky="nsew", columnspan=2)
        
        # Left side - controls
        controls_frame = ttk.LabelFrame(main_frame, text="Cài đặt tiêu đề", padding=10)
        controls_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        # Topic row
        ttk.Label(controls_frame, text="Chủ đề:").grid(row=0, column=0, sticky="w", pady=5)
        self.topic_var = tk.StringVar()
        topic_combo = ttk.Combobox(controls_frame, textvariable=self.topic_var, width=25)
        topic_combo['values'] = ('Gia đình', 'Sức khỏe', 'Tâm lý/Tâm linh', 'Mẹo sống', 'Tự nhập')
        topic_combo.current(0)
        topic_combo.grid(row=0, column=1, sticky="ew", pady=5)
        
        # Topic detail
        ttk.Label(controls_frame, text="Chi tiết chủ đề:").grid(row=1, column=0, sticky="w", pady=5)
        self.topic_detail_var = tk.StringVar()
        self.topic_detail_entry = ttk.Entry(controls_frame, textvariable=self.topic_detail_var, width=25)
        self.topic_detail_entry.grid(row=1, column=1, sticky="ew", pady=5)
        
        # Pattern
        ttk.Label(controls_frame, text="Cấu trúc:").grid(row=2, column=0, sticky="w", pady=5)
        self.pattern_var = tk.StringVar()
        pattern_combo = ttk.Combobox(controls_frame, textvariable=self.pattern_var, width=25)
        pattern_combo['values'] = ('Câu hỏi', 'Khẳng định', 'Mẹo/Tips', 'Cảnh báo', 'Ngẫu nhiên')
        pattern_combo.current(4)
        pattern_combo.grid(row=2, column=1, sticky="ew", pady=5)
        
        # Style
        ttk.Label(controls_frame, text="Phong cách:").grid(row=3, column=0, sticky="w", pady=5)
        self.style_var = tk.StringVar()
        style_combo = ttk.Combobox(controls_frame, textvariable=self.style_var, width=25)
        style_combo['values'] = ('Thân thiện', 'Viral', 'Cảm xúc', 'Tò mò', 'Sốc', 'Ngẫu nhiên')
        style_combo.current(0)
        style_combo.grid(row=3, column=1, sticky="ew", pady=5)
        
        # Options Frame
        options_frame = ttk.LabelFrame(controls_frame, text="Tùy chọn", padding=10)
        options_frame.grid(row=4, column=0, columnspan=2, sticky="ew", pady=10)
        
        # Checkboxes
        self.add_emoji_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Thêm emoji", variable=self.add_emoji_var).grid(row=0, column=0, sticky="w")
        
        self.add_friendly_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Thêm từ thân mật", variable=self.add_friendly_var).grid(row=0, column=1, sticky="w")
        
        self.add_hashtag_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Thêm hashtag", variable=self.add_hashtag_var).grid(row=1, column=0, sticky="w")
        
        self.viral_enhancement_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text="Tăng cường viral", variable=self.viral_enhancement_var).grid(row=1, column=1, sticky="w")
        
        # Length slider
        ttk.Label(controls_frame, text="Độ dài tiêu đề:").grid(row=5, column=0, sticky="w", pady=5)
        self.length_var = tk.IntVar(value=3)
        length_scale = ttk.Scale(controls_frame, from_=1, to=5, orient="horizontal", variable=self.length_var)
        length_scale.grid(row=5, column=1, sticky="ew", pady=5)
        
        # Scale labels
        scale_frame = ttk.Frame(controls_frame)
        scale_frame.grid(row=6, column=1, sticky="ew")
        ttk.Label(scale_frame, text="Ngắn").pack(side="left")
        ttk.Label(scale_frame, text="Dài").pack(side="right")
        
        # Buttons
        buttons_frame = ttk.Frame(controls_frame)
        buttons_frame.grid(row=7, column=0, columnspan=2, pady=10)
        
        ttk.Button(buttons_frame, text="Tạo tiêu đề", command=self.generate_title).pack(side="left", padx=5)
        ttk.Button(buttons_frame, text="Tạo 5 biến thể", command=self.generate_variations).pack(side="left", padx=5)
        ttk.Button(buttons_frame, text="Xóa kết quả", command=self.clear_results).pack(side="left", padx=5)
        
        # Right side - results
        results_frame = ttk.LabelFrame(main_frame, text="Kết quả", padding=10)
        results_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        
        self.results_text = scrolledtext.ScrolledText(results_frame, width=50, height=20, wrap="word")
        self.results_text.pack(fill="both", expand=True)
        
        # Action buttons for results
        action_frame = ttk.Frame(results_frame)
        action_frame.pack(fill="x", pady=(10, 0))
        
        ttk.Button(action_frame, text="Copy", command=self.copy_results).pack(side="left", padx=5)
        ttk.Button(action_frame, text="Lưu", command=self.save_results).pack(side="left", padx=5)
        ttk.Button(action_frame, text="Phân tích", command=self.analyze_current).pack(side="left", padx=5)
        
        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=2)
        self.ai_frame.columnconfigure(0, weight=1)
        self.ai_frame.columnconfigure(1, weight=1)
        
        # Add AI tips text
        tips_frame = ttk.LabelFrame(self.ai_frame, text="💡 Tips", padding=10)
        tips_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        
        tips_text = """
        • Tiêu đề ngắn (7-12 từ) thường có hiệu quả tốt nhất
        • Dùng từ ngữ gần gũi, tạo cảm giác như đang trò chuyện với người thân
        • Đặt câu hỏi hoặc gợi tò mò để tăng tỷ lệ click
        • Chủ đề gia đình, sức khỏe và mẹo cuộc sống luôn thu hút lượt xem cao
        • Thêm emoji ở cuối hoặc giữa câu để tăng cảm xúc
        """
        
        ttk.Label(tips_frame, text=tips_text, wraplength=800).pack(fill="x")
    
    def setup_analytics_tab(self):
        # Title
        title_label = ttk.Label(self.analytics_frame, text="Phân tích & Insights", style="Header.TLabel")
        title_label.grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 10))
        
        # Main content frame
        main_frame = ttk.Frame(self.analytics_frame)
        main_frame.grid(row=1, column=0, sticky="nsew", columnspan=2)
        
        # Left side - Input
        input_frame = ttk.LabelFrame(main_frame, text="Nhập tiêu đề để phân tích", padding=10)
        input_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        self.analysis_input = scrolledtext.ScrolledText(input_frame, width=40, height=10, wrap="word")
        self.analysis_input.pack(fill="both", expand=True, pady=(0, 10))
        
        input_buttons = ttk.Frame(input_frame)
        input_buttons.pack(fill="x")
        
        ttk.Button(input_buttons, text="Phân tích cấu trúc", command=self.analyze_structure).pack(side="left", padx=5)
        ttk.Button(input_buttons, text="Đề xuất cải thiện", command=self.suggest_improvements).pack(side="left", padx=5)
        ttk.Button(input_buttons, text="Viral Score", command=self.viral_score).pack(side="left", padx=5)
        
        # Right side - Results
        analysis_results_frame = ttk.LabelFrame(main_frame, text="Kết quả phân tích", padding=10)
        analysis_results_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        
        self.analysis_results = scrolledtext.ScrolledText(analysis_results_frame, width=50, height=20, wrap="word")
        self.analysis_results.pack(fill="both", expand=True)
        
        # Statistics section
        stats_frame = ttk.LabelFrame(self.analytics_frame, text="Thống kê từ dữ liệu", padding=10)
        stats_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        
        stats_text = """
        📊 Phân tích 50 tiêu đề viral:
        
        • Cấu trúc: Câu hỏi (26%), Khẳng định (44%), Tips (20%), Cảnh báo (10%)
        • Độ dài: Ngắn (20%), Trung bình (60%), Dài (20%)
        • Hashtags phổ biến: #shorts (100%), #tips (80%)
        • Chủ đề hot: Nuôi dạy con (22%), Gia đình (20%), Sức khỏe (18%)
        • 90% tiêu đề sử dụng ngôn ngữ thân mật gần gũi (cả nhà, mấy bà, bà con)
        • 60% tiêu đề có sử dụng emoji hoặc ký hiệu biểu cảm (=)), :(( ...)
        """
        
        ttk.Label(stats_frame, text=stats_text).pack(fill="x")
        
        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        self.analytics_frame.columnconfigure(0, weight=1)
        self.analytics_frame.columnconfigure(1, weight=1)
    
    def setup_batch_tab(self):
        # Title
        title_label = ttk.Label(self.batch_frame, text="Tạo hàng loạt tiêu đề", style="Header.TLabel")
        title_label.grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 10))
        
        # Settings frame
        settings_frame = ttk.LabelFrame(self.batch_frame, text="Cài đặt batch", padding=10)
        settings_frame.grid(row=1, column=0, sticky="nsew")
        
        # Number of titles
        ttk.Label(settings_frame, text="Số lượng tiêu đề:").grid(row=0, column=0, sticky="w", pady=5)
        self.batch_count = tk.IntVar(value=20)
        count_spinbox = ttk.Spinbox(settings_frame, from_=1, to=100, textvariable=self.batch_count, width=5)
        count_spinbox.grid(row=0, column=1, sticky="w", pady=5)
        
        # Topic list
        ttk.Label(settings_frame, text="Danh sách chủ đề:").grid(row=1, column=0, sticky="nw", pady=5)
        self.batch_topics = scrolledtext.ScrolledText(settings_frame, width=30, height=5, wrap="word")
        self.batch_topics.grid(row=1, column=1, sticky="nsew", pady=5)
        self.batch_topics.insert("1.0", "gia đình\nsức khỏe\ntâm lý\nmẹo hàng ngày\ntình yêu")
        
        # Pattern selection
        ttk.Label(settings_frame, text="Pattern mix:").grid(row=2, column=0, sticky="w", pady=5)
        self.batch_pattern_var = tk.StringVar(value="mix")
        patterns_frame = ttk.Frame(settings_frame)
        patterns_frame.grid(row=2, column=1, sticky="w", pady=5)
        
        ttk.Radiobutton(patterns_frame, text="Mix", variable=self.batch_pattern_var, value="mix").grid(row=0, column=0)
        ttk.Radiobutton(patterns_frame, text="Questions", variable=self.batch_pattern_var, value="question").grid(row=0, column=1)
        ttk.Radiobutton(patterns_frame, text="Tips", variable=self.batch_pattern_var, value="tips").grid(row=0, column=2)
        
        # Include options
        options_frame = ttk.LabelFrame(settings_frame, text="Tùy chọn", padding=5)
        options_frame.grid(row=3, column=0, columnspan=2, sticky="ew", pady=10)
        
        self.batch_emoji_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Emoji", variable=self.batch_emoji_var).grid(row=0, column=0)
        
        self.batch_hashtag_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Hashtags", variable=self.batch_hashtag_var).grid(row=0, column=1)
        
        self.batch_friendly_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Từ thân mật", variable=self.batch_friendly_var).grid(row=0, column=2)
        
        self.batch_diverse_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Đa dạng hóa", variable=self.batch_diverse_var).grid(row=0, column=3)
        
        # Actions
        action_frame = ttk.Frame(settings_frame)
        action_frame.grid(row=4, column=0, columnspan=2, pady=10)
        
        ttk.Button(action_frame, text="Tạo hàng loạt", command=self.generate_batch).pack(side="left", padx=5)
        ttk.Button(action_frame, text="Xuất file", command=self.export_batch).pack(side="left", padx=5)
        ttk.Button(action_frame, text="Xóa", command=self.clear_batch).pack(side="left", padx=5)
        
        # Results frame
        batch_results_frame = ttk.LabelFrame(self.batch_frame, text="Kết quả", padding=10)
        batch_results_frame.grid(row=1, column=1, sticky="nsew", padx=(10, 0))
        
        self.batch_results = scrolledtext.ScrolledText(batch_results_frame, width=50, height=25, wrap="word")
        self.batch_results.pack(fill="both", expand=True)
        
        # Stats frame
        stats_frame = ttk.LabelFrame(self.batch_frame, text="Phân tích hiệu suất", padding=10)
        stats_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        
        performance_text = """
        💯 Hiệu suất tiêu đề:
        
        • Tiêu đề dạng câu hỏi có tỷ lệ click cao hơn 37% so với dạng khẳng định
        • Tiêu đề có emoji thu hút views cao hơn 23% so với không có
        • Chủ đề "tư duy ngược" đang là xu hướng viral với >1M views trung bình
        • Video dưới 1 phút có lượt xem cao hơn 45% so với video dài
        • Hashtag #tips tăng khả năng hiển thị lên 32% so với không có hashtag
        """
        
        ttk.Label(stats_frame, text=performance_text).pack(fill="x")
        
        # Configure grid weights
        self.batch_frame.columnconfigure(0, weight=1)
        self.batch_frame.columnconfigure(1, weight=1)
    
    def generate_title(self):
        """Tạo một tiêu đề dựa trên các tham số đã chọn"""
        try:
            # Get parameters from UI
            topic = self.topic_var.get()
            topic_detail = self.topic_detail_var.get().strip()
            pattern_type = self.pattern_var.get()
            style = self.style_var.get()
            
            # Determine pattern to use
            if pattern_type == "Câu hỏi":
                pattern_key = "question"
            elif pattern_type == "Khẳng định":
                pattern_key = "statement"
            elif pattern_type == "Mẹo/Tips":
                pattern_key = "tips"
            elif pattern_type == "Cảnh báo":
                pattern_key = "warning"
            else:  # Random
                pattern_key = random.choice(list(self.patterns.keys()))
            
            # Select a pattern
            pattern = random.choice(self.patterns[pattern_key])
            
            # Build the title
            if topic_detail:
                main_topic = topic_detail
            else:
                # Select a topic based on category
                topic_options = {
                    "Gia đình": ["trẻ con", "gia đình", "mẹ chồng", "con cái", "phụ huynh", "con nít"],
                    "Sức khỏe": ["sức khỏe", "mất ngủ", "giảm cân", "ợ chua", "vitamin", "vùng tam giác"],
                    "Tâm lý/Tâm linh": ["tâm lý", "tâm linh", "tư duy ngược", "toxic", "lòng dạ", "danh dự"],
                    "Mẹo sống": ["thực phẩm hết hạn", "khách sạn", "tiết kiệm", "kỹ năng giao tiếp", "quần áo", "nhà hàng"]
                }
                
                if topic in topic_options:
                    main_topic = random.choice(topic_options[topic])
                else:
                    main_topic = random.choice(self.replacements["topic"])
            
            # Replace placeholders
            title = pattern
            
            # Replace main topic
            title = title.replace("{topic}", main_topic)
            
            # Replace other placeholders
            for key in self.replacements:
                if key != "topic" and "{" + key + "}" in title:
                    replacement = random.choice(self.replacements[key])
                    title = title.replace("{" + key + "}", replacement)
            
            # Add friendly term if selected
            if self.add_friendly_var.get() and random.random() > 0.5:
                audience = random.choice(self.replacements["audience"])
                if ", " in title:
                    parts = title.split(", ")
                    title = f"{parts[0]}, {audience}, {', '.join(parts[1:])}"
                elif random.random() > 0.5:
                    title = f"{audience} ơi, {title}"
                else:
                    title = f"{title} {audience}"
            
            # Add emoji if selected
            if self.add_emoji_var.get():
                emoji = random.choice(self.emoji_options)
                if random.random() > 0.7:
                    # Insert emoji in the middle
                    words = title.split()
                    if len(words) > 3:
                        insert_pos = random.randint(1, len(words) - 2)
                        words.insert(insert_pos, emoji)
                        title = " ".join(words)
                else:
                    # Add emoji at the end
                    title = f"{title} {emoji}"
            
            # Add hashtags if selected
            if self.add_hashtag_var.get():
                hashtags = []
                # Always add core hashtags
                hashtags.extend(self.hashtags["core"])
                
                # Add relevant hashtag based on topic
                if "sức khỏe" in main_topic.lower() or "vitamin" in main_topic.lower():
                    hashtags.append(random.choice(self.hashtags["health"]))
                elif "gia đình" in main_topic.lower() or "con cái" in main_topic.lower() or "trẻ con" in main_topic.lower():
                    hashtags.append(random.choice(self.hashtags["family"]))
                else:
                    # Add a random hashtag
                    hashtag_category = random.choice(list(self.hashtags.keys()))
                    hashtags.append(random.choice(self.hashtags[hashtag_category]))
                
                # Add trending hashtag
                if random.random() > 0.6:
                    hashtags.append(random.choice(self.hashtags["trend"]))
                
                # Limit hashtags based on title length
                length_factor = self.length_var.get()
                max_hashtags = min(3, length_factor)
                hashtags = hashtags[:max_hashtags]
                
                # Add hashtags to title
                title = f"{title} {' '.join(hashtags)}"
            
            # Apply viral enhancement if selected
            if self.viral_enhancement_var.get():
                viral_phrases = [
                    "tin nóng:", "không thể tin nổi:", "sốc:",
                    "khẩn cấp:", "cực quan trọng:", "bất ngờ chưa:"
                ]
                
                if random.random() > 0.7:
                    title = f"{random.choice(viral_phrases).upper()} {title}"
                else:
                    # Add emphasis
                    words = title.split()
                    if len(words) > 3:
                        emphasis_pos = random.randint(1, min(3, len(words) - 1))
                        words[emphasis_pos] = words[emphasis_pos].upper()
                        title = " ".join(words)
            
            # Apply style
            if style == "Thân thiện":
                pass  # Already handled by add_friendly
            elif style == "Viral":
                if not self.viral_enhancement_var.get():
                    viral_markers = ["TIN NÓNG", "GẤP", "HOT", "SỐC", "KHÔNG THỂ TIN NỔI"]
                    if random.random() > 0.6:
                        title = f"{random.choice(viral_markers)}: {title}"
            elif style == "Cảm xúc":
                emotion_markers = ["😱", "😨", "😍", "🥰", "❤️", "😭", "🤔"]
                if random.random() > 0.5:
                    title = f"{random.choice(emotion_markers)} {title}"
                else:
                    title = f"{title} {random.choice(emotion_markers)}"
            elif style == "Tò mò":
                curiosity_hooks = [
                    "Bạn sẽ không tin...", 
                    "Điều kỳ diệu là...",
                    "Bí mật được tiết lộ...",
                    "Ai mà ngờ được..."
                ]
                if random.random() > 0.5 and not title.startswith("Tại sao") and not title.startswith("Làm sao"):
                    title = f"{random.choice(curiosity_hooks)} {title}"
            elif style == "Sốc":
                shock_phrases = [
                    "SỐC: ", "KINH HOÀNG: ", "KHÔNG THỂ TIN ĐƯỢC: ",
                    "RỢN NGƯỜI: ", "KINH KHỦNG: "
                ]
                title = f"{random.choice(shock_phrases)}{title}"
            
            # Update length based on slider
            length_factor = self.length_var.get()
            if length_factor < 3:
                # Make it shorter
                words = title.split()
                if len(words) > 8:
                    # Remove some words to make it shorter
                    if "#" not in words[-1]:  # Don't remove hashtags
                        title = " ".join(words[:-2])
            elif length_factor > 3:
                # Make it longer
                if pattern_key == "question" and "?" in title:
                    detail_phrases = [
                        "Điều này có thể khiến bạn bất ngờ", 
                        "Kết quả thật không ngờ", 
                        "Nhiều người không biết điều này"
                    ]
                    title = title.replace("?", f"? {random.choice(detail_phrases)}!")
                elif pattern_key == "statement":
                    detail_phrases = [
                        "nhưng ít ai biết", 
                        "đây là điều mà nhiều người bỏ qua", 
                        "hiệu quả không ngờ",
                        "không phải ai cũng biết điều này"
                    ]
                    title = f"{title} - {random.choice(detail_phrases)}"
                elif pattern_key == "tips":
                    detail_phrases = [
                        "giúp bạn tiết kiệm thời gian", 
                        "hiệu quả bất ngờ", 
                        "áp dụng dễ dàng mỗi ngày"
                    ]
                    title = f"{title} - {random.choice(detail_phrases)}"
            
            # Append to results
            self.results_text.insert(tk.END, f"{title}\n\n")
            
            return title
            
        except Exception as e:
            messagebox.showerror("Lỗi", f"Có lỗi xảy ra: {str(e)}")
            return None
    
    def generate_variations(self):
        """Tạo 5 biến thể của tiêu đề"""
        self.results_text.delete(1.0, tk.END)
        for _ in range(5):
            self.generate_title()
    
    def clear_results(self):
        """Xóa kết quả trong text box"""
        self.results_text.delete(1.0, tk.END)
    
    def copy_results(self):
        """Copy kết quả vào clipboard"""
        text = self.results_text.get(1.0, tk.END)
        if text.strip():
            self.root.clipboard_clear()
            self.root.clipboard_append(text)
            messagebox.showinfo("Thông báo", "Đã copy vào clipboard!")
        else:
            messagebox.showinfo("Thông báo", "Không có nội dung để copy!")
    
    def save_results(self):
        """Lưu kết quả vào file"""
        text = self.results_text.get(1.0, tk.END)
        if not text.strip():
            messagebox.showinfo("Thông báo", "Không có nội dung để lưu!")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Lưu tiêu đề"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(text)
                messagebox.showinfo("Thành công", f"Đã lưu tiêu đề vào file {file_path}")
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không thể lưu file: {str(e)}")
    
    def analyze_current(self):
        """Phân tích tiêu đề hiện tại"""
        text = self.results_text.get(1.0, tk.END).strip()
        if text:
            # Switch to analytics tab
            self.notebook.select(1)
            # Put the text in the analysis input
            self.analysis_input.delete(1.0, tk.END)
            self.analysis_input.insert(1.0, text)
            # Trigger analysis
            self.analyze_structure()
        else:
            messagebox.showinfo("Thông báo", "Không có tiêu đề để phân tích!")
    
    def analyze_structure(self):
        """Phân tích cấu trúc tiêu đề"""
        title = self.analysis_input.get(1.0, tk.END).strip()
        if not title:
            messagebox.showinfo("Thông báo", "Vui lòng nhập tiêu đề để phân tích!")
            return
            
        # Clear previous results
        self.analysis_results.delete(1.0, tk.END)
        
        # Split into titles if there are multiple
        titles = [t for t in title.split('\n') if t.strip()]
        
        for idx, title in enumerate(titles):
            if not title.strip():
                continue
                
            result = f"===== PHÂN TÍCH TIÊU ĐỀ {idx+1} =====\n\n"
            
            # Identify pattern type
            pattern_type = "Không xác định"
            if "?" in title:
                pattern_type = "Câu hỏi"
            elif title.startswith("+1 tips") or title.startswith("Tips") or title.startswith("Mẹo") or title.startswith("Cách") or any(x in title for x in ["bí kíp", "mẹo"]):
                pattern_type = "Mẹo/Tips"
            elif title.startswith("Đừng") or title.startswith("Tránh") or title.startswith("Cẩn thận") or "cẩn thận" in title.lower() or "nguy hiểm" in title.lower():
                pattern_type = "Cảnh báo"
            else:
                pattern_type = "Khẳng định"
                
            result += f"Loại tiêu đề: {pattern_type}\n"
            
            # Identify length
            words = len(title.split())
            if words < 7:
                length = "Ngắn"
            elif words < 12:
                length = "Trung bình"
            else:
                length = "Dài"
                
            result += f"Độ dài: {length} ({words} từ)\n"
            
            # Identify friendly words
            friendly_words = ["cả nhà", "mấy bà", "bà con", "mọi người", "các mom", "mí bà", "chị em", "các bạn", "quý dị"]
            found_friendly = []
            for word in friendly_words:
                if word in title.lower():
                    found_friendly.append(word)
                    
            if found_friendly:
                result += f"Từ thân mật: {', '.join(found_friendly)}\n"
            else:
                result += "Từ thân mật: Không có\n"
                
            # Identify emojis/emotions
            emoji_pattern = r'[=][)]+|:[\(\)v]|[\^\^]+|hihi'
            emojis = re.findall(emoji_pattern, title)
            if emojis:
                result += f"Emoji/cảm xúc: {', '.join(emojis)}\n"
            else:
                result += "Emoji/cảm xúc: Không có\n"
                
            # Identify hashtags
            hashtags = re.findall(r'#\w+', title)
            if hashtags:
                result += f"Hashtags: {', '.join(hashtags)}\n"
            else:
                result += "Hashtags: Không có\n"
                
            # Identify main topic
            topics = [
                "trẻ con", "gia đình", "mẹ chồng", "con cái", "phụ huynh", 
                "sức khỏe", "mất ngủ", "giảm cân", "vitamin", 
                "tâm lý", "tâm linh", "tư duy", "danh dự"
            ]
            
            found_topics = []
            for topic in topics:
                if topic in title.lower():
                    found_topics.append(topic)
                    
            if found_topics:
                result += f"Chủ đề chính: {', '.join(found_topics)}\n"
            else:
                result += "Chủ đề chính: Không xác định rõ\n"
                
            # Calculate a viral score
            viral_score = 50  # Base score
            
            # Pattern bonus
            if pattern_type == "Câu hỏi":
                viral_score += 10
            elif pattern_type == "Mẹo/Tips":
                viral_score += 8
            elif pattern_type == "Cảnh báo":
                viral_score += 5
                
            # Length adjustment
            if length == "Trung bình":
                viral_score += 10
            elif length == "Ngắn":
                viral_score += 5
                
            # Friendly words bonus
            viral_score += len(found_friendly) * 5
            
            # Emoji bonus
            viral_score += len(emojis) * 3
            
            # Hashtag bonus
            viral_score += len(hashtags) * 2
            
            # Topic bonus
            if any(x in ["gia đình", "trẻ con", "con cái", "phụ huynh"] for x in found_topics):
                viral_score += 15
            elif any(x in ["sức khỏe", "giảm cân", "vitamin"] for x in found_topics):
                viral_score += 10
            elif any(x in ["tâm lý", "tư duy"] for x in found_topics):
                viral_score += 8
                
            # Caps boost
            caps_words = sum(1 for word in title.split() if word.isupper())
            viral_score += caps_words * 2
            
            # Viral phrases boost
            viral_phrases = ["tin nóng", "sốc", "khẩn cấp", "bất ngờ", "hot", "gấp"]
            for phrase in viral_phrases:
                if phrase in title.lower():
                    viral_score += 5
                    
            # Cap the score at 100
            viral_score = min(100, viral_score)
            
            # Determine ranking
            if viral_score >= 85:
                ranking = "Xuất sắc"
            elif viral_score >= 70:
                ranking = "Tốt"
            elif viral_score >= 50:
                ranking = "Trung bình"
            else:
                ranking = "Cần cải thiện"
                
            result += f"\nViral Score: {viral_score}/100 ({ranking})\n\n"
            
            # Add to results
            self.analysis_results.insert(tk.END, result)
    
    def suggest_improvements(self):
        """Đề xuất cải thiện tiêu đề"""
        title = self.analysis_input.get(1.0, tk.END).strip()
        if not title:
            messagebox.showinfo("Thông báo", "Vui lòng nhập tiêu đề để phân tích!")
            return
            
        # Clear previous results
        self.analysis_results.delete(1.0, tk.END)
        
        # Split into titles if there are multiple
        titles = [t for t in title.split('\n') if t.strip()]
        
        for idx, title in enumerate(titles):
            if not title.strip():
                continue
                
            result = f"===== ĐỀ XUẤT CẢI THIỆN TIÊU ĐỀ {idx+1} =====\n\n"
            
            # Analyze the title
            pattern_type = "Không xác định"
            if "?" in title:
                pattern_type = "Câu hỏi"
            elif title.startswith("+1 tips") or title.startswith("Tips") or title.startswith("Mẹo") or title.startswith("Cách") or any(x in title for x in ["bí kíp", "mẹo"]):
                pattern_type = "Mẹo/Tips"
            elif title.startswith("Đừng") or title.startswith("Tránh") or title.startswith("Cẩn thận") or "cẩn thận" in title.lower() or "nguy hiểm" in title.lower():
                pattern_type = "Cảnh báo"
            else:
                pattern_type = "Khẳng định"
                
            # Identify length
            words = len(title.split())
            if words < 7:
                length = "Ngắn"
            elif words < 12:
                length = "Trung bình"
            else:
                length = "Dài"
                
            # Identify emojis/emotions
            emoji_pattern = r'[=][)]+|:[\(\)v]|[\^\^]+|hihi'
            emojis = re.findall(emoji_pattern, title)
            
            # Identify hashtags
            hashtags = re.findall(r'#\w+', title)
            
            # Suggest improvements
            suggestions = []
            
            # Suggest length adjustments
            if length == "Ngắn" and words < 5:
                suggestions.append("• Tiêu đề quá ngắn, hãy thêm chi tiết để tăng mức độ hấp dẫn")
            elif length == "Dài" and words > 15:
                suggestions.append("• Tiêu đề quá dài, hãy rút gọn để tăng khả năng ghi nhớ")
                
            # Pattern-specific suggestions
            if pattern_type == "Câu hỏi":
                if "?" not in title:
                    suggestions.append("• Thêm dấu '?' để nhấn mạnh câu hỏi")
                if not any(word in title.lower() for word in ["tại sao", "như thế nào", "có nên", "làm sao"]):
                    suggestions.append("• Bắt đầu bằng từ hỏi như 'tại sao', 'như thế nào', 'có nên', 'làm sao' để tăng tính tò mò")
            elif pattern_type == "Mẹo/Tips":
                if not any(word in title.lower() for word in ["cách", "mẹo", "tips", "bí kíp"]):
                    suggestions.append("• Bắt đầu bằng từ 'cách', 'mẹo', 'tips' để nhấn mạnh giá trị thực tế")
                if not any(word in title.lower() for word in ["hiệu quả", "đơn giản", "nhanh chóng", "dễ dàng"]):
                    suggestions.append("• Thêm từ mô tả hiệu quả như 'hiệu quả', 'đơn giản', 'nhanh chóng'")
            elif pattern_type == "Cảnh báo":
                if not any(word in title.lower() for word in ["đừng", "tránh", "cẩn thận", "nguy hiểm"]):
                    suggestions.append("• Bắt đầu bằng từ 'đừng', 'tránh', 'cẩn thận' để nhấn mạnh cảnh báo")
            else:  # Khẳng định
                if not any(word in title.lower() for word in ["luôn", "chắc chắn", "không thể", "duy nhất"]):
                    suggestions.append("• Thêm từ nhấn mạnh như 'luôn', 'chắc chắn', 'không thể' để tăng độ thuyết phục")
                
            # Emoji suggestions
            if not emojis:
                suggestions.append("• Thêm emoji để tăng cảm xúc và thu hút sự chú ý")
                
            # Hashtag suggestions
            if not hashtags:
                suggestions.append("• Thêm hashtag #shorts và hashtag liên quan đến chủ đề để tăng khả năng tiếp cận")
            elif "#shorts" not in title.lower():
                suggestions.append("• Thêm hashtag #shorts để tăng khả năng hiển thị trên YouTube Shorts")
                
            # Audience suggestions
            friendly_words = ["cả nhà", "mấy bà", "bà con", "mọi người", "các mom", "mí bà", "chị em", "các bạn"]
            if not any(word in title.lower() for word in friendly_words):
                suggestions.append("• Thêm từ thân mật như 'cả nhà', 'mấy bà', 'chị em' để tạo sự gần gũi")
                
            # If there are no suggestions, add a positive feedback
            if not suggestions:
                result += "Tiêu đề đã tối ưu và có khả năng viral cao! Không cần điều chỉnh thêm.\n\n"
            else:
                result += "Các đề xuất cải thiện:\n\n"
                result += "\n".join(suggestions) + "\n\n"
                
            # Provide improvement examples
            result += "Ví dụ cải thiện:\n\n"
            
            # Create improved versions
            improved_title = title
            
            # Apply some suggested improvements
            if pattern_type == "Câu hỏi" and "?" not in title:
                improved_title += "?"
                
            # Add emoji if missing
            if not emojis:
                improved_title += " " + random.choice(self.emoji_options)
                
            # Add hashtags if missing
            if not hashtags:
                improved_title += " #shorts"
                if pattern_type == "Mẹo/Tips":
                    improved_title += " #tips"
                    
            # Add friendly term if missing
            if not any(word in title.lower() for word in friendly_words):
                audience = random.choice(self.replacements["audience"])
                if pattern_type == "Câu hỏi":
                    improved_title = f"{audience} ơi, {improved_title}"
                else:
                    improved_title = f"{improved_title} {audience}"
                    
            result += f"• {improved_title}\n\n"
            
            # Add to results
            self.analysis_results.insert(tk.END, result)
    
    def viral_score(self):
        """Tính điểm viral cho tiêu đề"""
        title = self.analysis_input.get(1.0, tk.END).strip()
        if not title:
            messagebox.showinfo("Thông báo", "Vui lòng nhập tiêu đề để phân tích!")
            return
            
        # Clear previous results
        self.analysis_results.delete(1.0, tk.END)
        
        # Split into titles if there are multiple
        titles = [t for t in title.split('\n') if t.strip()]
        
        all_scores = []
        
        for idx, title in enumerate(titles):
            if not title.strip():
                continue
                
            result = f"===== VIRAL SCORE - TIÊU ĐỀ {idx+1} =====\n\n"
            
            # Identify pattern type
            pattern_type = "Không xác định"
            if "?" in title:
                pattern_type = "Câu hỏi"
            elif title.startswith("+1 tips") or title.startswith("Tips") or title.startswith("Mẹo") or title.startswith("Cách") or any(x in title for x in ["bí kíp", "mẹo"]):
                pattern_type = "Mẹo/Tips"
            elif title.startswith("Đừng") or title.startswith("Tránh") or title.startswith("Cẩn thận") or "cẩn thận" in title.lower() or "nguy hiểm" in title.lower():
                pattern_type = "Cảnh báo"
            else:
                pattern_type = "Khẳng định"
                
            # Identify length
            words = len(title.split())
            if words < 7:
                length = "Ngắn"
            elif words < 12:
                length = "Trung bình"
            else:
                length = "Dài"
                
            # Identify friendly words
            friendly_words = ["cả nhà", "mấy bà", "bà con", "mọi người", "các mom", "mí bà", "chị em", "các bạn", "quý dị"]
            found_friendly = [word for word in friendly_words if word in title.lower()]
                
            # Identify emojis/emotions
            emoji_pattern = r'[=][)]+|:[\(\)v]|[\^\^]+|hihi'
            emojis = re.findall(emoji_pattern, title)
                
            # Identify hashtags
            hashtags = re.findall(r'#\w+', title)
                
            # Identify main topic
            topics = [
                "trẻ con", "gia đình", "mẹ chồng", "con cái", "phụ huynh", 
                "sức khỏe", "mất ngủ", "giảm cân", "vitamin", 
                "tâm lý", "tâm linh", "tư duy", "danh dự"
            ]
            
            found_topics = [topic for topic in topics if topic in title.lower()]
                
            # Calculate a viral score
            viral_score = 50  # Base score
            score_breakdown = []
            
            # Pattern bonus
            pattern_bonus = 0
            if pattern_type == "Câu hỏi":
                pattern_bonus = 10
            elif pattern_type == "Mẹo/Tips":
                pattern_bonus = 8
            elif pattern_type == "Cảnh báo":
                pattern_bonus = 5
                
            viral_score += pattern_bonus
            score_breakdown.append(f"Cấu trúc {pattern_type}: +{pattern_bonus}")
                
            # Length adjustment
            length_bonus = 0
            if length == "Trung bình":
                length_bonus = 10
            elif length == "Ngắn":
                length_bonus = 5
                
            viral_score += length_bonus
            score_breakdown.append(f"Độ dài {length} ({words} từ): +{length_bonus}")
                
            # Friendly words bonus
            friendly_bonus = len(found_friendly) * 5
            viral_score += friendly_bonus
            if friendly_bonus > 0:
                score_breakdown.append(f"Từ thân mật ({', '.join(found_friendly)}): +{friendly_bonus}")
            else:
                score_breakdown.append("Từ thân mật: +0")
                
            # Emoji bonus
            emoji_bonus = len(emojis) * 3
            viral_score += emoji_bonus
            if emoji_bonus > 0:
                score_breakdown.append(f"Emoji/cảm xúc ({', '.join(emojis)}): +{emoji_bonus}")
            else:
                score_breakdown.append("Emoji/cảm xúc: +0")
                
            # Hashtag bonus
            hashtag_bonus = len(hashtags) * 2
            viral_score += hashtag_bonus
            if hashtag_bonus > 0:
                score_breakdown.append(f"Hashtags ({', '.join(hashtags)}): +{hashtag_bonus}")
            else:
                score_breakdown.append("Hashtags: +0")
                
            # Topic bonus
            topic_bonus = 0
            if any(x in ["gia đình", "trẻ con", "con cái", "phụ huynh"] for x in found_topics):
                topic_bonus += 15
            elif any(x in ["sức khỏe", "giảm cân", "vitamin"] for x in found_topics):
                topic_bonus += 10
            elif any(x in ["tâm lý", "tư duy"] for x in found_topics):
                topic_bonus += 8
                
            viral_score += topic_bonus
            if topic_bonus > 0 and found_topics:
                score_breakdown.append(f"Chủ đề hot ({', '.join(found_topics)}): +{topic_bonus}")
            else:
                score_breakdown.append("Chủ đề hot: +0")
                # Caps boost
            caps_words = sum(1 for word in title.split() if word.isupper())
            caps_bonus = caps_words * 2
            viral_score += caps_bonus
            if caps_bonus > 0:
                score_breakdown.append(f"Từ in hoa ({caps_words} từ): +{caps_bonus}")
                
            # Viral phrases boost
            viral_phrases = ["tin nóng", "sốc", "khẩn cấp", "bất ngờ", "hot", "gấp"]
            found_phrases = [phrase for phrase in viral_phrases if phrase in title.lower()]
            viral_phrase_bonus = len(found_phrases) * 5
            viral_score += viral_phrase_bonus
            if viral_phrase_bonus > 0:
                score_breakdown.append(f"Từ khóa viral ({', '.join(found_phrases)}): +{viral_phrase_bonus}")
                
            # Cap the score at 100
            viral_score = min(100, viral_score)
            
            # Determine ranking
            if viral_score >= 85:
                ranking = "Xuất sắc"
            elif viral_score >= 70:
                ranking = "Tốt"
            elif viral_score >= 50:
                ranking = "Trung bình"
            else:
                ranking = "Cần cải thiện"
                
            result += f"Tiêu đề: {title}\n\n"
            result += "Chi tiết điểm:\n"
            result += "\n".join(score_breakdown)
            result += f"\n\nTổng điểm: {viral_score}/100 ({ranking})\n\n"
            
            # Add prediction of performance
            result += "Dự đoán hiệu suất:\n"
            
            if viral_score >= 85:
                result += "• Khả năng viral cao\n"
                result += "• Tỷ lệ CTR dự kiến: >8%\n"
                result += "• Tiềm năng đạt >500K views\n"
            elif viral_score >= 70:
                result += "• Hiệu suất tốt\n"
                result += "• Tỷ lệ CTR dự kiến: 5-8%\n"
                result += "• Tiềm năng đạt 100K-500K views\n"
            elif viral_score >= 50:
                result += "• Hiệu suất trung bình\n"
                result += "• Tỷ lệ CTR dự kiến: 3-5%\n"
                result += "• Tiềm năng đạt 50K-100K views\n"
            else:
                result += "• Hiệu suất thấp\n"
                result += "• Tỷ lệ CTR dự kiến: <3%\n"
                result += "• Tiềm năng đạt <50K views\n"
            
            result += "\n" + "-"*40 + "\n\n"
            
            # Add to results
            self.analysis_results.insert(tk.END, result)
            
            # Save the score for ranking
            all_scores.append((title, viral_score))
        
        # If there are multiple titles, show ranking
        if len(all_scores) > 1:
            ranking_result = "===== XẾP HẠNG TIÊU ĐỀ =====\n\n"
            
            # Sort by score
            all_scores.sort(key=lambda x: x[1], reverse=True)
            
            for i, (title, score) in enumerate(all_scores):
                ranking_result += f"{i+1}. [{score}/100] {title}\n"
                
            self.analysis_results.insert(tk.END, ranking_result)
    
    def generate_batch(self):
        """Tạo hàng loạt tiêu đề"""
        try:
            # Get settings
            count = self.batch_count.get()
            topics_text = self.batch_topics.get(1.0, tk.END)
            pattern_type = self.batch_pattern_var.get()
            
            # Parse topics
            topics = [t.strip() for t in topics_text.split('\n') if t.strip()]
            if not topics:
                topics = ["gia đình", "sức khỏe", "tâm lý", "mẹo hàng ngày"]
            
            # Clear results
            self.batch_results.delete(1.0, tk.END)
            
            # Track used patterns to ensure diversity
            used_patterns = set()
            
            for i in range(count):
                # Select a topic
                topic = random.choice(topics)
                
                # Determine pattern to use
                if pattern_type == "question":
                    pattern_key = "question"
                elif pattern_type == "tips":
                    pattern_key = "tips"
                else:  # mix
                    pattern_options = ["question", "statement", "tips", "warning"]
                    pattern_key = random.choice(pattern_options)
                
                # Select a pattern, ensuring diversity if enabled
                if self.batch_diverse_var.get() and len(self.patterns[pattern_key]) > len(used_patterns):
                    available_patterns = [p for p in self.patterns[pattern_key] if p not in used_patterns]
                    if available_patterns:
                        pattern = random.choice(available_patterns)
                        used_patterns.add(pattern)
                    else:
                        pattern = random.choice(self.patterns[pattern_key])
                        used_patterns.clear()  # Reset used patterns
                        used_patterns.add(pattern)
                else:
                    pattern = random.choice(self.patterns[pattern_key])
                
                # Build the title
                title = pattern
                
                # Replace {topic} with actual topic
                title = title.replace("{topic}", topic)
                
                # Replace other placeholders
                for key in self.replacements:
                    if key != "topic" and "{" + key + "}" in title:
                        replacement = random.choice(self.replacements[key])
                        title = title.replace("{" + key + "}", replacement)
                
                # Add friendly term if selected
                if self.batch_friendly_var.get() and random.random() > 0.5:
                    audience = random.choice(self.replacements["audience"])
                    if ", " in title:
                        parts = title.split(", ")
                        title = f"{parts[0]}, {audience}, {', '.join(parts[1:])}"
                    elif random.random() > 0.5:
                        title = f"{audience} ơi, {title}"
                    else:
                        title = f"{title} {audience}"
                
                # Add emoji if selected
                if self.batch_emoji_var.get():
                    emoji = random.choice(self.emoji_options)
                    if random.random() > 0.7:
                        # Insert emoji in the middle
                        words = title.split()
                        if len(words) > 3:
                            insert_pos = random.randint(1, len(words) - 2)
                            words.insert(insert_pos, emoji)
                            title = " ".join(words)
                    else:
                        # Add emoji at the end
                        title = f"{title} {emoji}"
                
                # Add hashtags if selected
                if self.batch_hashtag_var.get():
                    hashtags = []
                    # Always add core hashtags
                    hashtags.extend(self.hashtags["core"])
                    
                    # Add relevant hashtag based on topic
                    if "sức khỏe" in topic.lower() or "vitamin" in topic.lower():
                        hashtags.append(random.choice(self.hashtags["health"]))
                    elif "gia đình" in topic.lower() or "con cái" in topic.lower() or "trẻ con" in topic.lower():
                        hashtags.append(random.choice(self.hashtags["family"]))
                    else:
                        # Add a random hashtag
                        hashtag_category = random.choice(list(self.hashtags.keys()))
                        hashtags.append(random.choice(self.hashtags[hashtag_category]))
                    
                    # Randomly add trending hashtag
                    if random.random() > 0.6:
                        hashtags.append(random.choice(self.hashtags["trend"]))
                    
                    # Randomly decide number of hashtags to include
                    max_hashtags = random.randint(1, 3)
                    hashtags = hashtags[:max_hashtags]
                    
                    # Add hashtags to title
                    title = f"{title} {' '.join(hashtags)}"
                
                # Add to results with numbering
                self.batch_results.insert(tk.END, f"{i+1}. {title}\n\n")
            
            messagebox.showinfo("Thành công", f"Đã tạo {count} tiêu đề!")
            
        except Exception as e:
            messagebox.showerror("Lỗi", f"Có lỗi xảy ra: {str(e)}")
    
    def export_batch(self):
        """Xuất kết quả batch ra file"""
        text = self.batch_results.get(1.0, tk.END)
        if not text.strip():
            messagebox.showinfo("Thông báo", "Không có nội dung để xuất!")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")],
            title="Xuất tiêu đề hàng loạt"
        )
        
        if file_path:
            try:
                # Clean up the text to remove numbering and extra newlines
                titles = []
                for line in text.split("\n"):
                    line = line.strip()
                    if line and not line.isspace():
                        # Remove the numbering (e.g., "1. ")
                        if re.match(r"^\d+\.\s", line):
                            line = re.sub(r"^\d+\.\s", "", line)
                        titles.append(line)
                
                # If it's a CSV file, format accordingly
                if file_path.endswith(".csv"):
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
                    with open(file_path, 'w', encoding='utf-8') as file:
                        file.write("STT,Tiêu đề,Ngày tạo\n")
                        for i, title in enumerate(titles):
                            if title.strip():  # Skip empty lines
                                file.write(f"{i+1},\"{title}\",{timestamp}\n")
                else:
                    # Plain text file
                    with open(file_path, 'w', encoding='utf-8') as file:
                        for i, title in enumerate(titles):
                            if title.strip():  # Skip empty lines
                                file.write(f"{i+1}. {title}\n")
                
                messagebox.showinfo("Thành công", f"Đã xuất tiêu đề vào file {file_path}")
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không thể xuất file: {str(e)}")
    
    def clear_batch(self):
        """Xóa kết quả batch"""
        self.batch_results.delete(1.0, tk.END)


def main():
    root = tk.Tk()
    app = ViralTitleGenerator(root)
    root.mainloop()


if __name__ == "__main__":
    main()