"""
Build script for Midjourney Prompt Generator
Creates an executable file with all dependencies included
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def clean_build_dirs():
    """Clean previous build directories"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"Cleaning {dir_name}...")
            shutil.rmtree(dir_name)
            
def build_exe():
    """Build the executable using PyInstaller"""
    print("Building Midjourney Prompt Generator executable...")
    
    # Clean previous builds
    clean_build_dirs()
    
    # Build command
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=MidjourneyPromptGenerator',
        '--add-data=demo_script.txt;.',
        '--add-data=README_midjourney_prompt_generator.md;.',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.scrolledtext',
        '--hidden-import=openai',
        '--hidden-import=requests',
        '--hidden-import=json',
        '--hidden-import=threading',
        '--hidden-import=datetime',
        '--hidden-import=dotenv',
        'run_midjourney_prompt_generator.py'
    ]
    
    try:
        # Run PyInstaller
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Build successful!")
        print(f"Executable created: dist/MidjourneyPromptGenerator.exe")
        
        # Copy additional files to dist folder
        dist_dir = Path('dist')
        if dist_dir.exists():
            files_to_copy = [
                'demo_script.txt',
                'README_midjourney_prompt_generator.md'
            ]
            
            for file_name in files_to_copy:
                if os.path.exists(file_name):
                    shutil.copy2(file_name, dist_dir / file_name)
                    print(f"Copied {file_name} to dist folder")
                    
        print("\nBuild completed successfully!")
        print("Files in dist folder:")
        for item in dist_dir.iterdir():
            print(f"  - {item.name}")
            
    except subprocess.CalledProcessError as e:
        print(f"Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False
        
    except Exception as e:
        print(f"Unexpected error: {e}")
        return False
        
    return True

def create_portable_package():
    """Create a portable package with all necessary files"""
    print("\nCreating portable package...")
    
    package_dir = Path('MidjourneyPromptGenerator_Portable')
    
    # Create package directory
    if package_dir.exists():
        shutil.rmtree(package_dir)
    package_dir.mkdir()
    
    # Copy executable
    exe_file = Path('dist/MidjourneyPromptGenerator.exe')
    if exe_file.exists():
        shutil.copy2(exe_file, package_dir / 'MidjourneyPromptGenerator.exe')
        print("Copied executable")
    
    # Copy documentation and demo files
    files_to_copy = [
        'README_midjourney_prompt_generator.md',
        'demo_script.txt'
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, package_dir / file_name)
            print(f"Copied {file_name}")
    
    # Create a sample settings file
    sample_settings = {
        "openai_api_key": "",
        "midjourney_api_key": "",
        "openai_base_url": "https://api.openai.com/v1",
        "midjourney_base_url": "https://api.midjourney.com/v1",
        "prompts_dir": "",
        "images_dir": ""
    }
    
    import json
    with open(package_dir / 'sample_settings.json', 'w', encoding='utf-8') as f:
        json.dump(sample_settings, f, indent=4)
    print("Created sample_settings.json")
    
    # Create usage instructions
    instructions = """# Midjourney Prompt Generator - Portable Version

## Quick Start:
1. Run MidjourneyPromptGenerator.exe
2. Go to Setup tab and enter your OpenAI API key
3. Set your prompts and images directories
4. Click "Save Settings"
5. Go to Script tab and load demo_script.txt or enter your own script
6. Click "Analyze Script" to start

## Settings:
- Settings are automatically saved to your user AppData folder
- On Windows: %APPDATA%\\MidjourneyPromptGenerator\\settings.json
- You can also use the sample_settings.json as a template

## Note:
- Midjourney doesn't have a public API
- Use DALL-E 3 integration for actual image generation
- Or copy prompts to use manually in Discord

## Files included:
- MidjourneyPromptGenerator.exe - Main application
- demo_script.txt - Sample script for testing
- README_midjourney_prompt_generator.md - Full documentation
- sample_settings.json - Settings template
- INSTRUCTIONS.txt - This file
"""
    
    with open(package_dir / 'INSTRUCTIONS.txt', 'w', encoding='utf-8') as f:
        f.write(instructions)
    print("Created INSTRUCTIONS.txt")
    
    print(f"\nPortable package created: {package_dir}")
    print("Package contents:")
    for item in package_dir.iterdir():
        print(f"  - {item.name}")

def main():
    """Main build function"""
    print("=" * 50)
    print("Midjourney Prompt Generator Build Script")
    print("=" * 50)
    
    # Check if required files exist
    required_files = [
        'run_midjourney_prompt_generator.py',
        'midjourney_prompt_generator_app.py',
        'midjourney_prompt_generator.py'
    ]
    
    missing_files = []
    for file_name in required_files:
        if not os.path.exists(file_name):
            missing_files.append(file_name)
    
    if missing_files:
        print("Error: Missing required files:")
        for file_name in missing_files:
            print(f"  - {file_name}")
        return False
    
    # Build executable
    if build_exe():
        create_portable_package()
        print("\n" + "=" * 50)
        print("Build completed successfully!")
        print("You can find the executable in the 'dist' folder")
        print("And the portable package in 'MidjourneyPromptGenerator_Portable' folder")
        print("=" * 50)
        return True
    else:
        print("Build failed!")
        return False

if __name__ == "__main__":
    main()
