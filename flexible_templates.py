"""
Flexible Templates for Boring History Generator
Customizable prompt templates with adjustable parameters
"""

def get_title_generation_prompt(topic, num_titles=5, max_hook_words=14, include_runtime=True, 
                               tone="mildly tongue‑in‑cheek", focus_regions=None, custom_prefixes=None):
    """
    Generate customizable title generation prompt
    
    Args:
        topic (str): Main topic for the video
        num_titles (int): Number of titles to generate (default: 5)
        max_hook_words (int): Maximum words in hook (default: 14)
        include_runtime (bool): Whether to include runtime tags (default: True)
        tone (str): Tone style (default: "mildly tongue‑in‑cheek")
        focus_regions (list): Specific regions to focus on (optional)
        custom_prefixes (list): Custom title prefixes (optional)
    """
    
    if custom_prefixes:
        prefixes = custom_prefixes
    else:
        prefixes = [
            '"Boring History For Sleep | "',
            '"The Boring History For Sleep | "',
            '"Boring Greek Myths For Sleep | " (use sparingly for myth‑centric episodes)',
            '"History Podcast For Sleep | " (use sparingly for food/ daily‑life angles)'
        ]
    
    runtime_instruction = ""
    if include_runtime:
        runtime_instruction = 'When relevant, append runtime tags exactly as the audience expects, e.g. "(2 HOURS)", "(4 HOURS)", or "Gentle Storytelling & Ambient Sounds (2 HOURS)".'
    
    regions_instruction = ""
    if focus_regions:
        regions_instruction = f"Focus particularly on these regions: {', '.join(focus_regions)}."
    
    return f"""
Role & Goal
Act as an experienced YouTube content strategist. Your sole task is to brainstorm fresh, clickable video‑topic ideas that match this style: slow‑paced, gently humorous historical storytelling designed to help viewers relax or fall asleep.

Output
Provide {num_titles} unique video titles in a numbered list.
Each title must start with one of these prefixes (rotate them naturally):
{chr(10).join(prefixes)}

After the pipe (|) give a concise, curiosity‑driven hook (≤ {max_hook_words} words) teasing an obscure fact, vivid scenario, or surprising question.
{runtime_instruction}

Content Criteria
Variety – Cover multiple eras (Ancient, Medieval, Early‑Modern, 19‑20th cent.), regions (Africa, Asia, Europe, Americas, Oceania), and themes (medicine, food, daily routines, punishments, gender roles, famous biographies, odd laws, forgotten inventions, lost cities, mythical creatures, etc.).
{regions_instruction}
Tone – Titles should sound {tone} but not sensational; they must promise oddly soothing facts, gentle humor, and immersive "you‑are‑there" detail—never modern moral outrage or click‑bait shock.
Inclusivity & Freshness – At least one‑third of the list should highlight lesser‑known cultures or under‑represented figures.
Sleep‑Friendly – Avoid harsh words in the title ("horrific torture" → "peculiar punishments").
Keyword Logic – Work one or two SEO‑friendly nouns into each title ("silk routes," "Aztec gardens," "Victorian insomnia cures") while keeping the phrasing natural.

Topic: {topic}

Generate the list now.
"""


def get_script_generation_prompt(topic, total_words=15000, num_sections=15, words_per_section=1000, 
                                style="relaxed YouTube host + gentle sarcasm + sleepy ASMR cadence",
                                include_intro_template=True, include_wind_down=True):
    """
    Generate customizable script generation prompt
    
    Args:
        topic (str): Main topic for the script
        total_words (int): Total word count (default: 15000)
        num_sections (int): Number of sections (default: 15)
        words_per_section (int): Words per section (default: 1000)
        style (str): Narration style description
        include_intro_template (bool): Include intro template (default: True)
        include_wind_down (bool): Include wind-down section (default: True)
    """
    
    intro_section = ""
    if include_intro_template:
        intro_section = """
Introduction Template (150‑200 words, counts inside Section 1)  
Open exactly with:
Hey guys, tonight we …  
Hook the listener with vivid present‑tense imagery related to the topic.  
Drop one cheeky "you probably won't survive this"‑style reality check.  
Include this CTA verbatim:  
So, before you get comfortable, take a moment to like the video and subscribe—but only if you genuinely enjoy what I do here.  
Invite viewers to post their location & local time.  
Close with this sign‑off verbatim:  
Now, dim the lights, maybe turn on a fan for that soft background hum, and let's ease into tonight's journey together.  
Flow straight into the story—no extra headings.
"""
    
    wind_down_section = ""
    if include_wind_down:
        wind_down_section = f"""
{words_per_section//5}-Word Wind-Down (end of Section {num_sections})  
• Slow the pacing, soften vocabulary, lengthen sentences.  
• Reassure the listener, fade the final imagery, and close on a calming whisper.
"""
    
    return f"""
Role
You are my single‑speaker "Bed‑Time History" scriptwriter.

Global Specs  
One continuous, second‑person narrative on {topic}.  
Total length {total_words:,}–{total_words + 1500:,} words. 
Deliver in {num_sections} numbered sections, {words_per_section}–{words_per_section + 100} words EACH.  
No fresh intros between sections—narration must glide forward.

Two-Step Workflow  
Step 1 – {num_sections}-Bullet Outline  
Reply first with EXACTLY {num_sections} bullets.  
Bullet n = Section n.  
≤ 8‑word mini‑title + one‑sentence teaser of what that section will cover.  

STOP. Wait for my "CONTINUE".

Step 2 – Section Delivery  
For every "CONTINUE" command:  
Look at the corresponding outline bullet again.  
Expand precisely that content—no drifting to later bullets.  
Write {words_per_section}–{words_per_section + 100} words of seamless narration.  
Start with "Section n" heading, NO subtitle.  
End with:  

[Word count: ####]  
>>> Awaiting "CONTINUE"  
After Section {num_sections}, add the {words_per_section//5}‑word wind‑down (see below) and finish with  
>>> End of script. Sweet dreams.

If you ever stray from the bullet‑plan, self‑correct before sending.
{intro_section}
Narration Style Rules  
✓ Second‑person present ("you trudge …").  
✓ Voice = {style}.  
✓ Blend sensory detail, modern asides, and 3 light jokes per ±{words_per_section} words.  
✓ Each section must contain:  
   – 1 mainstream historical fact  
   – 1 quirky or fringe tidbit  
   – 1 open scholarly debate phrase ("Historians still argue whether …").  
✓ Keep PG‑13; avoid explicit gore/profanity.  
✓ No citations or URLs; weave facts naturally.  
✓ Absolutely **no** new section intros like "In this chapter…"—just continue.

Structure & Continuity 
• Section headings = "Section 1", "Section 2", etc.  
• Use callbacks ("remember that crocodile‑dung sunscreen?") for cohesion.  
• Do NOT re‑introduce the topic at each section break.  
• Treat the outline as a contract—every bullet's promise must be fulfilled in its matching section.
{wind_down_section}
BEGIN NOW with STEP 1—the {num_sections}‑bullet outline ONLY.
"""


def get_image_generation_prompt(script_section, num_images=1, art_style="late‑15th‑century illuminated‑manuscript miniature",
                               medium="tempera & shell‑gold on vellum", perspective="flat medieval perspective",
                               aspect_ratio="16:9", resolution="ultra‑high‑resolution (4K)",
                               color_palette="archaic clothing colors (indigo, russet, saffron, teal)"):
    """
    Generate customizable image generation prompt
    
    Args:
        script_section (str): Script section to generate images for
        num_images (int): Number of images to generate (default: 1)
        art_style (str): Art style description
        medium (str): Art medium description
        perspective (str): Perspective style
        aspect_ratio (str): Image aspect ratio
        resolution (str): Image resolution
        color_palette (str): Color palette description
    """
    
    return f"""
You are a scene‑selector and artistic prompt‑writer.

Goal
Generate exactly {num_images} highly‑detailed AI image generation prompts.
Each prompt must depict a different, visually distinct moment or setting from the script extract supplied below.
All prompts must share the same art‑direction: {art_style} ({medium}), {perspective}, limited depth cues, subtle parchment texture, delicate brown‑ink outlines, tiny gilded flourishes, no modern objects or techniques.

Method
Read the script section.
Identify the most picturable beats (places, actions, or moods) that occur in chronological order.
Write one prompt per beat. Keep them varied—interiors vs. exteriors, day vs. night, different characters, seasons, or activities—so the final sequence feels like a visual storybook of the text.

Structure each prompt like this:
Prompt X: "Core scene description, key characters & actions, setting, mood, color accents — {art_style}, {medium}, {perspective}, fine brown‑ink outlines, subtle parchment texture, {aspect_ratio} aspect, {resolution}"

Tips:
Use {color_palette}.
Mention small medieval props (wicker baskets, wooden buckets, iron fire‑tongs) when relevant.
If weather matters (snow, rain, harvest sun), weave it in.
Keep to one sentence per prompt; avoid camera jargon beyond "straight‑on" or "bird's‑eye."

Output Format
List each prompt on its own line starting with "Prompt 1: …", "Prompt 2: …", up to "{num_images}".
Do not add commentary, explanations, scene titles, or blank lines—only the prompts.

Script Section:
{script_section}
"""


def get_thumbnail_generation_prompt(character_action, style="crisp vector‑cartoon thumbnail art",
                                   background="pure white background", aspect_ratio="9:16",
                                   resolution="ultra‑high‑resolution (4K)", 
                                   clothing_style="historically accurate medieval clothing"):
    """
    Generate customizable thumbnail generation prompt
    
    Args:
        character_action (str): Description of character and action
        style (str): Art style description
        background (str): Background description
        aspect_ratio (str): Image aspect ratio
        resolution (str): Image resolution
        clothing_style (str): Clothing style description
    """
    
    return f"""
You are a specialist prompt‑writer for {style}.
Produce one concise image prompt (and nothing else) that tells the model to render a modern, high‑contrast digital illustration in the style of the sample thumbnails (bold black outlines, flat comic‑book colours, minimal shading).

Style requirements
• Close‑up or waist‑up characters only (fill the frame; no scenery)
• {background}, no drop shadows
• Strong facial expressions (anger, terror, glee, etc.)
• Clean vector lines, solid colour fills, limited palette per figure
• {clothing_style} and accessories appropriate to each character's role (tunics, wimples, leather aprons, linen caps, etc.)
• {aspect_ratio} aspect, {resolution}

Depict exactly the character(s) and actions supplied below:
{character_action}

Return only the finished image prompt, nothing else.
"""


# Template configuration class
class TemplateConfig:
    """Configuration class for template parameters"""
    
    def __init__(self):
        # Title generation settings
        self.title_num_titles = 5
        self.title_max_hook_words = 14
        self.title_include_runtime = True
        self.title_tone = "mildly tongue‑in‑cheek"
        self.title_focus_regions = None
        self.title_custom_prefixes = None
        
        # Script generation settings
        self.script_total_words = 15000
        self.script_num_sections = 15
        self.script_words_per_section = 1000
        self.script_style = "relaxed YouTube host + gentle sarcasm + sleepy ASMR cadence"
        self.script_include_intro = True
        self.script_include_wind_down = True
        
        # Image generation settings
        self.image_art_style = "late‑15th‑century illuminated‑manuscript miniature"
        self.image_medium = "tempera & shell‑gold on vellum"
        self.image_perspective = "flat medieval perspective"
        self.image_aspect_ratio = "16:9"
        self.image_resolution = "ultra‑high‑resolution (4K)"
        self.image_color_palette = "archaic clothing colors (indigo, russet, saffron, teal)"
        
        # Thumbnail generation settings
        self.thumbnail_style = "crisp vector‑cartoon thumbnail art"
        self.thumbnail_background = "pure white background"
        self.thumbnail_aspect_ratio = "9:16"
        self.thumbnail_resolution = "ultra‑high‑resolution (4K)"
        self.thumbnail_clothing_style = "historically accurate medieval clothing"
    
    def get_title_prompt(self, topic):
        """Get title generation prompt with current settings"""
        return get_title_generation_prompt(
            topic=topic,
            num_titles=self.title_num_titles,
            max_hook_words=self.title_max_hook_words,
            include_runtime=self.title_include_runtime,
            tone=self.title_tone,
            focus_regions=self.title_focus_regions,
            custom_prefixes=self.title_custom_prefixes
        )
    
    def get_script_prompt(self, topic):
        """Get script generation prompt with current settings"""
        return get_script_generation_prompt(
            topic=topic,
            total_words=self.script_total_words,
            num_sections=self.script_num_sections,
            words_per_section=self.script_words_per_section,
            style=self.script_style,
            include_intro_template=self.script_include_intro,
            include_wind_down=self.script_include_wind_down
        )
    
    def get_image_prompt(self, script_section, num_images=1):
        """Get image generation prompt with current settings"""
        return get_image_generation_prompt(
            script_section=script_section,
            num_images=num_images,
            art_style=self.image_art_style,
            medium=self.image_medium,
            perspective=self.image_perspective,
            aspect_ratio=self.image_aspect_ratio,
            resolution=self.image_resolution,
            color_palette=self.image_color_palette
        )
    
    def get_thumbnail_prompt(self, character_action):
        """Get thumbnail generation prompt with current settings"""
        return get_thumbnail_generation_prompt(
            character_action=character_action,
            style=self.thumbnail_style,
            background=self.thumbnail_background,
            aspect_ratio=self.thumbnail_aspect_ratio,
            resolution=self.thumbnail_resolution,
            clothing_style=self.thumbnail_clothing_style
        )
    
    def save_to_dict(self):
        """Save configuration to dictionary"""
        return {
            'title': {
                'num_titles': self.title_num_titles,
                'max_hook_words': self.title_max_hook_words,
                'include_runtime': self.title_include_runtime,
                'tone': self.title_tone,
                'focus_regions': self.title_focus_regions,
                'custom_prefixes': self.title_custom_prefixes
            },
            'script': {
                'total_words': self.script_total_words,
                'num_sections': self.script_num_sections,
                'words_per_section': self.script_words_per_section,
                'style': self.script_style,
                'include_intro': self.script_include_intro,
                'include_wind_down': self.script_include_wind_down
            },
            'image': {
                'art_style': self.image_art_style,
                'medium': self.image_medium,
                'perspective': self.image_perspective,
                'aspect_ratio': self.image_aspect_ratio,
                'resolution': self.image_resolution,
                'color_palette': self.image_color_palette
            },
            'thumbnail': {
                'style': self.thumbnail_style,
                'background': self.thumbnail_background,
                'aspect_ratio': self.thumbnail_aspect_ratio,
                'resolution': self.thumbnail_resolution,
                'clothing_style': self.thumbnail_clothing_style
            }
        }
    
    def load_from_dict(self, config_dict):
        """Load configuration from dictionary"""
        if 'title' in config_dict:
            title_config = config_dict['title']
            self.title_num_titles = title_config.get('num_titles', self.title_num_titles)
            self.title_max_hook_words = title_config.get('max_hook_words', self.title_max_hook_words)
            self.title_include_runtime = title_config.get('include_runtime', self.title_include_runtime)
            self.title_tone = title_config.get('tone', self.title_tone)
            self.title_focus_regions = title_config.get('focus_regions', self.title_focus_regions)
            self.title_custom_prefixes = title_config.get('custom_prefixes', self.title_custom_prefixes)
        
        if 'script' in config_dict:
            script_config = config_dict['script']
            self.script_total_words = script_config.get('total_words', self.script_total_words)
            self.script_num_sections = script_config.get('num_sections', self.script_num_sections)
            self.script_words_per_section = script_config.get('words_per_section', self.script_words_per_section)
            self.script_style = script_config.get('style', self.script_style)
            self.script_include_intro = script_config.get('include_intro', self.script_include_intro)
            self.script_include_wind_down = script_config.get('include_wind_down', self.script_include_wind_down)
        
        if 'image' in config_dict:
            image_config = config_dict['image']
            self.image_art_style = image_config.get('art_style', self.image_art_style)
            self.image_medium = image_config.get('medium', self.image_medium)
            self.image_perspective = image_config.get('perspective', self.image_perspective)
            self.image_aspect_ratio = image_config.get('aspect_ratio', self.image_aspect_ratio)
            self.image_resolution = image_config.get('resolution', self.image_resolution)
            self.image_color_palette = image_config.get('color_palette', self.image_color_palette)
        
        if 'thumbnail' in config_dict:
            thumbnail_config = config_dict['thumbnail']
            self.thumbnail_style = thumbnail_config.get('style', self.thumbnail_style)
            self.thumbnail_background = thumbnail_config.get('background', self.thumbnail_background)
            self.thumbnail_aspect_ratio = thumbnail_config.get('aspect_ratio', self.thumbnail_aspect_ratio)
            self.thumbnail_resolution = thumbnail_config.get('resolution', self.thumbnail_resolution)
            self.thumbnail_clothing_style = thumbnail_config.get('clothing_style', self.thumbnail_clothing_style)
