"""
Video Composer for Channel Content Repurposer

This module handles video composition and editing:
- Merging audio with background video
- Adding subtitles to video
- Creating transitions and effects
- Exporting final videos
"""

import os
import json
import time
import tempfile
from enum import Enum


class VideoComposer:
    """Main class for video composition and editing"""

    def __init__(self):
        """Initialize the Video Composer"""
        # Try to import required packages
        try:
            import moviepy.editor as mp
            self.mp = mp
            self.moviepy_available = True
        except ImportError:
            self.moviepy_available = False
            print("MoviePy not available. Install with: pip install moviepy")

    def merge_audio_with_video(self, audio_file, video_file, output_file,
                              loop_video=True, add_fade=True, volume=1.0):
        """
        Merge audio with video

        Args:
            audio_file (str): Path to the audio file
            video_file (str): Path to the video file
            output_file (str): Path to save the output video
            loop_video (bool, optional): Whether to loop the video if audio is longer
            add_fade (bool, optional): Whether to add fade in/out effects
            volume (float, optional): Audio volume multiplier

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.moviepy_available:
            print("MoviePy not available. Install with: pip install moviepy")
            return False

        if not os.path.exists(audio_file):
            print(f"Audio file not found: {audio_file}")
            return False

        if not os.path.exists(video_file):
            print(f"Video file not found: {video_file}")
            return False

        try:
            # Load the audio file
            audio = self.mp.AudioFileClip(audio_file)
            audio_duration = audio.duration

            # Adjust volume if needed
            if volume != 1.0:
                audio = audio.volumex(volume)

            # Load the background video
            video = self.mp.VideoFileClip(video_file)
            video_duration = video.duration

            # Check if we need to loop the video
            final_video = None
            if loop_video and audio_duration > video_duration:
                # Calculate how many times we need to loop the video
                loop_count = int(audio_duration / video_duration) + 1

                # Create a list of video clips to concatenate
                video_clips = []
                for i in range(loop_count):
                    video_clips.append(video)

                # Concatenate the video clips
                final_video = self.mp.concatenate_videoclips(video_clips)

                # Trim the final video to match the audio duration
                final_video = final_video.subclip(0, audio_duration)
            else:
                # If video is longer than audio or looping is disabled
                final_video = video.subclip(0, min(video_duration, audio_duration))

            # Add fade effects if requested
            if add_fade:
                # Add fade in at the beginning (0.5 seconds)
                final_video = final_video.fadein(0.5)

                # Add fade out at the end (0.5 seconds)
                final_video = final_video.fadeout(0.5)

            # Set the audio
            final_video = final_video.set_audio(audio)

            # Write the result to a file
            final_video.write_videofile(output_file, codec='libx264', audio_codec='aac')

            # Close the clips to free up resources
            final_video.close()
            video.close()
            audio.close()

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error merging audio with video: {str(e)}")
            return False

    def add_subtitles_to_video(self, video_file, subtitle_file, output_file,
                              font='Arial', fontsize=24, color='white',
                              stroke_color='black', stroke_width=1.5,
                              position=('center', 'bottom')):
        """
        Add subtitles to a video

        Args:
            video_file (str): Path to the video file
            subtitle_file (str): Path to the subtitle file (SRT format)
            output_file (str): Path to save the output video
            font (str, optional): Font name
            fontsize (int, optional): Font size
            color (str, optional): Text color
            stroke_color (str, optional): Text outline color
            stroke_width (float, optional): Text outline width
            position (tuple, optional): Position of subtitles (x, y)

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.moviepy_available:
            print("MoviePy not available. Install with: pip install moviepy")
            return False

        if not os.path.exists(video_file):
            print(f"Video file not found: {video_file}")
            return False

        if not os.path.exists(subtitle_file):
            print(f"Subtitle file not found: {subtitle_file}")
            return False

        try:
            from moviepy.video.tools.subtitles import SubtitlesClip

            # Load the video
            video = self.mp.VideoFileClip(video_file)

            # Define a generator function for subtitles
            def generator(txt):
                return self.mp.TextClip(
                    txt,
                    font=font,
                    fontsize=fontsize,
                    color=color,
                    stroke_color=stroke_color,
                    stroke_width=stroke_width,
                    method='caption',
                    align='center',
                    size=video.size
                )

            # Create subtitles clip
            subtitles = SubtitlesClip(subtitle_file, generator)

            # Add subtitles to video
            final_video = self.mp.CompositeVideoClip([video, subtitles.set_position(position)])

            # Write the result to a file
            final_video.write_videofile(output_file, codec='libx264', audio_codec='aac')

            # Close the clips to free up resources
            final_video.close()
            video.close()

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error adding subtitles to video: {str(e)}")
            return False

    def create_intro(self, title, duration=5, width=1920, height=1080,
                    bg_color='black', text_color='white', font='Arial',
                    fontsize=60, output_file=None):
        """
        Create an intro clip with title

        Args:
            title (str): Title text
            duration (int, optional): Duration in seconds
            width (int, optional): Width of the video
            height (int, optional): Height of the video
            bg_color (str, optional): Background color
            text_color (str, optional): Text color
            font (str, optional): Font name
            fontsize (int, optional): Font size
            output_file (str, optional): Path to save the output video

        Returns:
            moviepy.editor.VideoClip or bool: VideoClip object if output_file is None,
                                             otherwise True if successful, False if failed
        """
        if not self.moviepy_available:
            print("MoviePy not available. Install with: pip install moviepy")
            return False

        try:
            # Create a background clip
            bg_clip = self.mp.ColorClip(size=(width, height), color=bg_color, duration=duration)

            # Create a text clip
            txt_clip = self.mp.TextClip(
                title,
                font=font,
                fontsize=fontsize,
                color=text_color,
                method='caption',
                align='center',
                size=(width * 0.8, None)
            )

            # Set position and duration
            txt_clip = txt_clip.set_position('center').set_duration(duration)

            # Add fade in/out
            txt_clip = txt_clip.fadein(1).fadeout(1)

            # Composite the clips
            intro_clip = self.mp.CompositeVideoClip([bg_clip, txt_clip])

            # If output file is specified, write to file
            if output_file:
                intro_clip.write_videofile(output_file, codec='libx264', audio_codec='aac')

                # Close the clips to free up resources
                intro_clip.close()
                bg_clip.close()
                txt_clip.close()

                return os.path.exists(output_file)
            else:
                # Return the clip object
                return intro_clip

        except Exception as e:
            print(f"Error creating intro: {str(e)}")
            return False

    def create_outro(self, text, duration=5, width=1920, height=1080,
                    bg_color='black', text_color='white', font='Arial',
                    fontsize=40, output_file=None):
        """
        Create an outro clip with text

        Args:
            text (str): Outro text
            duration (int, optional): Duration in seconds
            width (int, optional): Width of the video
            height (int, optional): Height of the video
            bg_color (str, optional): Background color
            text_color (str, optional): Text color
            font (str, optional): Font name
            fontsize (int, optional): Font size
            output_file (str, optional): Path to save the output video

        Returns:
            moviepy.editor.VideoClip or bool: VideoClip object if output_file is None,
                                             otherwise True if successful, False if failed
        """
        if not self.moviepy_available:
            print("MoviePy not available. Install with: pip install moviepy")
            return False

        try:
            # Create a background clip
            bg_clip = self.mp.ColorClip(size=(width, height), color=bg_color, duration=duration)

            # Create a text clip
            txt_clip = self.mp.TextClip(
                text,
                font=font,
                fontsize=fontsize,
                color=text_color,
                method='caption',
                align='center',
                size=(width * 0.8, None)
            )

            # Set position and duration
            txt_clip = txt_clip.set_position('center').set_duration(duration)

            # Add fade in/out
            txt_clip = txt_clip.fadein(1).fadeout(1)

            # Composite the clips
            outro_clip = self.mp.CompositeVideoClip([bg_clip, txt_clip])

            # If output file is specified, write to file
            if output_file:
                outro_clip.write_videofile(output_file, codec='libx264', audio_codec='aac')

                # Close the clips to free up resources
                outro_clip.close()
                bg_clip.close()
                txt_clip.close()

                return os.path.exists(output_file)
            else:
                # Return the clip object
                return outro_clip

        except Exception as e:
            print(f"Error creating outro: {str(e)}")
            return False

    def create_complete_video(self, audio_file, bg_video_file, subtitle_file, output_file,
                             title=None, add_intro=True, add_outro=True,
                             intro_duration=5, outro_duration=5,
                             loop_video=True, add_fade=True):
        """
        Create a complete video with intro, content, and outro

        Args:
            audio_file (str): Path to the audio file
            bg_video_file (str): Path to the background video file
            subtitle_file (str): Path to the subtitle file
            output_file (str): Path to save the output video
            title (str, optional): Video title for intro
            add_intro (bool, optional): Whether to add an intro
            add_outro (bool, optional): Whether to add an outro
            intro_duration (int, optional): Intro duration in seconds
            outro_duration (int, optional): Outro duration in seconds
            loop_video (bool, optional): Whether to loop the background video
            add_fade (bool, optional): Whether to add fade effects

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.moviepy_available:
            print("MoviePy not available. Install with: pip install moviepy")
            return False

        if not os.path.exists(audio_file):
            print(f"Audio file not found: {audio_file}")
            return False

        if not os.path.exists(bg_video_file):
            print(f"Background video file not found: {bg_video_file}")
            return False

        if subtitle_file and not os.path.exists(subtitle_file):
            print(f"Subtitle file not found: {subtitle_file}")
            subtitle_file = None

        try:
            # Create temporary directory for intermediate files
            with tempfile.TemporaryDirectory() as temp_dir:
                # Step 1: Create main content (audio + background video)
                main_content_file = os.path.join(temp_dir, "main_content.mp4")
                if not self.merge_audio_with_video(
                    audio_file, bg_video_file, main_content_file,
                    loop_video=loop_video, add_fade=False, volume=1.0
                ):
                    print("Failed to create main content")
                    return False

                # Step 2: Add subtitles if available
                if subtitle_file:
                    subtitled_content_file = os.path.join(temp_dir, "subtitled_content.mp4")
                    if not self.add_subtitles_to_video(
                        main_content_file, subtitle_file, subtitled_content_file
                    ):
                        print("Failed to add subtitles")
                        return False
                else:
                    subtitled_content_file = main_content_file

                # Load the main content
                main_clip = self.mp.VideoFileClip(subtitled_content_file)

                # Create clips list
                clips = []

                # Step 3: Create intro if requested
                if add_intro and title:
                    intro_clip = self.create_intro(
                        title, duration=intro_duration,
                        width=main_clip.size[0], height=main_clip.size[1]
                    )
                    if intro_clip:
                        clips.append(intro_clip)

                # Add main content
                clips.append(main_clip)

                # Step 4: Create outro if requested
                if add_outro:
                    outro_text = "Thanks for watching!"
                    if title:
                        outro_text += f"\n{title}"

                    outro_clip = self.create_outro(
                        outro_text, duration=outro_duration,
                        width=main_clip.size[0], height=main_clip.size[1]
                    )
                    if outro_clip:
                        clips.append(outro_clip)

                # Step 5: Concatenate all clips
                final_clip = self.mp.concatenate_videoclips(clips)

                # Add fade effects if requested
                if add_fade:
                    final_clip = final_clip.fadein(1).fadeout(1)

                # Step 6: Write final video
                final_clip.write_videofile(output_file, codec='libx264', audio_codec='aac')

                # Close all clips
                final_clip.close()
                main_clip.close()

                return os.path.exists(output_file)

        except Exception as e:
            print(f"Error creating complete video: {str(e)}")
            return False

    def batch_process_videos(self, audio_files, bg_video_file, subtitle_files, output_dir,
                            titles=None, add_intro=True, add_outro=True):
        """
        Batch process multiple videos

        Args:
            audio_files (list): List of audio file paths
            bg_video_file (str): Path to the background video file
            subtitle_files (list): List of subtitle file paths (can be None for some entries)
            output_dir (str): Directory to save output videos
            titles (list, optional): List of video titles
            add_intro (bool, optional): Whether to add intros
            add_outro (bool, optional): Whether to add outros

        Returns:
            tuple: (successful_count, failed_count)
        """
        if not self.moviepy_available:
            print("MoviePy not available. Install with: pip install moviepy")
            return (0, len(audio_files))

        if not os.path.exists(bg_video_file):
            print(f"Background video file not found: {bg_video_file}")
            return (0, len(audio_files))

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        successful = 0
        failed = 0

        for i, audio_file in enumerate(audio_files):
            try:
                # Get corresponding subtitle file and title
                subtitle_file = subtitle_files[i] if i < len(subtitle_files) else None
                title = titles[i] if titles and i < len(titles) else None

                # Generate output file name
                if title:
                    # Clean title for filename
                    clean_title = "".join(c if c.isalnum() or c in " -_" else "_" for c in title)
                    clean_title = clean_title.replace(" ", "_")
                    output_file = os.path.join(output_dir, f"{clean_title}.mp4")
                else:
                    # Use audio filename as base
                    base_name = os.path.splitext(os.path.basename(audio_file))[0]
                    output_file = os.path.join(output_dir, f"{base_name}.mp4")

                print(f"Processing video {i+1}/{len(audio_files)}: {os.path.basename(output_file)}")

                # Create the video
                if self.create_complete_video(
                    audio_file, bg_video_file, subtitle_file, output_file,
                    title=title, add_intro=add_intro, add_outro=add_outro
                ):
                    successful += 1
                    print(f"Successfully created: {output_file}")
                else:
                    failed += 1
                    print(f"Failed to create: {output_file}")

            except Exception as e:
                failed += 1
                print(f"Error processing video {i+1}: {str(e)}")

        return (successful, failed)

    def extract_audio_from_video(self, video_file, output_file):
        """
        Extract audio from a video file

        Args:
            video_file (str): Path to the video file
            output_file (str): Path to save the audio file

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.moviepy_available:
            print("MoviePy not available. Install with: pip install moviepy")
            return False

        if not os.path.exists(video_file):
            print(f"Video file not found: {video_file}")
            return False

        try:
            # Load the video
            video = self.mp.VideoFileClip(video_file)

            # Extract audio
            audio = video.audio

            # Write audio to file
            audio.write_audiofile(output_file)

            # Close clips
            video.close()
            audio.close()

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error extracting audio: {str(e)}")
            return False

    def trim_video(self, video_file, output_file, start_time=0, end_time=None):
        """
        Trim a video file

        Args:
            video_file (str): Path to the video file
            output_file (str): Path to save the trimmed video
            start_time (float, optional): Start time in seconds
            end_time (float, optional): End time in seconds

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.moviepy_available:
            print("MoviePy not available. Install with: pip install moviepy")
            return False

        if not os.path.exists(video_file):
            print(f"Video file not found: {video_file}")
            return False

        try:
            # Load the video
            video = self.mp.VideoFileClip(video_file)

            # Trim the video
            trimmed_video = video.subclip(start_time, end_time)

            # Write the result to a file
            trimmed_video.write_videofile(output_file, codec='libx264', audio_codec='aac')

            # Close clips
            video.close()
            trimmed_video.close()

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error trimming video: {str(e)}")
            return False