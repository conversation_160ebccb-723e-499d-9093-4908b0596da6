# Boring History Generator

Một tool tự động để tạo nội dung video "Boring History for Sleep" hoàn chỉnh sử dụng AI.

## Tính năng

- ✅ **Tạo video titles** theo template "Boring History for Sleep"
- ✅ **Tạo script 15,000-16,500 từ** với 15 sections theo format chuẩn
- ✅ **Tạo voice** bằng Minimax TTS API
- ✅ **Tạo ảnh minh họa** bằng Leonardo AI API
- ✅ **Tạo project CapCut** tự động với timeline đã setup
- ✅ **Tổ chức file** theo cấu trúc thư mục logic
- ✅ **GUI thân thiện** với progress tracking

## Yêu cầu

### API Keys cần thiết:
1. **OpenAI API Key** - để tạo titles, scripts, và image prompts
2. **Minimax API Key** - để tạo voice/TTS
3. **Leonardo AI API Key** - để tạo ảnh

### Dependencies:
```bash
pip install -r requirements.txt
```

## Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd mergeaudiowithvideo
```

2. Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

3. Chạy ứng dụng:
```bash
python boring_history_generator.py
```

## Hướng dẫn sử dụng

### 1. Cấu hình API Keys

1. Mở tab **Settings**
2. Nhập các API keys:
   - **OpenAI API Key**: API key từ OpenAI
   - **OpenAI Base URL**: `https://api.openai.com/v1` (hoặc custom endpoint)
   - **Minimax API Key**: API key từ Minimax
   - **Minimax Base URL**: `https://api.minimax.chat`
   - **Leonardo AI API Key**: API key từ Leonardo AI

3. Cấu hình file settings:
   - **Output Directory**: Thư mục lưu output
   - **Background Video**: Video nền (tùy chọn)
   - **Voice ID**: ID voice cho TTS (mặc định: `male-qn-qingse`)
   - **Number of Images**: Số lượng ảnh tạo (5-20)

4. Click **Save Settings**

### 2. Tạo nội dung

1. Chuyển về tab **Main**
2. Nhập topic vào ô "Enter your topic"
3. Chọn một trong các options:
   - **1. Generate Title**: Chỉ tạo title
   - **2. Generate Script**: Chỉ tạo script outline
   - **3. Generate All Content**: Tạo toàn bộ nội dung

### 3. Workflow tự động (Generate All Content)

Khi chọn "Generate All Content", tool sẽ thực hiện:

1. **Tạo title** theo template "Boring History for Sleep"
2. **Tạo script outline** 15 bullets
3. **Tạo full script** 15 sections (1,000-1,100 từ mỗi section)
4. **Tạo project structure** với các thư mục:
   - `audio/` - File voice
   - `images/` - Ảnh minh họa
   - `video/` - Video output
   - `capcut/` - Project CapCut
   - `scripts/` - Script files
   - `prompts/` - Image prompts
5. **Tạo image prompts** từ script
6. **Tạo voice** bằng Minimax TTS
7. **Tạo ảnh** bằng Leonardo AI
8. **Tạo CapCut project** với timeline setup

### 4. Import vào CapCut

1. Mở CapCut PC
2. Import project file từ thư mục `capcut/`
3. Timeline đã được setup với:
   - Audio track: Voice narration
   - Video track 1: Background video (nếu có)
   - Video track 2: Ảnh minh họa với timing tự động
4. Xuất video theo ý muốn

## Cấu trúc output

```
Output Directory/
├── Project_Name/
│   ├── audio/
│   │   └── narration.mp3
│   ├── images/
│   │   ├── scene_001_1.jpg
│   │   ├── scene_002_1.jpg
│   │   └── ...
│   ├── video/
│   ├── capcut/
│   │   └── Project_Name.cep
│   ├── scripts/
│   │   └── full_script.txt
│   └── prompts/
│       └── image_prompts.txt
```

## Templates được sử dụng

### 1. Title Generation
- Sử dụng prefixes: "Boring History For Sleep |", "The Boring History For Sleep |", etc.
- Tạo hooks curiosity-driven ≤ 14 từ
- Thêm runtime tags khi phù hợp

### 2. Script Generation
- Format: 15 sections, 1,000-1,100 từ mỗi section
- Style: Second-person present tense, gentle sarcasm, ASMR cadence
- Bao gồm: Historical facts, quirky tidbits, scholarly debates
- Introduction template với CTA chuẩn

### 3. Image Generation
- Style: Late-15th-century illuminated manuscript
- Format: Medieval perspective, tempera & shell-gold on vellum
- Aspect ratio: 16:9, ultra-high-resolution (4K)

## Troubleshooting

### Lỗi API
- Kiểm tra API keys đã nhập đúng
- Kiểm tra balance/credits của các API services
- Kiểm tra network connection

### Lỗi file
- Đảm bảo output directory có quyền write
- Kiểm tra disk space đủ cho ảnh và audio files

### Lỗi generation
- Kiểm tra logs tab để xem chi tiết lỗi
- Thử giảm số lượng images nếu Leonardo AI bị rate limit
- Thử topic ngắn gọn hơn nếu script generation fail

## API Rate Limits

- **OpenAI**: Tuân theo rate limits của plan
- **Minimax**: Có delay giữa các requests
- **Leonardo AI**: Có delay 2 giây giữa các image generations

## Lưu ý

1. **Script generation** có thể mất 15-30 phút do phải tạo 15 sections
2. **Image generation** có thể mất 10-20 phút tùy số lượng ảnh
3. **Voice generation** có thể mất 5-15 phút tùy độ dài script
4. **Total time**: 30-60 phút cho một video hoàn chỉnh

## Support

Nếu gặp vấn đề, kiểm tra:
1. Logs tab trong ứng dụng
2. API documentation của các services
3. Network connectivity
4. File permissions

## Changelog

### v1.0.0
- Initial release
- Full workflow automation
- GUI interface
- CapCut project generation
