"""
Test Template Configurator
Simple test to verify the template configurator works
"""

import tkinter as tk
from template_configurator import TemplateConfigurator
from flexible_templates import TemplateConfig


def test_template_config():
    """Test template configuration functionality"""
    print("=== Testing Template Configuration ===")
    
    # Test default config
    config = TemplateConfig()
    print(f"Default title num_titles: {config.title_num_titles}")
    print(f"Default script total_words: {config.script_total_words}")
    print(f"Default image art_style: {config.image_art_style}")
    
    # Test generating prompts
    topic = "Medieval Court Jesters"
    
    print("\n--- Title Prompt ---")
    title_prompt = config.get_title_prompt(topic)
    print(title_prompt[:200] + "...")
    
    print("\n--- Script Prompt (first 200 chars) ---")
    script_prompt = config.get_script_prompt(topic)
    print(script_prompt[:200] + "...")
    
    print("\n--- Image Prompt ---")
    sample_script = "You find yourself in a medieval castle courtyard. The jester approaches with his colorful outfit and jingling bells..."
    image_prompt = config.get_image_prompt(sample_script, 1)
    print(image_prompt[:200] + "...")
    
    print("\n--- Thumbnail Prompt ---")
    thumbnail_prompt = config.get_thumbnail_prompt("medieval jester with bells and colorful costume, laughing expression")
    print(thumbnail_prompt[:200] + "...")
    
    # Test saving/loading config
    print("\n--- Testing Save/Load ---")
    config_dict = config.save_to_dict()
    print(f"Config saved to dict with keys: {list(config_dict.keys())}")
    
    # Modify config
    config.title_num_titles = 10
    config.script_total_words = 20000
    config.image_art_style = "modern digital art"
    
    print(f"Modified title num_titles: {config.title_num_titles}")
    print(f"Modified script total_words: {config.script_total_words}")
    print(f"Modified image art_style: {config.image_art_style}")
    
    # Load back original config
    config.load_from_dict(config_dict)
    print(f"Restored title num_titles: {config.title_num_titles}")
    print(f"Restored script total_words: {config.script_total_words}")
    print(f"Restored image art_style: {config.image_art_style}")
    
    print("\n✅ Template configuration test completed successfully!")


def test_gui():
    """Test the GUI configurator"""
    print("\n=== Testing GUI Configurator ===")
    
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    try:
        configurator = TemplateConfigurator(root)
        
        print("✅ Template configurator GUI created successfully!")
        print("GUI window should be visible now.")
        print("Close the configurator window to continue...")
        
        # Wait for window to close
        root.wait_window(configurator.window)
        
        # Get final config
        final_config = configurator.get_config()
        print(f"Final config - title num_titles: {final_config.title_num_titles}")
        print(f"Final config - script total_words: {final_config.script_total_words}")
        
        print("✅ GUI test completed!")
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
    finally:
        root.destroy()


def test_custom_templates():
    """Test custom template configurations"""
    print("\n=== Testing Custom Templates ===")
    
    config = TemplateConfig()
    
    # Test custom title settings
    config.title_num_titles = 3
    config.title_max_hook_words = 10
    config.title_tone = "serious and scholarly"
    config.title_focus_regions = ["Ancient Rome", "Medieval Europe"]
    config.title_custom_prefixes = [
        '"Historical Deep Dive | "',
        '"Ancient Wisdom For Sleep | "',
        '"Scholarly History | "'
    ]
    
    topic = "Roman Engineering"
    custom_title_prompt = config.get_title_prompt(topic)
    
    print("Custom Title Prompt:")
    print(custom_title_prompt)
    print()
    
    # Test custom script settings
    config.script_total_words = 8000
    config.script_num_sections = 8
    config.script_words_per_section = 1000
    config.script_style = "academic lecturer + measured pace + thoughtful pauses"
    config.script_include_intro = False
    config.script_include_wind_down = True
    
    custom_script_prompt = config.get_script_prompt(topic)
    
    print("Custom Script Prompt (first 300 chars):")
    print(custom_script_prompt[:300] + "...")
    print()
    
    # Test custom image settings
    config.image_art_style = "Renaissance fresco painting"
    config.image_medium = "oil on canvas"
    config.image_perspective = "one-point perspective"
    config.image_aspect_ratio = "4:3"
    config.image_color_palette = "warm earth tones (ochre, sienna, umber)"
    
    sample_script = "You stand before the magnificent Roman aqueduct, marveling at the engineering prowess of ancient builders..."
    custom_image_prompt = config.get_image_prompt(sample_script, 1)
    
    print("Custom Image Prompt:")
    print(custom_image_prompt)
    print()
    
    print("✅ Custom templates test completed!")


def main():
    """Run all tests"""
    print("🧪 Template Configurator Tests\n")
    
    # Test 1: Basic functionality
    test_template_config()
    
    # Test 2: Custom configurations
    test_custom_templates()
    
    # Test 3: GUI (optional - comment out if you don't want GUI)
    response = input("\nDo you want to test the GUI configurator? (y/n): ")
    if response.lower() == 'y':
        test_gui()
    else:
        print("Skipping GUI test.")
    
    print("\n🎉 All tests completed!")
    print("\nTo use the template configurator in the main app:")
    print("1. Run: python boring_history_generator.py")
    print("2. Go to Settings tab")
    print("3. Click 'Configure Templates' button")


if __name__ == "__main__":
    main()
