# Midjourney Prompt Generator

Một công cụ GUI để phân tích script bằng ChatGPT, tạo prompt cho Midjourney, chỉnh sửa prompt và tạo hình ảnh bằng DALL-E 3.

## Tính năng

- ✅ **Phân tích script với ChatGPT**: Tự động phân tích nội dung script và trích xuất các khái niệm trực quan
- ✅ **Tạo prompt Midjourney**: Tạo prompt chi tiết cho Midjourney dựa trên phân tích script
- ✅ **Chỉnh sửa prompt**: Giao diện để xem và chỉnh sửa prompt trước khi sử dụng
- ✅ **Tạo hình ảnh với DALL-E 3**: Thay thế cho Midjourney vì không có API công khai
- ✅ **Lưu prompt và hình ảnh**: Tự động lưu prompt và hình ảnh vào thư mục đã chọn
- ✅ **Cấu hình API**: Hỗ trợ cấu hình OpenAI base URL và API key

## Lưu ý quan trọng về Midjourney API

⚠️ **Midjourney hiện tại KHÔNG có API công khai**. Tool này cung cấp:

1. **Tạo prompt cho Midjourney**: Bạn có thể copy prompt để sử dụng thủ công trong Discord
2. **Thay thế bằng DALL-E 3**: Sử dụng DALL-E 3 để tạo hình ảnh trực tiếp
3. **Lưu prompt**: Lưu prompt để sử dụng sau với các dịch vụ khác

## Cài đặt

### Yêu cầu hệ thống
- Python 3.7+
- Các package Python cần thiết (xem requirements.txt)

### Cài đặt dependencies
```bash
pip install openai python-dotenv requests tkinter
```

## Sử dụng

### 1. Chạy ứng dụng
```bash
python run_midjourney_prompt_generator.py
```

### 2. Cấu hình API (Tab Setup)
- **OpenAI API Key**: API key của OpenAI (bắt buộc)
- **OpenAI Base URL**: URL của OpenAI API (mặc định: https://api.openai.com/v1)
- **Midjourney API Key**: Để trống (không có API công khai)
- **Prompts Directory**: Thư mục lưu prompt
- **Images Directory**: Thư mục lưu hình ảnh

### 3. Nhập script (Tab Script)
- Nhập hoặc load script từ file
- Click "Analyze Script" để phân tích với ChatGPT

### 4. Xem kết quả phân tích (Tab Analysis)
- Xem kết quả phân tích script
- Chọn style preset cho prompt
- Click "Generate Midjourney Prompts"

### 5. Chỉnh sửa và sử dụng prompt (Tab Prompts)
- Duyệt qua các prompt đã tạo
- Chỉnh sửa prompt nếu cần
- **Generate with DALL-E 3**: Tạo hình ảnh trực tiếp
- **Save Prompt**: Lưu prompt hiện tại
- **Save All Prompts**: Lưu tất cả prompt

## Cấu trúc file

```
midjourney_prompt_generator.py          # Core logic
midjourney_prompt_generator_app.py      # GUI application
run_midjourney_prompt_generator.py      # Launcher script
settings.json                           # Cấu hình
```

## Style Presets

Tool hỗ trợ các style preset sau:
- **minimalist**: Phong cách tối giản, đường nét sạch
- **sketch**: Phong cách vẽ tay, nét bút chì
- **watercolor**: Phong cách màu nước
- **3d_render**: Render 3D chuyên nghiệp
- **comic**: Phong cách truyện tranh
- **photorealistic**: Phong cách ảnh thực

## Ví dụ sử dụng

1. **Nhập script**: "Think you're not creative? I'm about to prove you wrong—here's how to unlock your creativity in just 3 minutes."

2. **Phân tích**: ChatGPT sẽ phân tích và trích xuất các khái niệm trực quan

3. **Tạo prompt**: Tool tạo prompt Midjourney chi tiết như:
   ```
   A minimalist black and white illustration of a stick figure scratching their head with a question mark floating above, surrounded by faded art supplies in the background, clean lines, simple shapes, high contrast --ar 16:9
   ```

4. **Tạo hình ảnh**: Sử dụng DALL-E 3 để tạo hình ảnh từ prompt

## Giải pháp thay thế cho Midjourney

Vì Midjourney không có API công khai, tool cung cấp các giải pháp thay thế:

### 1. DALL-E 3 (Được tích hợp)
- API chính thức từ OpenAI
- Chất lượng cao
- Tích hợp trực tiếp trong tool

### 2. Stable Diffusion (Có thể mở rộng)
- API miễn phí/mở
- Nhiều model khác nhau
- Có thể tự host

### 3. Sử dụng prompt thủ công
- Copy prompt từ tool
- Paste vào Discord Midjourney bot
- Sử dụng các dịch vụ third-party

## Troubleshooting

### Lỗi "OpenAI client not initialized"
- Kiểm tra API key OpenAI
- Kiểm tra base URL (nếu sử dụng custom)

### Lỗi "No prompts to generate"
- Phân tích script trước khi tạo prompt
- Kiểm tra script không rỗng

### Lỗi khi tạo hình ảnh
- Kiểm tra API key OpenAI
- Kiểm tra kết nối internet
- Kiểm tra prompt không quá dài (DALL-E có giới hạn)

## Phát triển thêm

Tool có thể mở rộng để hỗ trợ:
- Stable Diffusion API
- Các dịch vụ AI art khác
- Batch processing
- Template prompt library
- Integration với các workflow khác

## License

MIT License - Xem file LICENSE để biết thêm chi tiết.
