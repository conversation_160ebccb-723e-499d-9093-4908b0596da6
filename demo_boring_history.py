"""
Demo script for Boring History Generator
Test the individual components
"""

import os
import json
from boring_history_templates import *
from minimax_tts import MinimaxTTS
from leonardo_ai import LeonardoAI
from capcut_project_creator import CapCutProjectCreator
from openai import OpenAI


def test_templates():
    """Test template formatting"""
    print("=== Testing Templates ===")
    
    topic = "Medieval Court Jesters"
    
    # Test title generation prompt
    title_prompt = TITLE_GENERATION_PROMPT.format(topic=topic)
    print("Title Generation Prompt:")
    print(title_prompt[:200] + "...")
    print()
    
    # Test script generation prompt
    script_prompt = SCRIPT_GENERATION_PROMPT.format(topic=topic)
    print("Script Generation Prompt:")
    print(script_prompt[:200] + "...")
    print()
    
    # Test image generation prompt
    sample_script = "You find yourself in a medieval castle courtyard. The jester approaches with his colorful outfit and jingling bells..."
    image_prompt = IMAGE_GENERATION_PROMPT.format(num_images=3, script_section=sample_script)
    print("Image Generation Prompt:")
    print(image_prompt[:200] + "...")
    print()


def test_minimax_tts():
    """Test Minimax TTS (requires API key)"""
    print("=== Testing Minimax TTS ===")
    
    # Load API key from settings if available
    api_key = None
    try:
        if os.path.exists("boring_history_settings.json"):
            with open("boring_history_settings.json", "r") as f:
                settings = json.load(f)
                api_key = settings.get("minimax_api_key")
    except:
        pass
    
    if not api_key:
        print("No Minimax API key found. Skipping TTS test.")
        print("Add your API key to boring_history_settings.json to test.")
        return
    
    try:
        tts = MinimaxTTS(api_key=api_key)
        
        # Test getting voices
        print("Getting available voices...")
        voices = tts.get_available_voices()
        if voices:
            print(f"Found voices: {voices}")
        else:
            print("No voices returned or API error")
        
        # Test short text generation
        test_text = "Hey guys, tonight we're diving into the fascinating world of medieval court jesters."
        output_file = "test_voice.mp3"
        
        print(f"Generating test voice: '{test_text[:50]}...'")
        success = tts.generate_speech(test_text, output_file)
        
        if success and os.path.exists(output_file):
            print(f"✅ Voice generated successfully: {output_file}")
            print(f"File size: {os.path.getsize(output_file)} bytes")
        else:
            print("❌ Voice generation failed")
            
    except Exception as e:
        print(f"❌ Minimax TTS test failed: {e}")


def test_leonardo_ai():
    """Test Leonardo AI (requires API key)"""
    print("=== Testing Leonardo AI ===")
    
    # Load API key from settings if available
    api_key = None
    try:
        if os.path.exists("boring_history_settings.json"):
            with open("boring_history_settings.json", "r") as f:
                settings = json.load(f)
                api_key = settings.get("leonardo_api_key")
    except:
        pass
    
    if not api_key:
        print("No Leonardo AI API key found. Skipping image test.")
        print("Add your API key to boring_history_settings.json to test.")
        return
    
    try:
        leonardo = LeonardoAI(api_key=api_key)
        
        # Test getting user info
        print("Getting user info...")
        user_info = leonardo.get_user_info()
        if user_info:
            print(f"✅ User info retrieved: {user_info.get('user', {}).get('username', 'Unknown')}")
        else:
            print("❌ Failed to get user info")
            return
        
        # Test getting models
        print("Getting available models...")
        models = leonardo.get_models()
        if models:
            print(f"✅ Found {len(models)} models")
            if models:
                print(f"First model: {models[0].get('name', 'Unknown')}")
        else:
            print("❌ No models found")
        
        # Test image generation (small test)
        test_prompt = "A medieval court jester in colorful outfit with bells, late-15th-century illuminated manuscript style, tempera and shell-gold on vellum"
        print(f"Testing image generation with prompt: '{test_prompt[:50]}...'")
        
        generation_result = leonardo.generate_image(
            prompt=test_prompt,
            width=512,  # Smaller size for test
            height=512,
            num_images=1
        )
        
        if generation_result and generation_result.get('generation_id'):
            print(f"✅ Image generation started: {generation_result['generation_id']}")
            print("Note: Full generation test would wait for completion and download")
        else:
            print("❌ Image generation failed to start")
            
    except Exception as e:
        print(f"❌ Leonardo AI test failed: {e}")


def test_capcut_creator():
    """Test CapCut project creator"""
    print("=== Testing CapCut Project Creator ===")
    
    try:
        creator = CapCutProjectCreator()
        
        # Create test folder structure
        test_dir = "test_output"
        project_name = "Test_Medieval_Jesters"
        
        print("Creating project folder structure...")
        folders = creator.create_project_folder_structure(test_dir, project_name)
        
        print("✅ Created folders:")
        for name, path in folders.items():
            print(f"  {name}: {path}")
        
        # Create dummy files for testing
        dummy_audio = os.path.join(folders["audio"], "test_audio.mp3")
        dummy_images = []
        
        # Create dummy audio file
        with open(dummy_audio, "wb") as f:
            f.write(b"dummy audio data")
        
        # Create dummy image files
        for i in range(3):
            dummy_image = os.path.join(folders["images"], f"scene_{i+1:03d}_1.jpg")
            with open(dummy_image, "wb") as f:
                f.write(b"dummy image data")
            dummy_images.append(dummy_image)
        
        # Create CapCut project
        print("Creating CapCut project...")
        project_file = creator.create_project(
            project_name=project_name,
            audio_file=dummy_audio,
            image_files=dummy_images,
            output_dir=folders["capcut"]
        )
        
        if project_file and os.path.exists(project_file):
            print(f"✅ CapCut project created: {project_file}")
            
            # Check project file content
            with open(project_file, "r", encoding="utf-8") as f:
                project_data = json.load(f)
            
            print(f"Project ID: {project_data.get('id', 'Unknown')}")
            print(f"Project name: {project_data.get('name', 'Unknown')}")
            print(f"Duration: {project_data.get('duration', 0)} ms")
            print(f"Audio materials: {len(project_data.get('materials', {}).get('audios', []))}")
            print(f"Image materials: {len(project_data.get('materials', {}).get('images', []))}")
            print(f"Audio tracks: {len(project_data.get('tracks', {}).get('audio_tracks', []))}")
            print(f"Video tracks: {len(project_data.get('tracks', {}).get('video_tracks', []))}")
        else:
            print("❌ CapCut project creation failed")
        
        print(f"\nTest files created in: {folders['project']}")
        print("You can manually delete this folder after testing.")
        
    except Exception as e:
        print(f"❌ CapCut creator test failed: {e}")


def test_openai_connection():
    """Test OpenAI connection"""
    print("=== Testing OpenAI Connection ===")
    
    # Load API key from settings if available
    api_key = None
    base_url = "https://api.openai.com/v1"
    
    try:
        if os.path.exists("boring_history_settings.json"):
            with open("boring_history_settings.json", "r") as f:
                settings = json.load(f)
                api_key = settings.get("openai_api_key")
                base_url = settings.get("openai_base_url", base_url)
    except:
        pass
    
    if not api_key:
        print("No OpenAI API key found. Skipping OpenAI test.")
        print("Add your API key to boring_history_settings.json to test.")
        return
    
    try:
        client = OpenAI(api_key=api_key, base_url=base_url)
        
        # Test simple completion
        print("Testing simple completion...")
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Say 'Hello, this is a test!'"}],
            max_tokens=50
        )
        
        result = response.choices[0].message.content.strip()
        print(f"✅ OpenAI response: {result}")
        
        # Test title generation
        print("Testing title generation...")
        topic = "Medieval Court Jesters"
        prompt = TITLE_GENERATION_PROMPT.format(topic=topic)
        
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=500,
            temperature=0.8
        )
        
        titles = response.choices[0].message.content.strip()
        print(f"✅ Generated titles preview: {titles[:100]}...")
        
    except Exception as e:
        print(f"❌ OpenAI test failed: {e}")


def main():
    """Run all tests"""
    print("🧪 Boring History Generator - Component Tests\n")
    
    test_templates()
    print("\n" + "="*50 + "\n")
    
    test_openai_connection()
    print("\n" + "="*50 + "\n")
    
    test_minimax_tts()
    print("\n" + "="*50 + "\n")
    
    test_leonardo_ai()
    print("\n" + "="*50 + "\n")
    
    test_capcut_creator()
    print("\n" + "="*50 + "\n")
    
    print("🎉 All tests completed!")
    print("\nTo run the full application:")
    print("python boring_history_generator.py")


if __name__ == "__main__":
    main()
