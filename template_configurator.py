"""
Template Configurator GUI
Allows users to customize prompt templates
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import os
from flexible_templates import TemplateConfig


class TemplateConfigurator:
    """GUI for configuring template parameters"""

    def __init__(self, parent=None):
        if parent:
            self.window = tk.Toplevel(parent)
        else:
            self.window = tk.Tk()

        self.window.title("🎨 Tùy Chỉnh Templates - Cấu H<PERSON>nh Prompts AI")
        self.window.geometry("1000x900")

        self.config = TemplateConfig()
        self.config_updated = False
        self.load_config()

        self.create_gui()

    def create_gui(self):
        """Create the GUI interface"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title settings tab
        self.title_frame = ttk.Frame(notebook)
        notebook.add(self.title_frame, text="🎬 Cài Đặt Tiêu <PERSON>")

        # Script settings tab
        self.script_frame = ttk.Frame(notebook)
        notebook.add(self.script_frame, text="📜 Cài Đặt Kịch Bản")

        # Image settings tab
        self.image_frame = ttk.Frame(notebook)
        notebook.add(self.image_frame, text="🎨 Cài Đặt Hình Ảnh")

        # Thumbnail settings tab
        self.thumbnail_frame = ttk.Frame(notebook)
        notebook.add(self.thumbnail_frame, text="🖼️ Cài Đặt Thumbnail")

        # Preview tab
        self.preview_frame = ttk.Frame(notebook)
        notebook.add(self.preview_frame, text="👁️ Xem Trước")

        self.create_title_tab()
        self.create_script_tab()
        self.create_image_tab()
        self.create_thumbnail_tab()
        self.create_preview_tab()

        # Control buttons
        button_frame = ttk.Frame(self.window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(button_frame, text="💾 Lưu Cấu Hình", command=self.save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📂 Tải Cấu Hình", command=self.load_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 Khôi Phục Mặc Định", command=self.reset_defaults).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="✅ Áp Dụng & Đóng", command=self.apply_and_close).pack(side=tk.RIGHT, padx=5)

    def create_title_tab(self):
        """Create title settings tab"""
        # Description
        desc_frame = ttk.LabelFrame(self.title_frame, text="📝 Mô Tả", padding=5)
        desc_frame.grid(row=0, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(desc_frame, text="Cấu hình cách tạo tiêu đề video theo format 'Boring History for Sleep'",
                 font=("Arial", 9)).pack()

        # Number of titles
        ttk.Label(self.title_frame, text="Số lượng tiêu đề:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Label(self.title_frame, text="(1-10 tiêu đề sẽ được tạo)",
                 font=("Arial", 8), foreground="gray").grid(row=1, column=2, sticky=tk.W, padx=5)
        self.title_num_var = tk.IntVar(value=self.config.title_num_titles)
        ttk.Spinbox(self.title_frame, from_=1, to=10, textvariable=self.title_num_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5)

        # Max hook words
        ttk.Label(self.title_frame, text="Số từ tối đa trong hook:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Label(self.title_frame, text="(Phần hấp dẫn sau dấu |)",
                 font=("Arial", 8), foreground="gray").grid(row=2, column=2, sticky=tk.W, padx=5)
        self.title_hook_words_var = tk.IntVar(value=self.config.title_max_hook_words)
        ttk.Spinbox(self.title_frame, from_=5, to=30, textvariable=self.title_hook_words_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=5)

        # Include runtime
        self.title_runtime_var = tk.BooleanVar(value=self.config.title_include_runtime)
        ttk.Checkbutton(self.title_frame, text="Bao gồm thẻ thời lượng (2 HOURS, 4 HOURS)",
                       variable=self.title_runtime_var).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=5)

        # Tone
        ttk.Label(self.title_frame, text="Phong cách:", font=("Arial", 10, "bold")).grid(row=4, column=0, sticky=tk.W, pady=5)
        ttk.Label(self.title_frame, text="(Tone của tiêu đề)",
                 font=("Arial", 8), foreground="gray").grid(row=4, column=2, sticky=tk.W, padx=5)
        self.title_tone_var = tk.StringVar(value=self.config.title_tone)
        tone_combo = ttk.Combobox(self.title_frame, textvariable=self.title_tone_var, width=30)
        tone_combo['values'] = [
            "hài hước nhẹ nhàng",
            "nghiêm túc và học thuật",
            "vui tươi và hài hước",
            "bí ẩn và hấp dẫn",
            "ấm áp và thân thiện"
        ]
        tone_combo.grid(row=4, column=1, sticky=tk.W, padx=5)

        # Focus regions
        ttk.Label(self.title_frame, text="Khu vực tập trung:", font=("Arial", 10, "bold")).grid(row=5, column=0, sticky=tk.W, pady=5)
        ttk.Label(self.title_frame, text="(Cách nhau bằng dấu phẩy)",
                 font=("Arial", 8), foreground="gray").grid(row=5, column=2, sticky=tk.W, padx=5)
        self.title_regions_var = tk.StringVar()
        if self.config.title_focus_regions:
            self.title_regions_var.set(", ".join(self.config.title_focus_regions))
        ttk.Entry(self.title_frame, textvariable=self.title_regions_var, width=40).grid(row=5, column=1, sticky=tk.W, padx=5)

        # Custom prefixes
        ttk.Label(self.title_frame, text="Tiền tố tùy chỉnh:", font=("Arial", 10, "bold")).grid(row=6, column=0, sticky=tk.NW, pady=5)
        ttk.Label(self.title_frame, text="(Mỗi dòng một tiền tố)",
                 font=("Arial", 8), foreground="gray").grid(row=6, column=2, sticky=tk.NW, padx=5)
        self.title_prefixes_text = scrolledtext.ScrolledText(self.title_frame, height=5, width=50)
        self.title_prefixes_text.grid(row=6, column=1, sticky=tk.W, padx=5, pady=2)
        if self.config.title_custom_prefixes:
            self.title_prefixes_text.insert(1.0, "\n".join(self.config.title_custom_prefixes))

    def create_script_tab(self):
        """Create script settings tab"""
        # Description
        desc_frame = ttk.LabelFrame(self.script_frame, text="📝 Mô Tả", padding=5)
        desc_frame.grid(row=0, column=0, columnspan=3, sticky=tk.EW, padx=5, pady=5)
        ttk.Label(desc_frame, text="Cấu hình cách tạo kịch bản video với nhiều phần và phong cách kể chuyện",
                 font=("Arial", 9)).pack()

        # Language selection
        ttk.Label(self.script_frame, text="Ngôn ngữ:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Label(self.script_frame, text="(Chọn ngôn ngữ cho kịch bản)",
                 font=("Arial", 8), foreground="gray").grid(row=1, column=2, sticky=tk.W, padx=25)
        self.language_var = tk.StringVar(value=getattr(self.config, 'language', 'english'))
        language_combo = ttk.Combobox(self.script_frame, textvariable=self.language_var, width=20)
        language_combo['values'] = ["english", "vietnamese"]
        language_combo.grid(row=1, column=1, sticky=tk.W, padx=5)
        language_combo.bind('<<ComboboxSelected>>', self.on_language_change)

        # Total words
        ttk.Label(self.script_frame, text="Tổng số từ:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Label(self.script_frame, text="(5,000-30,000 từ)",
                 font=("Arial", 8), foreground="gray").grid(row=2, column=2, sticky=tk.W, padx=5)
        self.script_total_words_var = tk.IntVar(value=self.config.script_total_words)
        ttk.Spinbox(self.script_frame, from_=5000, to=30000, increment=1000, textvariable=self.script_total_words_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=5)

        # Number of sections
        ttk.Label(self.script_frame, text="Number of sections:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.script_num_sections_var = tk.IntVar(value=self.config.script_num_sections)
        ttk.Spinbox(self.script_frame, from_=5, to=25, textvariable=self.script_num_sections_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5)

        # Words per section
        ttk.Label(self.script_frame, text="Words per section:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.script_words_per_section_var = tk.IntVar(value=self.config.script_words_per_section)
        ttk.Spinbox(self.script_frame, from_=500, to=2000, increment=100, textvariable=self.script_words_per_section_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=5)

        # Style
        ttk.Label(self.script_frame, text="Narration style:").grid(row=3, column=0, sticky=tk.NW, pady=2)
        self.script_style_var = tk.StringVar(value=self.config.script_style)
        style_combo = ttk.Combobox(self.script_frame, textvariable=self.script_style_var, width=50)
        style_combo['values'] = [
            "relaxed YouTube host + gentle sarcasm + sleepy ASMR cadence",
            "professional documentary narrator + calm delivery",
            "storytelling grandparent + warm and cozy",
            "academic lecturer + measured pace + thoughtful pauses",
            "bedtime story reader + soft whispers + gentle rhythm"
        ]
        style_combo.grid(row=3, column=1, sticky=tk.W, padx=5)

        # Include intro
        self.script_intro_var = tk.BooleanVar(value=self.config.script_include_intro)
        ttk.Checkbutton(self.script_frame, text="Include intro template", variable=self.script_intro_var).grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=2)

        # Include wind-down
        self.script_wind_down_var = tk.BooleanVar(value=self.config.script_include_wind_down)
        ttk.Checkbutton(self.script_frame, text="Include wind-down section", variable=self.script_wind_down_var).grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=2)

    def create_image_tab(self):
        """Create image settings tab"""
        # Art style
        ttk.Label(self.image_frame, text="Art style:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.image_art_style_var = tk.StringVar(value=self.config.image_art_style)
        art_style_combo = ttk.Combobox(self.image_frame, textvariable=self.image_art_style_var, width=50)
        art_style_combo['values'] = [
            "late‑15th‑century illuminated‑manuscript miniature",
            "medieval tapestry style",
            "Renaissance fresco painting",
            "Victorian book illustration",
            "Art Nouveau poster design",
            "minimalist line art",
            "watercolor sketch"
        ]
        art_style_combo.grid(row=0, column=1, sticky=tk.W, padx=5)

        # Medium
        ttk.Label(self.image_frame, text="Medium:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.image_medium_var = tk.StringVar(value=self.config.image_medium)
        medium_combo = ttk.Combobox(self.image_frame, textvariable=self.image_medium_var, width=50)
        medium_combo['values'] = [
            "tempera & shell‑gold on vellum",
            "oil on canvas",
            "watercolor on paper",
            "ink and wash",
            "digital art",
            "pencil sketch",
            "charcoal drawing"
        ]
        medium_combo.grid(row=1, column=1, sticky=tk.W, padx=5)

        # Perspective
        ttk.Label(self.image_frame, text="Perspective:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.image_perspective_var = tk.StringVar(value=self.config.image_perspective)
        perspective_combo = ttk.Combobox(self.image_frame, textvariable=self.image_perspective_var, width=50)
        perspective_combo['values'] = [
            "flat medieval perspective",
            "one-point perspective",
            "bird's eye view",
            "isometric view",
            "close-up portrait",
            "wide landscape view"
        ]
        perspective_combo.grid(row=2, column=1, sticky=tk.W, padx=5)

        # Aspect ratio
        ttk.Label(self.image_frame, text="Aspect ratio:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.image_aspect_var = tk.StringVar(value=self.config.image_aspect_ratio)
        aspect_combo = ttk.Combobox(self.image_frame, textvariable=self.image_aspect_var, width=20)
        aspect_combo['values'] = ["16:9", "4:3", "1:1", "3:4", "9:16"]
        aspect_combo.grid(row=3, column=1, sticky=tk.W, padx=5)

        # Resolution
        ttk.Label(self.image_frame, text="Resolution:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.image_resolution_var = tk.StringVar(value=self.config.image_resolution)
        resolution_combo = ttk.Combobox(self.image_frame, textvariable=self.image_resolution_var, width=30)
        resolution_combo['values'] = [
            "ultra‑high‑resolution (4K)",
            "high‑resolution (1080p)",
            "standard‑resolution (720p)",
            "8K resolution",
            "print quality (300 DPI)"
        ]
        resolution_combo.grid(row=4, column=1, sticky=tk.W, padx=5)

        # Color palette
        ttk.Label(self.image_frame, text="Color palette:").grid(row=5, column=0, sticky=tk.NW, pady=2)
        self.image_color_var = tk.StringVar(value=self.config.image_color_palette)
        ttk.Entry(self.image_frame, textvariable=self.image_color_var, width=60).grid(row=5, column=1, sticky=tk.W, padx=5)

    def create_thumbnail_tab(self):
        """Create thumbnail settings tab"""
        # Style
        ttk.Label(self.thumbnail_frame, text="Style:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.thumbnail_style_var = tk.StringVar(value=self.config.thumbnail_style)
        ttk.Entry(self.thumbnail_frame, textvariable=self.thumbnail_style_var, width=50).grid(row=0, column=1, sticky=tk.W, padx=5)

        # Background
        ttk.Label(self.thumbnail_frame, text="Background:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.thumbnail_bg_var = tk.StringVar(value=self.config.thumbnail_background)
        bg_combo = ttk.Combobox(self.thumbnail_frame, textvariable=self.thumbnail_bg_var, width=30)
        bg_combo['values'] = [
            "pure white background",
            "black background",
            "gradient background",
            "textured background",
            "transparent background"
        ]
        bg_combo.grid(row=1, column=1, sticky=tk.W, padx=5)

        # Aspect ratio
        ttk.Label(self.thumbnail_frame, text="Aspect ratio:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.thumbnail_aspect_var = tk.StringVar(value=self.config.thumbnail_aspect_ratio)
        thumb_aspect_combo = ttk.Combobox(self.thumbnail_frame, textvariable=self.thumbnail_aspect_var, width=20)
        thumb_aspect_combo['values'] = ["9:16", "16:9", "1:1", "4:3", "3:4"]
        thumb_aspect_combo.grid(row=2, column=1, sticky=tk.W, padx=5)

        # Resolution
        ttk.Label(self.thumbnail_frame, text="Resolution:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.thumbnail_resolution_var = tk.StringVar(value=self.config.thumbnail_resolution)
        ttk.Entry(self.thumbnail_frame, textvariable=self.thumbnail_resolution_var, width=30).grid(row=3, column=1, sticky=tk.W, padx=5)

        # Clothing style
        ttk.Label(self.thumbnail_frame, text="Clothing style:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.thumbnail_clothing_var = tk.StringVar(value=self.config.thumbnail_clothing_style)
        clothing_combo = ttk.Combobox(self.thumbnail_frame, textvariable=self.thumbnail_clothing_var, width=50)
        clothing_combo['values'] = [
            "historically accurate medieval clothing",
            "modern casual clothing",
            "fantasy costume design",
            "period-appropriate formal wear",
            "stylized cartoon clothing"
        ]
        clothing_combo.grid(row=4, column=1, sticky=tk.W, padx=5)

    def create_preview_tab(self):
        """Create preview tab"""
        ttk.Label(self.preview_frame, text="Topic for preview:").pack(anchor=tk.W, pady=5)
        self.preview_topic_var = tk.StringVar(value="Medieval Court Jesters")
        ttk.Entry(self.preview_frame, textvariable=self.preview_topic_var, width=50).pack(anchor=tk.W, pady=5)

        ttk.Button(self.preview_frame, text="Generate Preview", command=self.generate_preview).pack(anchor=tk.W, pady=5)

        self.preview_text = scrolledtext.ScrolledText(self.preview_frame, wrap=tk.WORD, height=25)
        self.preview_text.pack(fill=tk.BOTH, expand=True, pady=5)

    def update_config_from_gui(self):
        """Update config object from GUI values"""
        # Title settings
        self.config.title_num_titles = self.title_num_var.get()
        self.config.title_max_hook_words = self.title_hook_words_var.get()
        self.config.title_include_runtime = self.title_runtime_var.get()
        self.config.title_tone = self.title_tone_var.get()

        regions_text = self.title_regions_var.get().strip()
        if regions_text:
            self.config.title_focus_regions = [r.strip() for r in regions_text.split(",")]
        else:
            self.config.title_focus_regions = None

        prefixes_text = self.title_prefixes_text.get(1.0, tk.END).strip()
        if prefixes_text:
            self.config.title_custom_prefixes = [p.strip() for p in prefixes_text.split("\n") if p.strip()]
        else:
            self.config.title_custom_prefixes = None

        # Script settings
        self.config.script_total_words = self.script_total_words_var.get()
        self.config.script_num_sections = self.script_num_sections_var.get()
        self.config.script_words_per_section = self.script_words_per_section_var.get()
        self.config.script_style = self.script_style_var.get()
        self.config.script_include_intro = self.script_intro_var.get()
        self.config.script_include_wind_down = self.script_wind_down_var.get()

        # Image settings
        self.config.image_art_style = self.image_art_style_var.get()
        self.config.image_medium = self.image_medium_var.get()
        self.config.image_perspective = self.image_perspective_var.get()
        self.config.image_aspect_ratio = self.image_aspect_var.get()
        self.config.image_resolution = self.image_resolution_var.get()
        self.config.image_color_palette = self.image_color_var.get()

        # Thumbnail settings
        self.config.thumbnail_style = self.thumbnail_style_var.get()
        self.config.thumbnail_background = self.thumbnail_bg_var.get()
        self.config.thumbnail_aspect_ratio = self.thumbnail_aspect_var.get()
        self.config.thumbnail_resolution = self.thumbnail_resolution_var.get()
        self.config.thumbnail_clothing_style = self.thumbnail_clothing_var.get()

    def generate_preview(self):
        """Generate preview of prompts with current settings"""
        self.update_config_from_gui()
        topic = self.preview_topic_var.get()

        preview_text = f"=== PREVIEW FOR TOPIC: {topic} ===\n\n"

        # Title prompt preview
        preview_text += "TITLE GENERATION PROMPT:\n"
        preview_text += "=" * 50 + "\n"
        preview_text += self.config.get_title_prompt(topic)
        preview_text += "\n\n"

        # Script prompt preview (first 500 chars)
        preview_text += "SCRIPT GENERATION PROMPT (first 500 chars):\n"
        preview_text += "=" * 50 + "\n"
        script_prompt = self.config.get_script_prompt(topic)
        preview_text += script_prompt[:500] + "...\n\n"

        # Image prompt preview
        preview_text += "IMAGE GENERATION PROMPT:\n"
        preview_text += "=" * 50 + "\n"
        sample_script = "You find yourself in a medieval castle courtyard. The jester approaches with his colorful outfit and jingling bells..."
        preview_text += self.config.get_image_prompt(sample_script, 1)
        preview_text += "\n\n"

        # Thumbnail prompt preview
        preview_text += "THUMBNAIL GENERATION PROMPT:\n"
        preview_text += "=" * 50 + "\n"
        preview_text += self.config.get_thumbnail_prompt("medieval jester with bells and colorful costume, laughing expression")

        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(1.0, preview_text)

    def save_config(self):
        """Save configuration to file"""
        self.update_config_from_gui()
        try:
            config_dict = self.config.save_to_dict()
            with open("template_config.json", "w", encoding="utf-8") as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            messagebox.showinfo("Success", "Configuration saved to template_config.json")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists("template_config.json"):
                with open("template_config.json", "r", encoding="utf-8") as f:
                    config_dict = json.load(f)
                self.config.load_from_dict(config_dict)
                self.update_gui_from_config()
        except Exception as e:
            print(f"Failed to load configuration: {e}")

    def update_gui_from_config(self):
        """Update GUI from config object"""
        try:
            # Update title settings
            if hasattr(self, 'title_num_var'):
                self.title_num_var.set(self.config.title_num_titles)
            if hasattr(self, 'title_hook_words_var'):
                self.title_hook_words_var.set(self.config.title_max_hook_words)
            if hasattr(self, 'title_runtime_var'):
                self.title_runtime_var.set(self.config.title_include_runtime)
            if hasattr(self, 'title_tone_var'):
                self.title_tone_var.set(self.config.title_tone)

            if hasattr(self, 'title_regions_var') and self.config.title_focus_regions:
                self.title_regions_var.set(", ".join(self.config.title_focus_regions))

            if hasattr(self, 'title_prefixes_text') and self.config.title_custom_prefixes:
                self.title_prefixes_text.delete(1.0, tk.END)
                self.title_prefixes_text.insert(1.0, "\n".join(self.config.title_custom_prefixes))

            # Update script settings
            if hasattr(self, 'script_total_words_var'):
                self.script_total_words_var.set(self.config.script_total_words)
            if hasattr(self, 'script_num_sections_var'):
                self.script_num_sections_var.set(self.config.script_num_sections)
            if hasattr(self, 'script_words_per_section_var'):
                self.script_words_per_section_var.set(self.config.script_words_per_section)
            if hasattr(self, 'script_style_var'):
                self.script_style_var.set(self.config.script_style)
            if hasattr(self, 'script_intro_var'):
                self.script_intro_var.set(self.config.script_include_intro)
            if hasattr(self, 'script_wind_down_var'):
                self.script_wind_down_var.set(self.config.script_include_wind_down)

            # Update image settings
            if hasattr(self, 'image_art_style_var'):
                self.image_art_style_var.set(self.config.image_art_style)
            if hasattr(self, 'image_medium_var'):
                self.image_medium_var.set(self.config.image_medium)
            if hasattr(self, 'image_perspective_var'):
                self.image_perspective_var.set(self.config.image_perspective)
            if hasattr(self, 'image_aspect_var'):
                self.image_aspect_var.set(self.config.image_aspect_ratio)
            if hasattr(self, 'image_resolution_var'):
                self.image_resolution_var.set(self.config.image_resolution)
            if hasattr(self, 'image_color_var'):
                self.image_color_var.set(self.config.image_color_palette)

            # Update thumbnail settings
            if hasattr(self, 'thumbnail_style_var'):
                self.thumbnail_style_var.set(self.config.thumbnail_style)
            if hasattr(self, 'thumbnail_bg_var'):
                self.thumbnail_bg_var.set(self.config.thumbnail_background)
            if hasattr(self, 'thumbnail_aspect_var'):
                self.thumbnail_aspect_var.set(self.config.thumbnail_aspect_ratio)
            if hasattr(self, 'thumbnail_resolution_var'):
                self.thumbnail_resolution_var.set(self.config.thumbnail_resolution)
            if hasattr(self, 'thumbnail_clothing_var'):
                self.thumbnail_clothing_var.set(self.config.thumbnail_clothing_style)

        except Exception as e:
            print(f"Error updating GUI from config: {e}")

    def on_language_change(self, event=None):
        """Handle language change"""
        try:
            language = self.language_var.get()
            self.config.language = language

            # Update style options based on language
            if hasattr(self, 'script_style_var'):
                if language == "vietnamese":
                    from vietnamese_templates import VIETNAMESE_SCRIPT_STYLES
                    self.script_style_combo['values'] = VIETNAMESE_SCRIPT_STYLES
                    if self.script_style_var.get() not in VIETNAMESE_SCRIPT_STYLES:
                        self.script_style_var.set(VIETNAMESE_SCRIPT_STYLES[0])
                else:
                    english_styles = [
                        "relaxed YouTube host + gentle sarcasm + sleepy ASMR cadence",
                        "professional documentary narrator + calm delivery",
                        "storytelling grandparent + warm and cozy",
                        "academic lecturer + measured pace + thoughtful pauses",
                        "bedtime story reader + soft whispers + gentle rhythm"
                    ]
                    self.script_style_combo['values'] = english_styles
                    if self.script_style_var.get() not in english_styles:
                        self.script_style_var.set(english_styles[0])

            # Update tone options
            if hasattr(self, 'title_tone_var'):
                if language == "vietnamese":
                    from vietnamese_templates import VIETNAMESE_TONE_OPTIONS
                    tone_combo = None
                    # Find the tone combobox and update it
                    for child in self.title_frame.winfo_children():
                        if isinstance(child, ttk.Combobox) and child.cget('textvariable') == str(self.title_tone_var):
                            tone_combo = child
                            break
                    if tone_combo:
                        tone_combo['values'] = VIETNAMESE_TONE_OPTIONS
                        if self.title_tone_var.get() not in VIETNAMESE_TONE_OPTIONS:
                            self.title_tone_var.set(VIETNAMESE_TONE_OPTIONS[0])

        except Exception as e:
            print(f"Error handling language change: {e}")

    def reset_defaults(self):
        """Reset to default configuration"""
        self.config = TemplateConfig()
        self.update_gui_from_config()
        messagebox.showinfo("Reset", "Configuration reset to defaults")

    def apply_and_close(self):
        """Apply configuration and close window"""
        try:
            self.update_config_from_gui()
            # Set a flag to indicate config was updated
            self.config_updated = True
            self.window.destroy()
        except Exception as e:
            print(f"Error in apply_and_close: {e}")
            try:
                self.window.destroy()
            except:
                pass

    def get_config(self):
        """Get the current configuration"""
        try:
            self.update_config_from_gui()
            return self.config
        except Exception as e:
            print(f"Error getting config: {e}")
            return self.config


def main():
    """Run the template configurator standalone"""
    app = TemplateConfigurator()
    app.window.mainloop()


if __name__ == "__main__":
    main()
