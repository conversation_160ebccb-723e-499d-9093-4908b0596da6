"""
Boring History Generator Templates
Contains all the prompt templates for generating content
"""

# Video Title Generation Template
TITLE_GENERATION_PROMPT = """
CHỦ ĐỀ
Role & Goal
Act as an experienced YouTube content strategist. Your sole task is to brainstorm fresh, clickable video‑topic ideas that match this style: slow‑paced, gently humorous historical storytelling designed to help viewers relax or fall asleep.

Output
Provide 5 unique video titles in a numbered list.
Each title must start with one of these prefixes (rotate them naturally):
"Boring History For Sleep | "
"The Boring History For Sleep | "
"Boring Greek Myths For Sleep | " (use sparingly for myth‑centric episodes)
"History Podcast For Sleep | " (use sparingly for food/ daily‑life angles)

After the pipe (|) give a concise, curiosity‑driven hook (≤ 14 words) teasing an obscure fact, vivid scenario, or surprising question.
When relevant, append runtime tags exactly as the audience expects, e.g. "(2 HOURS)", "(4 HOURS)", or "Gentle Storytelling & Ambient Sounds (2 HOURS)".

Content Criteria
Variety – Cover multiple eras (Ancient, Medieval, Early‑Modern, 19‑20th cent.), regions (Africa, Asia, Europe, Americas, Oceania), and themes (medicine, food, daily routines, punishments, gender roles, famous biographies, odd laws, forgotten inventions, lost cities, mythical creatures, etc.).
Tone – Titles should sound mildly tongue‑in‑cheek but not sensational; they must promise oddly soothing facts, gentle humor, and immersive "you‑are‑there" detail—never modern moral outrage or click‑bait shock.
Inclusivity & Freshness – At least one‑third of the list should highlight lesser‑known cultures or under‑represented figures.
Sleep‑Friendly – Avoid harsh words in the title ("horrific torture" → "peculiar punishments").
Keyword Logic – Work one or two SEO‑friendly nouns into each title ("silk routes," "Aztec gardens," "Victorian insomnia cures") while keeping the phrasing natural.

Topic: {topic}

Generate the list now.
"""

# Script Generation Template
SCRIPT_GENERATION_PROMPT = """
Role
You are my single‑speaker "Bed‑Time History" scriptwriter.

Global Specs  
One continuous, second‑person narrative on {topic}.  
Total length 15 000–16 500 words. 
Deliver in 15 numbered sections, 1 000–1 100 words EACH.  
No fresh intros between sections—narration must glide forward.

Two-Step Workflow  
Step 1 – 15-Bullet Outline  
Reply first with EXACTLY 15 bullets.  
Bullet n = Section n.  
≤ 8‑word mini‑title + one‑sentence teaser of what that section will cover.  

STOP. Wait for my "CONTINUE".

Step 2 – Section Delivery  
For every "CONTINUE" command:  
Look at the corresponding outline bullet again.  
Expand precisely that content—no drifting to later bullets.  
Write 1 000–1 100 words of seamless narration.  
Start with "Section n" heading, NO subtitle.  
End with:  

[Word count: ####]  
>>> Awaiting "CONTINUE"  
After Section 15, add the 300‑word wind‑down (see below) and finish with  
>>> End of script. Sweet dreams.

If you ever stray from the bullet‑plan, self‑correct before sending.

Introduction Template (150‑200 words, counts inside Section 1)  
Open exactly with:
Hey guys, tonight we …  
Hook the listener with vivid present‑tense imagery related to the topic.  
Drop one cheeky "you probably won't survive this"‑style reality check.  
Include this CTA verbatim:  
So, before you get comfortable, take a moment to like the video and subscribe—but only if you genuinely enjoy what I do here.  
Invite viewers to post their location & local time.  
Close with this sign‑off verbatim:  
Now, dim the lights, maybe turn on a fan for that soft background hum, and let's ease into tonight's journey together.  
Flow straight into the story—no extra headings.

Narration Style Rules  
✓ Second‑person present ("you trudge …").  
✓ Voice = relaxed YouTube host + gentle sarcasm + sleepy ASMR cadence.  
✓ Blend sensory detail, modern asides, and 3 light jokes per ±1 000 words.  
✓ Each section must contain:  
   – 1 mainstream historical fact  
   – 1 quirky or fringe tidbit  
   – 1 open scholarly debate phrase ("Historians still argue whether …").  
✓ Keep PG‑13; avoid explicit gore/profanity.  
✓ No citations or URLs; weave facts naturally.  
✓ Absolutely **no** new section intros like "In this chapter…"—just continue.

Structure & Continuity 
• Section headings = "Section 1", "Section 2", etc.  
• Use callbacks ("remember that crocodile‑dung sunscreen?") for cohesion.  
• Do NOT re‑introduce the topic at each section break.  
• Treat the outline as a contract—every bullet's promise must be fulfilled in its matching section.

300-Word Wind-Down (end of Section 15)  
• Slow the pacing, soften vocabulary, lengthen sentences.  
• Reassure the listener, fade the final imagery, and close on a calming whisper.

BEGIN NOW with STEP 1—the 15‑bullet outline ONLY.
"""

# Image Generation Template
IMAGE_GENERATION_PROMPT = """
You are a scene‑selector and medieval‑illumination prompt‑writer.
Goal
Generate exactly {num_images} highly‑detailed Leonardo AI‑ready prompts.
Each prompt must depict a different, visually distinct moment or setting from the script extract supplied below.
All prompts must share the same art‑direction: late‑15th‑century illuminated‑manuscript miniature (tempera & shell‑gold on vellum), flat medieval perspective, limited depth cues, subtle parchment texture, delicate brown‑ink outlines, tiny gilded flourishes, no modern objects or techniques.

Method
Read the script section.
Identify the most picturable beats (places, actions, or moods) that occur in chronological order.
Write one prompt per beat. Keep them varied—interiors vs. exteriors, day vs. night, different characters, seasons, or activities—so the final sequence feels like a visual storybook of the text.

Structure each prompt like this:
Prompt X: "Core scene description, key characters & actions, setting, mood, color accents — late‑15th‑century illuminated manuscript miniature, tempera and shell‑gold on vellum, flat medieval perspective, fine brown‑ink outlines, subtle parchment texture, 16:9 aspect, ultra‑high‑resolution (4K)"

Tips:
Use archaic clothing colors (indigo, russet, saffron, teal).
Mention small medieval props (wicker baskets, wooden buckets, iron fire‑tongs) when relevant.
If weather matters (snow, rain, harvest sun), weave it in.
Keep to one sentence per prompt; avoid camera jargon beyond "straight‑on" or "bird's‑eye."

Output Format
List each prompt on its own line starting with "Prompt 1: …", "Prompt 2: …", up to "{num_images}".
Do not add commentary, explanations, scene titles, or blank lines—only the prompts.

Script Section:
{script_section}
"""

# Thumbnail Generation Template
THUMBNAIL_GENERATION_PROMPT = """
You are a specialist prompt‑writer for crisp vector‑cartoon thumbnail art.
Produce one concise image prompt (and nothing else) that tells the model to render a modern, high‑contrast digital illustration in the style of the sample thumbnails (bold black outlines, flat comic‑book colours, minimal shading).

Style requirements
• Close‑up or waist‑up characters only (fill the frame; no scenery)
• Pure white background, no drop shadows
• Strong facial expressions (anger, terror, glee, etc.)
• Clean vector lines, solid colour fills, limited palette per figure
• Historically accurate medieval clothing and accessories appropriate to each character's role (tunics, wimples, leather aprons, linen caps, etc.)
• 9 : 16 aspect, ultra‑high‑resolution (4 K)

Depict exactly the character(s) and actions supplied below:
{character_action}

Return only the finished image prompt, nothing else.
"""
