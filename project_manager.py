"""
Project Manager for Channel Content Repurposer

This module handles project management:
- Creating and organizing project directories
- Tracking progress of processing
- Managing settings and configuration
- Handling file operations
"""

import os
import json
import time
import shutil
import datetime
from enum import Enum


class ProjectStatus(Enum):
    """Enum for project status"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class VideoStatus(Enum):
    """Enum for video processing status"""
    PENDING = "pending"
    TRANSCRIPT_DOWNLOADED = "transcript_downloaded"
    CONTENT_REWRITTEN = "content_rewritten"
    VOICE_GENERATED = "voice_generated"
    SUBTITLES_GENERATED = "subtitles_generated"
    VIDEO_CREATED = "video_created"
    UPLOADED = "uploaded"
    FAILED = "failed"


class ProjectManager:
    """Main class for project management"""

    def __init__(self, project_dir=None):
        """
        Initialize the Project Manager

        Args:
            project_dir (str, optional): Path to the project directory
        """
        self.project_dir = project_dir
        self.settings = {}
        self.videos = {}

        # Create project directory if specified
        if project_dir:
            self.create_project_structure(project_dir)
            self.load_settings()
            self.load_videos()

    def create_project_structure(self, project_dir):
        """
        Create the project directory structure

        Args:
            project_dir (str): Path to the project directory

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Set project directory
            self.project_dir = project_dir

            # Create main project directory
            os.makedirs(project_dir, exist_ok=True)

            # Create subdirectories
            subdirs = [
                "transcripts",    # Original transcripts
                "rewritten",      # Rewritten content
                "audio",          # Generated voice audio
                "subtitles",      # Generated subtitles
                "videos",         # Final videos
                "temp",           # Temporary files
                "logs"            # Log files
            ]

            for subdir in subdirs:
                os.makedirs(os.path.join(project_dir, subdir), exist_ok=True)

            # Create default settings file if it doesn't exist
            settings_file = os.path.join(project_dir, "settings.json")
            if not os.path.exists(settings_file):
                default_settings = {
                    "project_name": os.path.basename(project_dir),
                    "created_at": datetime.datetime.now().isoformat(),
                    "status": ProjectStatus.NOT_STARTED.value,
                    "settings": {
                        "bg_video": "",
                        "openai_api_key": "",
                        "tts_api_key": "",
                        "tts_voice": "Default",
                        "add_intro": True,
                        "add_outro": True,
                        "loop_video": True
                    }
                }

                with open(settings_file, 'w', encoding='utf-8') as f:
                    json.dump(default_settings, f, indent=4)

                self.settings = default_settings

            # Create videos tracking file if it doesn't exist
            videos_file = os.path.join(project_dir, "videos.json")
            if not os.path.exists(videos_file):
                default_videos = {
                    "videos": []
                }

                with open(videos_file, 'w', encoding='utf-8') as f:
                    json.dump(default_videos, f, indent=4)

                self.videos = default_videos

            return True

        except Exception as e:
            print(f"Error creating project structure: {str(e)}")
            return False

    def load_settings(self):
        """
        Load project settings

        Returns:
            dict: Project settings
        """
        if not self.project_dir:
            print("Project directory not set")
            return {}

        settings_file = os.path.join(self.project_dir, "settings.json")
        if not os.path.exists(settings_file):
            print(f"Settings file not found: {settings_file}")
            return {}

        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                self.settings = json.load(f)

            return self.settings

        except Exception as e:
            print(f"Error loading settings: {str(e)}")
            return {}

    def save_settings(self, settings=None):
        """
        Save project settings

        Args:
            settings (dict, optional): Settings to save

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.project_dir:
            print("Project directory not set")
            return False

        settings_file = os.path.join(self.project_dir, "settings.json")

        try:
            # Use provided settings or current settings
            data = settings if settings else self.settings

            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4)

            self.settings = data
            return True

        except Exception as e:
            print(f"Error saving settings: {str(e)}")
            return False

    def load_videos(self):
        """
        Load videos tracking data

        Returns:
            dict: Videos tracking data
        """
        if not self.project_dir:
            print("Project directory not set")
            return {}

        videos_file = os.path.join(self.project_dir, "videos.json")
        if not os.path.exists(videos_file):
            print(f"Videos file not found: {videos_file}")
            return {}

        try:
            with open(videos_file, 'r', encoding='utf-8') as f:
                self.videos = json.load(f)

            return self.videos

        except Exception as e:
            print(f"Error loading videos data: {str(e)}")
            return {}

    def save_videos(self, videos=None):
        """
        Save videos tracking data

        Args:
            videos (dict, optional): Videos data to save

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.project_dir:
            print("Project directory not set")
            return False

        videos_file = os.path.join(self.project_dir, "videos.json")

        try:
            # Use provided videos data or current data
            data = videos if videos else self.videos

            with open(videos_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4)

            self.videos = data
            return True

        except Exception as e:
            print(f"Error saving videos data: {str(e)}")
            return False

    def add_video(self, video_id, title, url=None):
        """
        Add a video to the project

        Args:
            video_id (str): YouTube video ID
            title (str): Video title
            url (str, optional): Video URL

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.project_dir:
            print("Project directory not set")
            return False

        try:
            # Load current videos data
            self.load_videos()

            # Check if video already exists
            for video in self.videos.get("videos", []):
                if video.get("id") == video_id:
                    print(f"Video already exists: {video_id}")
                    return False

            # Create video entry
            video_entry = {
                "id": video_id,
                "title": title,
                "url": url or f"https://www.youtube.com/watch?v={video_id}",
                "status": VideoStatus.PENDING.value,
                "added_at": datetime.datetime.now().isoformat(),
                "updated_at": datetime.datetime.now().isoformat(),
                "files": {
                    "transcript": "",
                    "rewritten": "",
                    "audio": "",
                    "subtitles": "",
                    "video": ""
                }
            }

            # Add to videos list
            if "videos" not in self.videos:
                self.videos["videos"] = []

            self.videos["videos"].append(video_entry)

            # Save videos data
            return self.save_videos()

        except Exception as e:
            print(f"Error adding video: {str(e)}")
            return False

    def update_video_status(self, video_id, status, files=None):
        """
        Update video status and files

        Args:
            video_id (str): YouTube video ID
            status (VideoStatus): New status
            files (dict, optional): Updated file paths

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.project_dir:
            print("Project directory not set")
            return False

        try:
            # Load current videos data
            self.load_videos()

            # Find the video
            for i, video in enumerate(self.videos.get("videos", [])):
                if video.get("id") == video_id:
                    # Update status
                    self.videos["videos"][i]["status"] = status.value
                    self.videos["videos"][i]["updated_at"] = datetime.datetime.now().isoformat()

                    # Update files if provided
                    if files:
                        for key, value in files.items():
                            if key in self.videos["videos"][i]["files"]:
                                self.videos["videos"][i]["files"][key] = value

                    # Save videos data
                    return self.save_videos()

            print(f"Video not found: {video_id}")
            return False

        except Exception as e:
            print(f"Error updating video status: {str(e)}")
            return False

    def get_video(self, video_id):
        """
        Get video data by ID

        Args:
            video_id (str): YouTube video ID

        Returns:
            dict: Video data or None if not found
        """
        if not self.project_dir:
            print("Project directory not set")
            return None

        # Load current videos data
        self.load_videos()

        # Find the video
        for video in self.videos.get("videos", []):
            if video.get("id") == video_id:
                return video

        return None

    def get_videos_by_status(self, status):
        """
        Get videos with a specific status

        Args:
            status (VideoStatus): Status to filter by

        Returns:
            list: List of videos with the specified status
        """
        if not self.project_dir:
            print("Project directory not set")
            return []

        # Load current videos data
        self.load_videos()

        # Filter videos by status
        return [video for video in self.videos.get("videos", [])
                if video.get("status") == status.value]

    def get_project_status(self):
        """
        Get overall project status

        Returns:
            ProjectStatus: Current project status
        """
        if not self.project_dir:
            print("Project directory not set")
            return ProjectStatus.NOT_STARTED

        # Load current settings
        self.load_settings()

        # Get status from settings
        status_value = self.settings.get("status", ProjectStatus.NOT_STARTED.value)

        # Convert string to enum
        for status in ProjectStatus:
            if status.value == status_value:
                return status

        return ProjectStatus.NOT_STARTED

    def update_project_status(self, status):
        """
        Update project status

        Args:
            status (ProjectStatus): New status

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.project_dir:
            print("Project directory not set")
            return False

        try:
            # Load current settings
            self.load_settings()

            # Update status
            self.settings["status"] = status.value

            # Save settings
            return self.save_settings()

        except Exception as e:
            print(f"Error updating project status: {str(e)}")
            return False

    def get_file_path(self, video_id, file_type):
        """
        Get file path for a specific video and file type

        Args:
            video_id (str): YouTube video ID
            file_type (str): File type (transcript, rewritten, audio, subtitles, video)

        Returns:
            str: File path or None if not found
        """
        if not self.project_dir:
            print("Project directory not set")
            return None

        # Define file extensions
        extensions = {
            "transcript": "txt",
            "rewritten": "txt",
            "audio": "mp3",
            "subtitles": "srt",
            "video": "mp4"
        }

        # Check if file type is valid
        if file_type not in extensions:
            print(f"Invalid file type: {file_type}")
            return None

        # Get video data
        video = self.get_video(video_id)
        if not video:
            print(f"Video not found: {video_id}")
            return None

        # Check if file path is already set
        if video["files"].get(file_type):
            return video["files"][file_type]

        # Generate default file path
        return os.path.join(self.project_dir, file_type + "s", f"{video_id}.{extensions[file_type]}")

    def save_file(self, video_id, file_type, content, update_status=True):
        """
        Save file content for a specific video and file type

        Args:
            video_id (str): YouTube video ID
            file_type (str): File type (transcript, rewritten, audio, subtitles)
            content (str or bytes): File content
            update_status (bool, optional): Whether to update video status

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.project_dir:
            print("Project directory not set")
            return False

        # Get file path
        file_path = self.get_file_path(video_id, file_type)
        if not file_path:
            return False

        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Write content to file
            mode = 'wb' if isinstance(content, bytes) else 'w'
            encoding = None if isinstance(content, bytes) else 'utf-8'

            with open(file_path, mode, encoding=encoding) as f:
                f.write(content)

            # Update video files and status
            if update_status:
                files = {file_type: file_path}

                # Determine new status based on file type
                status_map = {
                    "transcript": VideoStatus.TRANSCRIPT_DOWNLOADED,
                    "rewritten": VideoStatus.CONTENT_REWRITTEN,
                    "audio": VideoStatus.VOICE_GENERATED,
                    "subtitles": VideoStatus.SUBTITLES_GENERATED,
                    "video": VideoStatus.VIDEO_CREATED
                }

                status = status_map.get(file_type, None)
                if status:
                    self.update_video_status(video_id, status, files)

            return True

        except Exception as e:
            print(f"Error saving file: {str(e)}")
            return False

    def load_file(self, video_id, file_type, as_binary=False):
        """
        Load file content for a specific video and file type

        Args:
            video_id (str): YouTube video ID
            file_type (str): File type (transcript, rewritten, audio, subtitles, video)
            as_binary (bool, optional): Whether to load as binary

        Returns:
            str or bytes: File content or None if not found
        """
        if not self.project_dir:
            print("Project directory not set")
            return None

        # Get file path
        file_path = self.get_file_path(video_id, file_type)
        if not file_path or not os.path.exists(file_path):
            return None

        try:
            # Read file content
            mode = 'rb' if as_binary else 'r'
            encoding = None if as_binary else 'utf-8'

            with open(file_path, mode, encoding=encoding) as f:
                return f.read()

        except Exception as e:
            print(f"Error loading file: {str(e)}")
            return None

    def delete_video(self, video_id, delete_files=False):
        """
        Delete a video from the project

        Args:
            video_id (str): YouTube video ID
            delete_files (bool, optional): Whether to delete associated files

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.project_dir:
            print("Project directory not set")
            return False

        try:
            # Load current videos data
            self.load_videos()

            # Find the video
            video_index = None
            for i, video in enumerate(self.videos.get("videos", [])):
                if video.get("id") == video_id:
                    video_index = i
                    break

            if video_index is None:
                print(f"Video not found: {video_id}")
                return False

            # Delete associated files if requested
            if delete_files:
                video = self.videos["videos"][video_index]
                for file_type, file_path in video["files"].items():
                    if file_path and os.path.exists(file_path):
                        os.remove(file_path)

            # Remove video from list
            del self.videos["videos"][video_index]

            # Save videos data
            return self.save_videos()

        except Exception as e:
            print(f"Error deleting video: {str(e)}")
            return False

    def export_project(self, output_dir):
        """
        Export project to a different directory

        Args:
            output_dir (str): Output directory

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.project_dir:
            print("Project directory not set")
            return False

        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                print(f"Error creating output directory: {str(e)}")
                return False

        try:
            # Copy project structure
            subdirs = [
                "transcripts",
                "rewritten",
                "audio",
                "subtitles",
                "videos",
                "logs"
            ]

            for subdir in subdirs:
                src_dir = os.path.join(self.project_dir, subdir)
                dst_dir = os.path.join(output_dir, subdir)

                if os.path.exists(src_dir):
                    os.makedirs(dst_dir, exist_ok=True)

                    # Copy files
                    for file_name in os.listdir(src_dir):
                        src_file = os.path.join(src_dir, file_name)
                        dst_file = os.path.join(dst_dir, file_name)

                        if os.path.isfile(src_file):
                            shutil.copy2(src_file, dst_file)

            # Copy settings and videos data
            shutil.copy2(os.path.join(self.project_dir, "settings.json"),
                        os.path.join(output_dir, "settings.json"))

            shutil.copy2(os.path.join(self.project_dir, "videos.json"),
                        os.path.join(output_dir, "videos.json"))

            return True

        except Exception as e:
            print(f"Error exporting project: {str(e)}")
            return False