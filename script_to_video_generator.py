"""
Script to Video Generator

This application takes a script as input, uses ElevenLabs API to generate voice narration,
and OpenAI API to create images for each line of the script. It then synchronizes the images
with the audio to create a video.

Features:
- Script input and parsing
- ElevenLabs TTS integration
- OpenAI image generation with minimalist black and white stick figure style
- Image-audio synchronization
- Video export
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
import time
import requests
from datetime import datetime
from dotenv import load_dotenv
import shutil

# Load environment variables from .env file if it exists
load_dotenv()

# Import required packages
from openai import OpenAI
import elevenlabs
from elevenlabs.client import ElevenLabs
import moviepy.editor as mp


class ScriptToVideoGenerator:
    """Main application class for Script to Video Generator"""

    def __init__(self, root):
        """Initialize the application"""
        self.root = root
        self.root.title("Script to Video Generator")
        self.root.geometry("800x700")
        self.root.resizable(True, True)

        # Set style
        self.style = ttk.Style()
        self.style.configure("TButton", padding=6, relief="flat", background="#ccc")
        self.style.configure("TLabel", padding=6)
        self.style.configure("TEntry", padding=6)

        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create notebook (tabs)
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create tabs
        self.setup_tab = ttk.Frame(self.notebook)
        self.script_tab = ttk.Frame(self.notebook)
        self.generate_tab = ttk.Frame(self.notebook)

        self.notebook.add(self.setup_tab, text="Setup")
        self.notebook.add(self.script_tab, text="Script")
        self.notebook.add(self.generate_tab, text="Generate")

        # Setup each tab
        self.setup_setup_tab()
        self.setup_script_tab()
        self.setup_generate_tab()

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Initialize variables
        self.openai_client = None
        self.elevenlabs_voices = []
        self.temp_dir = None
        self.load_settings()

    def setup_setup_tab(self):
        """Setup the Setup tab"""
        # API Keys section
        api_frame = ttk.LabelFrame(self.setup_tab, text="API Keys")
        api_frame.pack(fill=tk.X, padx=5, pady=5)

        # OpenAI API Key
        openai_label = ttk.Label(api_frame, text="OpenAI API Key:")
        openai_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.openai_api_key_var = tk.StringVar()
        openai_entry = ttk.Entry(api_frame, textvariable=self.openai_api_key_var, width=40, show="*")
        openai_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # OpenAI Base URL
        openai_base_url_label = ttk.Label(api_frame, text="OpenAI Base URL:")
        openai_base_url_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)

        self.openai_base_url_var = tk.StringVar(value="https://api.openai.com/v1")
        openai_base_url_entry = ttk.Entry(api_frame, textvariable=self.openai_base_url_var, width=40)
        openai_base_url_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        # ElevenLabs API Key
        elevenlabs_label = ttk.Label(api_frame, text="ElevenLabs API Key:")
        elevenlabs_label.grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)

        self.elevenlabs_api_key_var = tk.StringVar()
        elevenlabs_entry = ttk.Entry(api_frame, textvariable=self.elevenlabs_api_key_var, width=40, show="*")
        elevenlabs_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # Voice selection
        voice_frame = ttk.LabelFrame(self.setup_tab, text="Voice Settings")
        voice_frame.pack(fill=tk.X, padx=5, pady=5)

        voice_label = ttk.Label(voice_frame, text="ElevenLabs Voice:")
        voice_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.voice_var = tk.StringVar()
        self.voice_dropdown = ttk.Combobox(voice_frame, textvariable=self.voice_var, width=30)
        self.voice_dropdown.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.voice_dropdown['values'] = ["Default Voice"]
        self.voice_dropdown.current(0)

        # Refresh voices button
        refresh_button = ttk.Button(voice_frame, text="Refresh Voices", command=self.refresh_voices)
        refresh_button.grid(row=0, column=2, padx=5, pady=5)

        # Output directory
        output_frame = ttk.LabelFrame(self.setup_tab, text="Output Settings")
        output_frame.pack(fill=tk.X, padx=5, pady=5)

        output_label = ttk.Label(output_frame, text="Output Directory:")
        output_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.output_dir_var = tk.StringVar()
        output_entry = ttk.Entry(output_frame, textvariable=self.output_dir_var, width=40)
        output_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        browse_button = ttk.Button(output_frame, text="Browse", command=self.browse_output_dir)
        browse_button.grid(row=0, column=2, padx=5, pady=5)

        # Buttons frame
        buttons_frame = ttk.Frame(self.setup_tab)
        buttons_frame.pack(fill=tk.X, padx=5, pady=10)

        # Save settings button
        save_button = ttk.Button(buttons_frame, text="Save Settings", command=self.save_settings)
        save_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Check dependencies button
        check_deps_button = ttk.Button(buttons_frame, text="Check Dependencies", command=self.check_dependencies_ui)
        check_deps_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Install required packages button
        install_deps_button = ttk.Button(buttons_frame, text="Install Required Packages", command=self.install_required_packages)
        install_deps_button.pack(side=tk.LEFT, padx=5, pady=5)

    def setup_script_tab(self):
        """Setup the Script tab"""
        # Script input
        script_frame = ttk.LabelFrame(self.script_tab, text="Script Input")
        script_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Script text area
        self.script_text = scrolledtext.ScrolledText(script_frame, wrap=tk.WORD, height=20)
        self.script_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Load script button
        load_button = ttk.Button(self.script_tab, text="Load Script from File", command=self.load_script)
        load_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Save script button
        save_button = ttk.Button(self.script_tab, text="Save Script to File", command=self.save_script)
        save_button.pack(side=tk.LEFT, padx=5, pady=5)

        # Clear script button
        clear_button = ttk.Button(self.script_tab, text="Clear Script", command=self.clear_script)
        clear_button.pack(side=tk.LEFT, padx=5, pady=5)

    def setup_generate_tab(self):
        """Setup the Generate tab"""
        # Options frame
        options_frame = ttk.LabelFrame(self.generate_tab, text="Generation Options")
        options_frame.pack(fill=tk.X, padx=5, pady=5)

        # Image resolution
        resolution_label = ttk.Label(options_frame, text="Image Resolution:")
        resolution_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        self.resolution_var = tk.StringVar(value="1792x1024")
        resolution_dropdown = ttk.Combobox(options_frame, textvariable=self.resolution_var, width=15)
        resolution_dropdown['values'] = ["1024x1024", "1024x1792", "1792x1024"]
        resolution_dropdown.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Add resolution info label
        resolution_info = ttk.Label(options_frame, text="(DALL-E 3 supported resolutions)")
        resolution_info.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)

        # Add option to use ChatGPT for prompt generation
        self.use_chatgpt_var = tk.BooleanVar(value=True)
        chatgpt_check = ttk.Checkbutton(options_frame, text="Use ChatGPT to enhance image prompts",
                                        variable=self.use_chatgpt_var)
        chatgpt_check.grid(row=1, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)

        # Add ChatGPT model selection
        chatgpt_model_label = ttk.Label(options_frame, text="ChatGPT Model:")
        chatgpt_model_label.grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)

        self.chatgpt_model_var = tk.StringVar(value="gpt-4o")
        chatgpt_model_dropdown = ttk.Combobox(options_frame, textvariable=self.chatgpt_model_var, width=15)
        chatgpt_model_dropdown['values'] = ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"]
        chatgpt_model_dropdown.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        # Add number of images option
        num_images_label = ttk.Label(options_frame, text="Number of Images:")
        num_images_label.grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)

        self.num_images_var = tk.IntVar(value=0)
        num_images_spinbox = ttk.Spinbox(options_frame, from_=0, to=100, textvariable=self.num_images_var, width=5)
        num_images_spinbox.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        # Add help text for number of images
        num_images_help = ttk.Label(options_frame, text="(0 = auto based on line breaks, -1 = use all detected segments, 1+ = specific number)")
        num_images_help.grid(row=3, column=2, sticky=tk.W, padx=5, pady=5)

        # Add option to use all detected segments
        self.use_all_segments_var = tk.BooleanVar(value=False)
        use_all_segments_check = ttk.Checkbutton(options_frame, text="Use all detected segments (no combining)",
                                        variable=self.use_all_segments_var)
        use_all_segments_check.grid(row=4, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)

        # Add script splitting method option
        split_method_label = ttk.Label(options_frame, text="Script Splitting Method:")
        split_method_label.grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)

        self.split_method_var = tk.StringVar(value="chatgpt")
        split_method_dropdown = ttk.Combobox(options_frame, textvariable=self.split_method_var, width=15)
        split_method_dropdown['values'] = ["chatgpt", "punctuation", "sentences", "paragraphs", "equal_length"]
        split_method_dropdown.grid(row=5, column=1, sticky=tk.W, padx=5, pady=5)

        # Add help text for splitting method
        split_method_help = ttk.Label(options_frame, text="(How to divide the script into segments for images)")
        split_method_help.grid(row=5, column=2, sticky=tk.W, padx=5, pady=5)

        # Add option to use existing audio file
        self.use_existing_audio_var = tk.BooleanVar(value=False)
        use_existing_audio_check = ttk.Checkbutton(options_frame, text="Use existing audio file instead of generating TTS",
                                        variable=self.use_existing_audio_var, command=self.toggle_audio_file_selection)
        use_existing_audio_check.grid(row=6, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)

        # Audio file selection frame (initially hidden)
        self.audio_file_frame = ttk.Frame(options_frame)
        self.audio_file_frame.grid(row=7, column=0, columnspan=3, sticky=tk.W, padx=20, pady=5)
        self.audio_file_frame.grid_remove()  # Hide initially

        # Audio file path
        self.audio_file_var = tk.StringVar()
        audio_file_entry = ttk.Entry(self.audio_file_frame, textvariable=self.audio_file_var, width=40)
        audio_file_entry.pack(side=tk.LEFT, padx=5)

        # Browse button for audio file
        browse_audio_button = ttk.Button(self.audio_file_frame, text="Browse", command=self.browse_audio_file)
        browse_audio_button.pack(side=tk.LEFT, padx=5)

        # Add advanced options button
        advanced_button = ttk.Button(options_frame, text="Advanced Options", command=self.show_advanced_options)
        advanced_button.grid(row=8, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)

        # Buttons frame
        buttons_frame = ttk.Frame(self.generate_tab)
        buttons_frame.pack(padx=5, pady=10)

        # Generate button
        generate_button = ttk.Button(buttons_frame, text="Generate Video", command=self.generate_video)
        generate_button.pack(side=tk.LEFT, padx=5)

        # View prompts button
        self.view_prompts_button = ttk.Button(buttons_frame, text="View Generated Prompts",
                                             command=self.view_generated_prompts, state=tk.DISABLED)
        self.view_prompts_button.pack(side=tk.LEFT, padx=5)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.generate_tab, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=5, pady=5)

        # Log area
        log_frame = ttk.LabelFrame(self.generate_tab, text="Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory()
        if directory:
            self.output_dir_var.set(directory)

    def browse_audio_file(self):
        """Browse for audio file"""
        file_path = filedialog.askopenfilename(filetypes=[
            ("Audio Files", "*.mp3 *.wav *.ogg *.m4a *.aac"),
            ("All Files", "*.*")
        ])
        if file_path:
            self.audio_file_var.set(file_path)
            self.log_message(f"Selected audio file: {file_path}")

    def toggle_audio_file_selection(self):
        """Show or hide the audio file selection frame based on checkbox state"""
        if self.use_existing_audio_var.get():
            self.audio_file_frame.grid()  # Show the frame
            self.log_message("Using existing audio file enabled")
        else:
            self.audio_file_frame.grid_remove()  # Hide the frame
            self.log_message("Using TTS generation")

    def load_script(self):
        """Load script from file"""
        file_path = filedialog.askopenfilename(filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")])
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.script_text.delete(1.0, tk.END)
                    self.script_text.insert(tk.END, content)
                self.log_message(f"Loaded script from {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load script: {str(e)}")

    def save_script(self):
        """Save script to file"""
        file_path = filedialog.asksaveasfilename(defaultextension=".txt", filetypes=[("Text Files", "*.txt"), ("All Files", "*.*")])
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.script_text.get(1.0, tk.END))
                self.log_message(f"Saved script to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save script: {str(e)}")

    def clear_script(self):
        """Clear the script text area"""
        if messagebox.askyesno("Confirm", "Are you sure you want to clear the script?"):
            self.script_text.delete(1.0, tk.END)

    def get_settings_dir(self):
        """Get the directory where settings should be stored"""
        # Use AppData on Windows, or ~/.config on Linux/Mac
        if os.name == 'nt':  # Windows
            app_data = os.environ.get('APPDATA', '')
            if not app_data:
                # Fallback to user home directory
                app_data = os.path.expanduser('~')
            settings_dir = os.path.join(app_data, 'ScriptToVideoGenerator')
        else:  # Linux/Mac
            settings_dir = os.path.join(os.path.expanduser('~'), '.config', 'ScriptToVideoGenerator')

        # Create the directory if it doesn't exist
        os.makedirs(settings_dir, exist_ok=True)
        return settings_dir

    def load_settings(self):
        """Load settings from file"""
        # First try to load from the application directory (for backward compatibility)
        app_dir_settings = os.path.join(os.path.dirname(os.path.abspath(__file__)), "settings.json")
        settings_dir = self.get_settings_dir()
        settings_file = os.path.join(settings_dir, "settings.json")

        # Check if settings exist in app directory but not in user directory
        if os.path.exists(app_dir_settings) and not os.path.exists(settings_file):
            try:
                # Copy settings from app directory to user directory
                with open(app_dir_settings, 'r', encoding='utf-8') as src:
                    with open(settings_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
                self.log_message(f"Migrated settings to {settings_file}")
            except Exception as e:
                self.log_message(f"Failed to migrate settings: {str(e)}")

        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                    # Load API keys
                    self.openai_api_key_var.set(settings.get("openai_api_key", ""))
                    self.elevenlabs_api_key_var.set(settings.get("elevenlabs_api_key", ""))

                    # Load OpenAI Base URL
                    if "openai_base_url" in settings:
                        self.openai_base_url_var.set(settings.get("openai_base_url"))
                    else:
                        self.openai_base_url_var.set("https://api.openai.com/v1")

                    # Load output directory
                    self.output_dir_var.set(settings.get("output_dir", ""))

                    # Load voice
                    self.voice_var.set(settings.get("voice", "Default Voice"))

                    # Initialize API clients
                    self.initialize_apis()

                self.log_message(f"Settings loaded from {settings_file}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to load settings: {str(e)}")

    def save_settings(self):
        """Save settings to file"""
        settings_dir = self.get_settings_dir()
        settings_file = os.path.join(settings_dir, "settings.json")
        try:
            settings = {
                "openai_api_key": self.openai_api_key_var.get(),
                "elevenlabs_api_key": self.elevenlabs_api_key_var.get(),
                "openai_base_url": self.openai_base_url_var.get(),
                "output_dir": self.output_dir_var.get(),
                "voice": self.voice_var.get()
            }

            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4)

            self.log_message(f"Settings saved to {settings_file}")

            # Initialize API clients
            self.initialize_apis()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")

    def install_required_packages(self):
        """Install required packages"""
        # Create a dialog to show installation progress
        dialog = tk.Toplevel(self.root)
        dialog.title("Installing Required Packages")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # Create a scrolled text widget to show results
        result_text = scrolledtext.ScrolledText(dialog, wrap=tk.WORD)
        result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Redirect stdout to the text widget
        import sys

        class TextWidgetRedirector:
            def __init__(self, text_widget):
                self.text_widget = text_widget

            def write(self, string):
                self.text_widget.insert(tk.END, string)
                self.text_widget.see(tk.END)

            def flush(self):
                pass

        old_stdout = sys.stdout
        sys.stdout = TextWidgetRedirector(result_text)

        # Run installation
        try:
            print("Installing required packages...\n")

            # Import required modules for installation
            import subprocess

            # List of required packages
            required_packages = [
                "pillow",
                "python-dotenv",
                "elevenlabs>=1.0.0",
                "openai",
                "moviepy",
                "requests"
            ]

            # Install packages
            for package in required_packages:
                print(f"Installing {package}...")
                try:
                    result = subprocess.run(
                        [sys.executable, "-m", "pip", "install", "--upgrade", package],
                        capture_output=True,
                        text=True
                    )
                    print(result.stdout)
                    if result.stderr and "WARNING" not in result.stderr:
                        print(f"Errors: {result.stderr}")
                    print(f"Successfully installed {package}")
                except Exception as e:
                    print(f"Error installing {package}: {str(e)}")

            print("\nInstallation complete. Please restart the application.")

        except Exception as e:
            print(f"Error during installation: {str(e)}")

        finally:
            # Restore stdout
            sys.stdout = old_stdout

        # Add close button
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        close_button = ttk.Button(button_frame, text="Close", command=dialog.destroy)
        close_button.pack(side=tk.RIGHT, padx=5)

    def check_dependencies_ui(self):
        """Check dependencies from UI"""
        # Create a dialog to show dependency check results
        dialog = tk.Toplevel(self.root)
        dialog.title("Dependency Check")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # Create a scrolled text widget to show results
        result_text = scrolledtext.ScrolledText(dialog, wrap=tk.WORD)
        result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Redirect stdout to the text widget
        import sys

        class TextWidgetRedirector:
            def __init__(self, text_widget):
                self.text_widget = text_widget

            def write(self, string):
                self.text_widget.insert(tk.END, string)
                self.text_widget.see(tk.END)

            def flush(self):
                pass

        old_stdout = sys.stdout
        sys.stdout = TextWidgetRedirector(result_text)

        # Run dependency check
        try:
            print("Checking dependencies...\n")

            # Import required modules for checking
            import subprocess
            import platform

            # Check Python version
            python_version = sys.version_info
            print(f"Python version: {platform.python_version()}")

            if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
                print("Warning: This application requires Python 3.7 or higher.")
                print(f"Current Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")

            # Print pip version and location
            try:
                pip_version = subprocess.check_output([sys.executable, "-m", "pip", "--version"], text=True)
                print(f"Pip information: {pip_version.strip()}")
            except Exception as e:
                print(f"Error checking pip version: {str(e)}")

            # Check packages
            packages_to_check = {
                "openai": "OpenAI API",
                "elevenlabs": "ElevenLabs API",
                "moviepy": "Video processing",
                "pillow": "Image processing",
                "requests": "HTTP requests",
                "python-dotenv": "Environment variables"
            }

            print("\nChecking installed packages:")
            missing_packages = []

            # Special check for elevenlabs
            try:
                import elevenlabs
                print(f"✓ elevenlabs is installed (version: {elevenlabs.__version__})")

                # Check if the required functions are available in the new API
                try:
                    # Test basic functionality
                    elevenlabs.api_key = "test_key"  # Just for testing, not making actual API calls

                    # Check if generate function exists
                    if hasattr(elevenlabs, "generate"):
                        print("  ✓ elevenlabs.generate function is available (legacy API)")
                    else:
                        print("  ✗ elevenlabs.generate function is not available")
                        print("  ℹ This is normal for newer versions of elevenlabs")

                    # Check if voices module exists
                    if hasattr(elevenlabs, "voices"):
                        print("  ✓ elevenlabs.voices module is available")

                        # Check if we can get voices
                        try:
                            # Try different API versions
                            if hasattr(elevenlabs.voices, "get_all"):
                                print("  ✓ elevenlabs.voices.get_all() is available")
                                try:
                                    voices = elevenlabs.voices.get_all()
                                    print(f"  ✓ Found {len(voices)} voices")
                                except Exception as e:
                                    print(f"  ✗ Error calling elevenlabs.voices.get_all(): {str(e)}")
                            elif hasattr(elevenlabs, "voices") and callable(elevenlabs.voices):
                                print("  ✓ elevenlabs.voices() is available")
                                try:
                                    voices = elevenlabs.voices()
                                    print(f"  ✓ Found {len(voices)} voices")
                                except Exception as e:
                                    print(f"  ✗ Error calling elevenlabs.voices(): {str(e)}")
                            else:
                                print("  ✓ elevenlabs.voices attribute is available")
                                try:
                                    voices = elevenlabs.voices
                                    if isinstance(voices, list):
                                        print(f"  ✓ Found {len(voices)} voices")
                                    else:
                                        print(f"  ✗ elevenlabs.voices is not a list: {type(voices)}")
                                except Exception as e:
                                    print(f"  ✗ Error accessing elevenlabs.voices: {str(e)}")

                            # Check for Voice class
                            if hasattr(elevenlabs, "Voice"):
                                print("  ✓ elevenlabs.Voice class is available")
                            else:
                                print("  ✗ elevenlabs.Voice class is not available")
                        except Exception as e:
                            print(f"  ✗ Error checking voices: {str(e)}")
                    else:
                        print("  ✗ elevenlabs.voices module is not available")

                    # All checks passed
                    print("  ✓ ElevenLabs API appears to be compatible")
                except Exception as e:
                    print(f"  ✗ Error testing elevenlabs API: {str(e)}")
                    print("  This might be due to an outdated version. Try: pip install --upgrade elevenlabs")
                    missing_packages.append("elevenlabs")
            except ImportError:
                print("✗ elevenlabs is not installed")
                missing_packages.append("elevenlabs")
            except Exception as e:
                print(f"✗ Error checking elevenlabs: {str(e)}")
                missing_packages.append("elevenlabs")

            # Check other packages
            for package, description in packages_to_check.items():
                if package == "elevenlabs":  # Skip elevenlabs as we already checked it
                    continue

                try:
                    module = __import__(package)
                    version = getattr(module, "__version__", "unknown")
                    print(f"✓ {package} is installed (version: {version}) - {description}")
                except ImportError:
                    print(f"✗ {package} is not installed - {description}")
                    missing_packages.append(package)

            if missing_packages:
                print("\nMissing packages:")
                for package in missing_packages:
                    print(f"- {package}")

                print("\nInstallation commands:")
                if "elevenlabs" in missing_packages:
                    print("pip install --upgrade elevenlabs>=1.0.0")

                for package in missing_packages:
                    if package != "elevenlabs":
                        print(f"pip install {package}")

                # Add install button
                def install_packages():
                    dialog.title("Installing Packages")
                    print("\nInstalling missing packages...")

                    # Import subprocess for installation
                    import subprocess
                    import sys

                    # Install elevenlabs with specific version if needed
                    if "elevenlabs" in missing_packages:
                        print("Installing elevenlabs with specific version...")
                        try:
                            result = subprocess.run(
                                [sys.executable, "-m", "pip", "install", "--upgrade", "elevenlabs>=1.0.0"],
                                capture_output=True,
                                text=True
                            )
                            print(result.stdout)
                            if result.stderr:
                                print(f"Errors: {result.stderr}")
                            print("Successfully installed elevenlabs")
                        except Exception as e:
                            print(f"Error installing elevenlabs: {str(e)}")

                    # Install other packages
                    for package in missing_packages:
                        if package != "elevenlabs":
                            print(f"Installing {package}...")
                            try:
                                result = subprocess.run(
                                    [sys.executable, "-m", "pip", "install", package],
                                    capture_output=True,
                                    text=True
                                )
                                print(result.stdout)
                                if result.stderr:
                                    print(f"Errors: {result.stderr}")
                                print(f"Successfully installed {package}")
                            except Exception as e:
                                print(f"Error installing {package}: {str(e)}")

                    print("\nInstallation complete. Please restart the application.")
                    install_button.config(state=tk.DISABLED)

                button_frame = ttk.Frame(dialog)
                button_frame.pack(fill=tk.X, padx=10, pady=10)

                install_button = ttk.Button(button_frame, text="Install Missing Packages", command=install_packages)
                install_button.pack(side=tk.LEFT, padx=5)
            else:
                print("\nAll required packages are installed!")

        except Exception as e:
            print(f"Error during dependency check: {str(e)}")

        finally:
            # Restore stdout
            sys.stdout = old_stdout

        # Add close button
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        close_button = ttk.Button(button_frame, text="Close", command=dialog.destroy)
        close_button.pack(side=tk.RIGHT, padx=5)

    def initialize_apis(self):
        """Initialize API clients"""
        # Initialize OpenAI client
        openai_api_key = self.openai_api_key_var.get().strip()
        openai_base_url = self.openai_base_url_var.get().strip()

        if openai_api_key:
            try:
                # Use the specified base URL if provided
                if openai_base_url:
                    self.openai_client = OpenAI(api_key=openai_api_key, base_url=openai_base_url)
                    self.log_message(f"OpenAI client initialized with custom base URL: {openai_base_url}")
                else:
                    self.openai_client = OpenAI(api_key=openai_api_key)
                    self.log_message("OpenAI client initialized with default base URL")
            except Exception as e:
                self.log_message(f"Error initializing OpenAI client: {str(e)}")
                self.openai_client = None

        # Initialize ElevenLabs
        elevenlabs_api_key = self.elevenlabs_api_key_var.get().strip()
        if elevenlabs_api_key:
            try:
                # Set API key for both old and new API
                elevenlabs.api_key = elevenlabs_api_key

                # Initialize client for new API
                self.elevenlabs_client = ElevenLabs(api_key=elevenlabs_api_key)

                self.refresh_voices()
                self.log_message("ElevenLabs initialized")
            except Exception as e:
                self.log_message(f"Error initializing ElevenLabs: {str(e)}")

    def _get_voices_from_api(self):
        """Get voices directly from ElevenLabs API"""
        try:
            import requests

            url = "https://api.elevenlabs.io/v1/voices"

            headers = {
                "Accept": "application/json",
                "xi-api-key": elevenlabs.api_key
            }

            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if "voices" in data:
                    return data["voices"]

            self.log_message(f"API call failed with status code {response.status_code}")
            return []

        except Exception as e:
            self.log_message(f"Error calling ElevenLabs API directly: {str(e)}")
            return []

    def refresh_voices(self):
        """Refresh the list of available ElevenLabs voices"""
        elevenlabs_api_key = self.elevenlabs_api_key_var.get().strip()
        if not elevenlabs_api_key:
            messagebox.showerror("Error", "Please enter your ElevenLabs API key")
            return

        try:
            # Set API key
            elevenlabs.api_key = elevenlabs_api_key

            # Get all voices using new API client
            try:
                self.log_message("Using new ElevenLabs client API")
                response = self.elevenlabs_client.voices.get_all()
                all_voices = response.voices
                self.log_message(f"Successfully retrieved {len(all_voices)} voices using new ElevenLabs API")
            except Exception as e:
                self.log_message(f"Error getting voices using new client API: {str(e)}")

                # Try older API versions as fallback
                try:
                    # Try newer API first
                    if hasattr(elevenlabs.voices, "get_all"):
                        self.log_message("Using elevenlabs.voices.get_all()")
                        all_voices = elevenlabs.voices.get_all()
                    elif hasattr(elevenlabs, "voices") and callable(elevenlabs.voices):
                        # Try direct call to voices()
                        self.log_message("Using elevenlabs.voices()")
                        all_voices = elevenlabs.voices()
                    elif hasattr(elevenlabs, "voices"):
                        # Try to access voices directly
                        self.log_message("Using elevenlabs.voices attribute")
                        all_voices = elevenlabs.voices
                    else:
                        # Try direct API call as last resort
                        self.log_message("Using direct API call to get voices")
                        all_voices = self._get_voices_from_api()

                    self.log_message(f"Successfully retrieved voices using fallback method")
                except Exception as e2:
                    self.log_message(f"Error getting voices using fallback methods: {str(e2)}")
                    self.log_message("Trying direct API call as final fallback")
                    # Try direct API call as fallback
                    all_voices = self._get_voices_from_api()

            # If still no voices, create empty list as fallback
            if not all_voices:
                self.log_message("No voices found, using empty list")
                all_voices = []

            # Store voice information
            self.elevenlabs_voices = []
            voice_names = []

            # Process voices based on their structure
            for voice in all_voices:
                # Check if voice is an object with attributes or a dictionary
                if hasattr(voice, "voice_id") and hasattr(voice, "name"):
                    # Object with attributes
                    voice_id = voice.voice_id
                    voice_name = voice.name
                elif isinstance(voice, dict) and "voice_id" in voice and "name" in voice:
                    # Dictionary
                    voice_id = voice["voice_id"]
                    voice_name = voice["name"]
                else:
                    # Unknown format, try to extract information
                    self.log_message(f"Unknown voice format: {type(voice)}")
                    continue

                self.elevenlabs_voices.append({
                    "id": voice_id,
                    "name": voice_name
                })
                voice_names.append(voice_name)

            # Update dropdown
            self.voice_dropdown['values'] = voice_names

            # Set current selection if it exists in the list
            current_voice = self.voice_var.get()
            if current_voice in voice_names:
                self.voice_dropdown.set(current_voice)
            elif voice_names:
                self.voice_dropdown.set(voice_names[0])

            self.log_message(f"Loaded {len(voice_names)} voices from ElevenLabs")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load voices: {str(e)}")

    def log_message(self, message):
        """Add a message to the log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # Insert at the end of the log
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)  # Scroll to the end

        # Update status bar
        self.status_var.set(message)

    def generate_video(self):
        """Generate video from script"""
        # Check if API clients are initialized
        if not self.openai_client:
            messagebox.showerror("Error", "OpenAI client not initialized. Please check your API key.")
            return

        # Check if script is provided
        script = self.script_text.get(1.0, tk.END).strip()
        if not script:
            messagebox.showerror("Error", "Please enter a script")
            return

        # Check if output directory is provided
        output_dir = self.output_dir_var.get().strip()
        if not output_dir:
            messagebox.showerror("Error", "Please select an output directory")
            return

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Start generation in a separate thread
        threading.Thread(target=self._generate_video_thread, args=(script, output_dir)).start()

    def show_advanced_options(self):
        """Show advanced options dialog for script splitting"""
        # Create a dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Advanced Script Splitting Options")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # Create a frame for options
        options_frame = ttk.LabelFrame(dialog, text="Script Splitting Options")
        options_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create variables for options if they don't exist
        if not hasattr(self, 'custom_split_markers'):
            self.custom_split_markers = tk.StringVar(value=".!?")

        if not hasattr(self, 'min_segment_length'):
            self.min_segment_length = tk.IntVar(value=50)

        if not hasattr(self, 'max_segment_length'):
            self.max_segment_length = tk.IntVar(value=500)

        if not hasattr(self, 'balance_segments'):
            self.balance_segments = tk.BooleanVar(value=True)

        # Custom split markers
        markers_label = ttk.Label(options_frame, text="Custom Split Markers:")
        markers_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        markers_entry = ttk.Entry(options_frame, textvariable=self.custom_split_markers, width=20)
        markers_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        markers_help = ttk.Label(options_frame, text="(Characters that mark the end of a segment, e.g., .!?)")
        markers_help.grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)

        # Minimum segment length
        min_length_label = ttk.Label(options_frame, text="Minimum Segment Length:")
        min_length_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)

        min_length_spinbox = ttk.Spinbox(options_frame, from_=10, to=200, textvariable=self.min_segment_length, width=5)
        min_length_spinbox.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        min_length_help = ttk.Label(options_frame, text="(Minimum characters per segment)")
        min_length_help.grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)

        # Maximum segment length
        max_length_label = ttk.Label(options_frame, text="Maximum Segment Length:")
        max_length_label.grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)

        max_length_spinbox = ttk.Spinbox(options_frame, from_=100, to=1000, textvariable=self.max_segment_length, width=5)
        max_length_spinbox.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)

        max_length_help = ttk.Label(options_frame, text="(Maximum characters per segment)")
        max_length_help.grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)

        # Balance segments option
        balance_check = ttk.Checkbutton(options_frame, text="Balance segment lengths", variable=self.balance_segments)
        balance_check.grid(row=3, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)

        # Add a text area for custom segment breaks
        custom_breaks_frame = ttk.LabelFrame(dialog, text="Custom Segment Breaks (one per line)")
        custom_breaks_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create a variable for custom breaks if it doesn't exist
        if not hasattr(self, 'custom_breaks_text'):
            self.custom_breaks_text = ""

        # Create a text widget for custom breaks
        self.custom_breaks_widget = scrolledtext.ScrolledText(custom_breaks_frame, wrap=tk.WORD, height=8)
        self.custom_breaks_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.custom_breaks_widget.insert(tk.END, self.custom_breaks_text)

        # Add a help text
        help_text = ttk.Label(custom_breaks_frame, text="Enter phrases that should always start a new segment (e.g., 'Next, ')")
        help_text.pack(anchor=tk.W, padx=5, pady=5)

        # Add buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # Save button
        save_button = ttk.Button(button_frame, text="Save", command=lambda: self.save_advanced_options(dialog))
        save_button.pack(side=tk.RIGHT, padx=5)

        # Cancel button
        cancel_button = ttk.Button(button_frame, text="Cancel", command=dialog.destroy)
        cancel_button.pack(side=tk.RIGHT, padx=5)

    def save_advanced_options(self, dialog):
        """Save advanced options and close the dialog"""
        # Save custom breaks text
        self.custom_breaks_text = self.custom_breaks_widget.get(1.0, tk.END).strip()

        # Close the dialog
        dialog.destroy()

        # Log the settings
        self.log_message("Advanced script splitting options saved")
        self.log_message(f"Custom split markers: {self.custom_split_markers.get()}")
        self.log_message(f"Min/Max segment length: {self.min_segment_length.get()}/{self.max_segment_length.get()}")
        self.log_message(f"Balance segments: {self.balance_segments.get()}")
        if self.custom_breaks_text:
            # Sử dụng raw string để tránh lỗi với dấu backslash trong f-string
            num_phrases = len(self.custom_breaks_text.split("\n"))
            self.log_message(f"Custom breaks: {num_phrases} phrases")

    def _analyze_script_with_chatgpt(self, script, audio_duration):
        """Use ChatGPT to analyze the script and suggest scenes with timing"""
        self.log_message("Analyzing script with ChatGPT to create scene plan...")

        try:
            # Get selected ChatGPT model
            model = self.chatgpt_model_var.get()
            self.log_message(f"Using ChatGPT model: {model}")

            # Create system message for ChatGPT
            system_message = """
            You are an expert at analyzing scripts and creating visual storyboards.
            Your task is to analyze a script and create a detailed scene plan for a video.
            Each scene should have:
            1. The exact text from the script that will be narrated during this scene
            2. A description of what the stick figure character should be doing (ACTION)
            3. What objects or visual elements should be in the scene (OBJECTS)
            4. How many seconds this scene should last based on the total audio duration

            The scenes should cover the entire script and the timing should add up to the total audio duration.
            """

            # Create user message
            user_message = f"""
            Analyze this script and create a detailed scene plan for a video with stick figure illustrations.
            The total audio duration is {audio_duration:.1f} seconds.

            SCRIPT:
            "{script.strip()}"

            Return a JSON array where each scene is an object with these properties:
            - "text": The exact text from the script that will be narrated during this scene
            - "action": What the stick figure character should be doing
            - "objects": What objects or visual elements should be in the scene (2-4 items)
            - "duration": How many seconds this scene should last (should add up to {audio_duration:.1f})

            Example:
            [
              {{
                "text": "Think you're not creative?",
                "action": "scratching head with question mark above",
                "objects": "question mark, thought bubble, art supplies faded in background",
                "duration": 2.5
              }},
              {{
                "text": "I'm about to prove you wrong—here's how to unlock your creativity in just 3 minutes.",
                "action": "unlocking a door with a key, looking excited",
                "objects": "key labeled 'creativity', clock showing 3 minutes, door opening with light",
                "duration": 5.0
              }}
            ]

            Make sure:
            1. The scenes cover the entire script
            2. The durations add up to exactly {audio_duration:.1f} seconds
            3. Each scene has a clear ACTION and OBJECTS
            4. The scenes flow logically and match the narration
            """

            # Call ChatGPT API
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.7,
                max_tokens=1500,
                response_format={"type": "json_object"}  # Request JSON format
            )

            # Extract the JSON from the response
            json_response = response.choices[0].message.content.strip()

            # Clean up the response if it contains markdown formatting
            if json_response.startswith("```") and ("json" in json_response.split("\n")[0].lower()):
                # Extract the JSON part from the markdown code block
                lines = json_response.split("\n")
                json_response = "\n".join(lines[1:-1] if lines[-1] == "```" else lines[1:])
                self.log_message("Cleaned up markdown formatting from JSON response")

            # Parse the JSON
            import json
            try:
                parsed_data = json.loads(json_response)

                # Check if we have a scenes array
                if "scenes" in parsed_data:
                    scenes = parsed_data["scenes"]
                else:
                    # If the response is directly an array
                    scenes = parsed_data

                # Validate the scenes
                if not isinstance(scenes, list):
                    raise ValueError("Scenes data is not a list")

                if len(scenes) == 0:
                    raise ValueError("No scenes were generated")

                # Check if each scene has the required properties
                for i, scene in enumerate(scenes):
                    if not all(key in scene for key in ["text", "action", "objects", "duration"]):
                        self.log_message(f"Warning: Scene {i+1} is missing required properties")

                # Calculate total duration
                total_duration = sum(scene.get("duration", 0) for scene in scenes)
                self.log_message(f"Total scene duration: {total_duration:.1f} seconds (target: {audio_duration:.1f})")

                # Log the scenes
                self.log_message(f"ChatGPT analysis complete - Generated {len(scenes)} scenes:")
                self.log_message(f"NOTE: Number of images will be {len(scenes)} based on ChatGPT's scene plan, ignoring the 'Number of Images' setting")
                for i, scene in enumerate(scenes):
                    self.log_message(f"Scene {i+1}: {scene.get('text', '')[:50]}... ({scene.get('duration', 0):.1f}s)")

                return scenes

            except json.JSONDecodeError as e:
                self.log_message(f"Error parsing JSON: {str(e)}")
                self.log_message(f"Raw response: {json_response}")
                return None

        except Exception as e:
            self.log_message(f"Error analyzing script with ChatGPT: {str(e)}")
            return None

    def _split_script_into_segments(self, script, num_segments):
        """Split a script into segments for image generation using the selected method"""
        # If script is empty, return empty list
        if not script.strip():
            return []

        # If num_segments is 0 or not specified, split by line breaks
        if num_segments == 0:
            lines = [line.strip() for line in script.split('\n') if line.strip()]
            if len(lines) > 1:
                self.log_message(f"Using natural line breaks: {len(lines)} segments")
                return lines
            else:
                # If there's only one line, use default number of segments
                self.log_message("Only one line detected, using default splitting")
                num_segments = 3  # Default to 3 segments if not specified
        # If num_segments is -1, use all detected segments without combining
        elif num_segments == -1:
            self.log_message("Using all detected segments without combining")
            # We'll detect segments but won't combine them later

        # Get the selected splitting method
        split_method = self.split_method_var.get()
        self.log_message(f"Using splitting method: {split_method}")

        # Apply the selected splitting method
        if split_method == "punctuation":
            return self._split_by_punctuation(script, num_segments)
        elif split_method == "sentences":
            return self._split_by_sentences(script, num_segments)
        elif split_method == "paragraphs":
            return self._split_by_paragraphs(script, num_segments)
        elif split_method == "equal_length":
            return self._split_by_equal_length(script, num_segments)
        else:
            # Default to punctuation method
            self.log_message(f"Unknown splitting method: {split_method}, using punctuation")
            return self._split_by_punctuation(script, num_segments)

    def _split_by_punctuation(self, script, num_segments):
        """Split script by punctuation marks and natural break points"""
        import re

        # Get custom split markers if available
        if hasattr(self, 'custom_split_markers') and self.custom_split_markers.get():
            split_markers = self.custom_split_markers.get()
            # Escape special regex characters
            split_markers = re.escape(split_markers)
            split_pattern = f'(?<=[{split_markers}])\\s+'
            self.log_message(f"Using custom split markers: {split_markers}")
        else:
            # Default to periods, question marks, exclamation points
            split_pattern = r'(?<=[.!?])\s+'

        # First, split by punctuation
        sentences = re.split(split_pattern, script)
        sentences = [s.strip() for s in sentences if s.strip()]

        # Check for custom break phrases
        if hasattr(self, 'custom_breaks_text') and self.custom_breaks_text:
            custom_breaks = [b.strip() for b in self.custom_breaks_text.split('\n') if b.strip()]
            if custom_breaks:
                self.log_message(f"Applying {len(custom_breaks)} custom break phrases")

                # Create new sentences by checking for custom break phrases
                new_sentences = []
                for sentence in sentences:
                    # Check if the sentence contains any of the custom break phrases
                    found_break = False
                    for break_phrase in custom_breaks:
                        if break_phrase in sentence:
                            # Split the sentence at the break phrase
                            parts = sentence.split(break_phrase)
                            if parts[0].strip():
                                new_sentences.append(parts[0].strip())

                            # Add the break phrase with the rest of the sentence
                            for i in range(1, len(parts)):
                                if parts[i].strip():
                                    new_sentences.append(f"{break_phrase}{parts[i].strip()}")

                            found_break = True
                            break

                    # If no break phrase was found, add the whole sentence
                    if not found_break:
                        new_sentences.append(sentence)

                sentences = new_sentences

        self.log_message(f"Split script into {len(sentences)} segments based on punctuation and custom breaks")

        # If we have exactly the right number of sentences, use them
        if len(sentences) == num_segments:
            for i, sentence in enumerate(sentences):
                self.log_message(f"Segment {i+1}: {sentence[:50]}...")
            return sentences

        # Check for minimum segment length
        min_length = 50  # Default
        if hasattr(self, 'min_segment_length'):
            min_length = self.min_segment_length.get()

        # Check for maximum segment length
        max_length = 500  # Default
        if hasattr(self, 'max_segment_length'):
            max_length = self.max_segment_length.get()

        # Check if we need to split any sentences that are too long
        for i, sentence in enumerate(sentences):
            if len(sentence) > max_length:
                self.log_message(f"Sentence {i+1} exceeds maximum length ({len(sentence)} > {max_length}), splitting")
                # This will be handled in the next step

        # If we have fewer sentences than requested segments, split the longest sentences
        if len(sentences) < num_segments:
            self.log_message(f"Fewer sentences ({len(sentences)}) than requested segments ({num_segments}), splitting longest sentences")

            while len(sentences) < num_segments:
                # Find the longest sentence
                longest_idx = 0
                longest_len = 0
                for i, sentence in enumerate(sentences):
                    if len(sentence) > longest_len:
                        longest_len = len(sentence)
                        longest_idx = i

                # If the longest sentence is shorter than the minimum length and we're not forced to split,
                # stop splitting
                if longest_len < min_length and not (len(sentences) < num_segments / 2):
                    self.log_message(f"Longest sentence ({longest_len} chars) is below minimum length ({min_length}), stopping split")
                    break

                # Split the longest sentence at a natural break point
                longest = sentences[longest_idx]

                # Try to split at a natural break point
                split_points = []

                # Look for commas
                comma_positions = [m.start() for m in re.finditer(r',\s+', longest)]
                split_points.extend([(pos + 2, 'comma') for pos in comma_positions])

                # Look for semicolons
                semicolon_positions = [m.start() for m in re.finditer(r';\s+', longest)]
                split_points.extend([(pos + 2, 'semicolon') for pos in semicolon_positions])

                # Look for colons
                colon_positions = [m.start() for m in re.finditer(r':\s+', longest)]
                split_points.extend([(pos + 2, 'colon') for pos in colon_positions])

                # Look for conjunctions (and, but, or, so, yet, for, nor)
                conjunction_positions = [m.start() for m in re.finditer(r'\s+(and|but|or|so|yet|for|nor)\s+', longest)]
                split_points.extend([(pos + 1, 'conjunction') for pos in conjunction_positions])

                # Look for dashes
                dash_positions = [m.start() for m in re.finditer(r'\s+[-—–]\s+', longest)]
                split_points.extend([(pos + 1, 'dash') for pos in dash_positions])

                # Look for parentheses
                paren_positions = [m.start() for m in re.finditer(r'\)\s+', longest)]
                split_points.extend([(pos + 2, 'parenthesis') for pos in paren_positions])

                if split_points:
                    # Sort by position
                    split_points.sort(key=lambda x: x[0])

                    # Find the middle-most split point
                    middle_idx = len(longest) // 2
                    best_split_idx = min(split_points, key=lambda x: abs(x[0] - middle_idx))[0]

                    # Split the sentence
                    part1 = longest[:best_split_idx].strip()
                    part2 = longest[best_split_idx:].strip()

                    # Replace the original sentence with the two parts
                    sentences[longest_idx] = part1
                    sentences.insert(longest_idx + 1, part2)

                    self.log_message(f"Split sentence at position {best_split_idx}: '{part1[:30]}...' | '{part2[:30]}...'")
                else:
                    # If no natural break points, split in the middle
                    middle = len(longest) // 2
                    # Try to find a space near the middle
                    space_pos = longest.find(' ', middle)
                    if space_pos == -1 or space_pos > middle + 15:
                        space_pos = longest.rfind(' ', 0, middle)

                    if space_pos != -1:
                        part1 = longest[:space_pos].strip()
                        part2 = longest[space_pos:].strip()
                    else:
                        # If no spaces found, just split in the middle
                        part1 = longest[:middle].strip()
                        part2 = longest[middle:].strip()

                    # Replace the original sentence with the two parts
                    sentences[longest_idx] = part1
                    sentences.insert(longest_idx + 1, part2)

                    self.log_message(f"Split sentence in the middle: '{part1[:30]}...' | '{part2[:30]}...'")

        # Check if we should use all detected segments without combining
        if self.use_all_segments_var.get() or num_segments == -1:
            # If using all segments, don't combine
            self.log_message(f"Using all {len(sentences)} detected segments without combining")
            # Log the final segments
            for i, segment in enumerate(sentences):
                self.log_message(f"Final segment {i+1}: {segment[:50]}...")
            return sentences

        # If we have more sentences than requested segments, combine some
        if len(sentences) > num_segments and num_segments > 0:
            self.log_message(f"More sentences ({len(sentences)}) than requested segments ({num_segments}), combining sentences")

            # Check if we should balance segment lengths
            balance_segments = True
            if hasattr(self, 'balance_segments'):
                balance_segments = self.balance_segments.get()

            if balance_segments:
                self.log_message("Using balanced segment lengths")
                # Try to create segments of similar length

                # Calculate target length per segment
                total_length = sum(len(s) for s in sentences)
                target_length = total_length / num_segments
                self.log_message(f"Target length per segment: {target_length:.1f} characters")

                segments = []
                current_segment = []
                current_length = 0

                for sentence in sentences:
                    # If adding this sentence would exceed the target length by too much
                    # and we already have content, finish the current segment
                    if (current_length > 0 and
                        current_length + len(sentence) > target_length * 1.5 and
                        len(segments) < num_segments - 1):
                        segments.append(" ".join(current_segment))
                        current_segment = [sentence]
                        current_length = len(sentence)
                    else:
                        # Add the sentence to the current segment
                        current_segment.append(sentence)
                        current_length += len(sentence)

                # Add the last segment if it's not empty
                if current_segment:
                    segments.append(" ".join(current_segment))

                # If we didn't create enough segments, fall back to the regular method
                if len(segments) < num_segments:
                    self.log_message(f"Could only create {len(segments)} balanced segments, falling back to regular method")
                    balance_segments = False

                # If we created too many segments, combine the shortest ones
                while len(segments) > num_segments:
                    # Find the shortest segment
                    shortest_idx = 0
                    shortest_len = float('inf')
                    for i, segment in enumerate(segments):
                        if len(segment) < shortest_len:
                            shortest_len = len(segment)
                            shortest_idx = i

                    # Combine the shortest segment with an adjacent one
                    if shortest_idx > 0:
                        segments[shortest_idx-1] += " " + segments[shortest_idx]
                        segments.pop(shortest_idx)
                    else:
                        segments[shortest_idx] += " " + segments[shortest_idx+1]
                        segments.pop(shortest_idx+1)
            else:
                # If not balancing, use the regular method
                self.log_message("Using regular segment distribution")
                # Calculate how many sentences to combine into each segment
                sentences_per_segment = len(sentences) / num_segments

                segments = []
                current_segment = []

                for i, sentence in enumerate(sentences):
                    current_segment.append(sentence)

                    # Check if we've reached the target number of sentences for this segment
                    target_count = int((i + 1) / sentences_per_segment)

                    if (i == len(sentences) - 1) or (len(segments) < target_count):
                        segments.append(" ".join(current_segment))
                        current_segment = []

                # Make sure we have exactly the right number of segments
                while len(segments) > num_segments:
                    # Find the shortest segment
                    shortest_idx = 0
                    shortest_len = float('inf')
                    for i, segment in enumerate(segments):
                        if len(segment) < shortest_len:
                            shortest_len = len(segment)
                            shortest_idx = i

                    # Combine the shortest segment with an adjacent one
                    if shortest_idx > 0:
                        segments[shortest_idx-1] += " " + segments[shortest_idx]
                        segments.pop(shortest_idx)
                    else:
                        segments[shortest_idx] += " " + segments[shortest_idx+1]
                        segments.pop(shortest_idx+1)

            sentences = segments

        # Log the final segments
        for i, segment in enumerate(sentences):
            self.log_message(f"Final segment {i+1}: {segment[:50]}...")

        return sentences

    def _split_by_sentences(self, script, num_segments):
        """Split script into complete sentences and distribute evenly"""
        import re

        # Split by sentence-ending punctuation
        sentences = re.split(r'(?<=[.!?])\s+', script)
        sentences = [s.strip() for s in sentences if s.strip()]

        self.log_message(f"Split script into {len(sentences)} complete sentences")

        # If we have fewer sentences than segments, fall back to punctuation method
        if len(sentences) < num_segments:
            self.log_message(f"Not enough sentences ({len(sentences)}) for {num_segments} segments, using punctuation method")
            return self._split_by_punctuation(script, num_segments)

        # If we have exactly the right number, use them
        if len(sentences) == num_segments:
            return sentences

        # Check if we should use all detected segments without combining
        if self.use_all_segments_var.get() or num_segments == -1:
            # If using all segments, don't combine
            self.log_message(f"Using all {len(sentences)} detected segments without combining")
            return sentences

        # If we have more sentences than segments, group them
        segments = []
        sentences_per_segment = len(sentences) / num_segments

        current_segment = []
        for i, sentence in enumerate(sentences):
            current_segment.append(sentence)

            # Check if we've reached the target number of sentences for this segment
            target_count = int((i + 1) / sentences_per_segment)

            if (i == len(sentences) - 1) or (len(segments) < target_count):
                segments.append(" ".join(current_segment))
                current_segment = []

        # Log the final segments
        for i, segment in enumerate(segments):
            self.log_message(f"Sentence group {i+1}: {segment[:50]}...")

        return segments

    def _split_by_paragraphs(self, script, num_segments):
        """Split script by paragraphs (double line breaks)"""
        # Split by double line breaks
        paragraphs = [p.strip() for p in script.split('\n\n') if p.strip()]

        # If there are no paragraph breaks, try single line breaks
        if len(paragraphs) <= 1:
            paragraphs = [p.strip() for p in script.split('\n') if p.strip()]
            self.log_message(f"No double line breaks found, using single line breaks: {len(paragraphs)} paragraphs")
        else:
            self.log_message(f"Split script into {len(paragraphs)} paragraphs")

        # If we still don't have enough paragraphs, fall back to sentences
        if len(paragraphs) < num_segments:
            self.log_message(f"Not enough paragraphs ({len(paragraphs)}) for {num_segments} segments, using sentence method")
            return self._split_by_sentences(script, num_segments)

        # If we have exactly the right number, use them
        if len(paragraphs) == num_segments:
            return paragraphs

        # Check if we should use all detected segments without combining
        if self.use_all_segments_var.get() or num_segments == -1:
            # If using all segments, don't combine
            self.log_message(f"Using all {len(paragraphs)} detected paragraphs without combining")
            return paragraphs

        # If we have more paragraphs than segments, group them
        segments = []
        paragraphs_per_segment = len(paragraphs) / num_segments

        current_segment = []
        for i, paragraph in enumerate(paragraphs):
            current_segment.append(paragraph)

            # Check if we've reached the target number of paragraphs for this segment
            target_count = int((i + 1) / paragraphs_per_segment)

            if (i == len(paragraphs) - 1) or (len(segments) < target_count):
                segments.append(" ".join(current_segment))
                current_segment = []

        # Log the final segments
        for i, segment in enumerate(segments):
            self.log_message(f"Paragraph group {i+1}: {segment[:50]}...")

        return segments

    def _split_by_equal_length(self, script, num_segments):
        """Split script into segments of approximately equal length"""
        # If script is very short, fall back to punctuation method
        if len(script) < 100:
            self.log_message(f"Script too short ({len(script)} chars) for equal length splitting, using punctuation method")
            return self._split_by_punctuation(script, num_segments)

        # Calculate target length for each segment
        target_length = len(script) / num_segments
        self.log_message(f"Target length per segment: {target_length:.1f} characters")

        # Split by sentences first
        import re
        sentences = re.split(r'(?<=[.!?])\s+', script)
        sentences = [s.strip() for s in sentences if s.strip()]

        # Check if we should use all detected segments without combining
        if self.use_all_segments_var.get() or num_segments == -1:
            # If using all segments, don't combine
            self.log_message(f"Using all {len(sentences)} detected sentences without combining")
            return sentences

        # Create segments of approximately equal length
        segments = []
        current_segment = []
        current_length = 0

        for sentence in sentences:
            # If adding this sentence would exceed the target length and we already have content,
            # finish the current segment
            if current_length > 0 and current_length + len(sentence) > target_length * 1.5 and len(segments) < num_segments - 1:
                segments.append(" ".join(current_segment))
                current_segment = [sentence]
                current_length = len(sentence)
            else:
                # Add the sentence to the current segment
                current_segment.append(sentence)
                current_length += len(sentence)

        # Add the last segment if it's not empty
        if current_segment:
            segments.append(" ".join(current_segment))

        # If we didn't create enough segments, fall back to punctuation method
        if len(segments) < num_segments:
            self.log_message(f"Could only create {len(segments)} segments of equal length, using punctuation method")
            return self._split_by_punctuation(script, num_segments)

        # If we created too many segments, combine the shortest ones
        while len(segments) > num_segments:
            # Find the shortest segment
            shortest_idx = 0
            shortest_len = float('inf')
            for i, segment in enumerate(segments):
                if len(segment) < shortest_len:
                    shortest_len = len(segment)
                    shortest_idx = i

            # Combine the shortest segment with an adjacent one
            if shortest_idx > 0:
                segments[shortest_idx-1] += " " + segments[shortest_idx]
                segments.pop(shortest_idx)
            else:
                segments[shortest_idx] += " " + segments[shortest_idx+1]
                segments.pop(shortest_idx+1)

        # Log the final segments
        for i, segment in enumerate(segments):
            self.log_message(f"Equal length segment {i+1}: {len(segment)} chars, '{segment[:50]}...'")

        return segments

    def _generate_video_thread(self, script, output_dir):
        """Thread for generating video"""
        try:
            self.log_message("Starting video generation...")
            self.progress_var.set(0)

            # Reset generated prompts
            self.generated_prompts = []

            # Disable view prompts button
            self.root.after(0, lambda: self.view_prompts_button.config(state=tk.DISABLED))

            # Create cache directory in the output directory
            timestamp = int(time.time())
            self.cache_dir = os.path.join(output_dir, "cache")
            os.makedirs(self.cache_dir, exist_ok=True)
            self.log_message(f"Using cache directory: {self.cache_dir}")

            # Create a project-specific directory for this generation
            project_name = f"project_{timestamp}"
            self.temp_dir = os.path.join(self.cache_dir, project_name)
            os.makedirs(self.temp_dir, exist_ok=True)
            self.log_message(f"Created project directory: {self.temp_dir}")

            # Get splitting method
            split_method = self.split_method_var.get()

            # If using ChatGPT method, we'll determine the number of images later
            # Otherwise, use the number specified in the UI
            if split_method == "chatgpt":
                # For ChatGPT method, we'll just pass the whole script for now
                lines = [script]
                self.log_message("Using ChatGPT for scene planning - number of images will be determined by ChatGPT analysis")
            else:
                # Get number of images to generate from UI
                num_images = self.num_images_var.get()

                # Check if we should use all detected segments
                if self.use_all_segments_var.get():
                    self.log_message("Using all detected segments option is enabled")
                    num_images = -1  # Set to -1 to use all detected segments

                # Split script into segments
                lines = self._split_script_into_segments(script, num_images)
                self.log_message(f"Script split into {len(lines)} segments")

            # Create a clean filename from the first few words of the script
            import re
            script_words = re.sub(r'[^\w\s]', '', script.strip().split('\n')[0])[:30]
            clean_filename = re.sub(r'\s+', '_', script_words).lower()

            # Check if we should use an existing audio file
            if self.use_existing_audio_var.get():
                existing_audio_file = self.audio_file_var.get().strip()
                if not existing_audio_file:
                    raise Exception("No audio file selected. Please select an audio file or disable the 'Use existing audio file' option.")

                if not os.path.exists(existing_audio_file):
                    raise Exception(f"Selected audio file does not exist: {existing_audio_file}")

                # Copy the existing audio file to the cache directory with a meaningful name
                audio_filename = f"audio_{clean_filename}.mp3"
                audio_file = os.path.join(self.cache_dir, audio_filename)
                self.log_message(f"Using existing audio file: {existing_audio_file}")
                shutil.copy2(existing_audio_file, audio_file)

                # Also copy to the project directory for this generation
                project_audio_file = os.path.join(self.temp_dir, audio_filename)
                shutil.copy2(existing_audio_file, project_audio_file)
            else:
                # Generate TTS audio with a meaningful name
                self.log_message("Generating TTS audio...")
                audio_filename = f"audio_{clean_filename}.mp3"
                audio_file = os.path.join(self.cache_dir, audio_filename)

                # Check if this audio already exists in cache
                if os.path.exists(audio_file):
                    self.log_message(f"Found existing audio in cache: {audio_filename}")
                else:
                    # Generate new audio
                    if not self._generate_tts(script, audio_file):
                        raise Exception("Failed to generate TTS audio")
                    self.log_message(f"Saved audio to cache as: {audio_filename}")

                # Copy to the project directory for this generation
                project_audio_file = os.path.join(self.temp_dir, audio_filename)
                shutil.copy2(audio_file, project_audio_file)

            self.progress_var.set(20)

            # Get audio duration
            import moviepy.editor as mp
            audio = mp.AudioFileClip(audio_file)
            audio_duration = audio.duration
            self.log_message(f"Audio duration: {audio_duration:.2f} seconds")

            # Store the audio file path for reference
            self.current_audio_file = audio_file

            # Check if we're using ChatGPT for scene planning
            if split_method == "chatgpt":
                self.log_message("Using ChatGPT for scene planning...")

                # Analyze script with ChatGPT
                scenes = self._analyze_script_with_chatgpt(script, audio_duration)

                if not scenes:
                    self.log_message("ChatGPT scene planning failed, falling back to regular splitting")
                    # Fall back to regular splitting
                    split_method_backup = "punctuation"
                    self.log_message(f"Using fallback splitting method: {split_method_backup}")

                    if split_method_backup == "punctuation":
                        lines = self._split_by_punctuation(script, 3)  # Default to 3 segments
                    elif split_method_backup == "sentences":
                        lines = self._split_by_sentences(script, 3)
                    elif split_method_backup == "paragraphs":
                        lines = self._split_by_paragraphs(script, 3)
                    else:
                        lines = self._split_by_equal_length(script, 3)

                    # Generate images for each line
                    self.log_message("Generating images for each line...")
                    image_files = []

                    for i, line in enumerate(lines):
                        self.log_message(f"Generating image {i+1}/{len(lines)}: {line[:50]}...")
                        image_file = os.path.join(self.temp_dir, f"image_{i:03d}.png")

                        if self._generate_image(line, image_file):
                            image_files.append(image_file)
                        else:
                            self.log_message(f"Warning: Failed to generate image for line {i+1}")

                        # Update progress
                        progress = 20 + (60 * (i + 1) / len(lines))
                        self.progress_var.set(progress)

                    if not image_files:
                        raise Exception("Failed to generate any images")

                    # Create video
                    self.log_message("Creating video...")
                    output_file = os.path.join(output_dir, f"generated_video_{int(time.time())}.mp4")

                    if not self._create_video(audio_file, image_files, output_file, script=script, segments=lines):
                        raise Exception("Failed to create video")

                else:
                    # Generate images for each scene
                    self.log_message(f"Generating images for {len(scenes)} scenes...")
                    image_files = []
                    scene_durations = []

                    for i, scene in enumerate(scenes):
                        # Extract scene information
                        scene_text = scene.get("text", "")
                        scene_action = scene.get("action", "")
                        scene_objects = scene.get("objects", "")
                        scene_duration = scene.get("duration", 0)

                        self.log_message(f"Generating image for scene {i+1}: {scene_text[:50]}... ({scene_duration:.1f}s)")
                        image_file = os.path.join(self.temp_dir, f"scene_{i:03d}.png")

                        # Create prompt for this scene
                        prompt = f"""A clean, cartoon-style black and white stick figure scene on a light beige background. Characters are simple stick figures with expressive faces and exaggerated motion lines. Use a hand-drawn aesthetic with smooth lines and minimal shading. The main figure is {scene_action}. Optional objects or characters include: {scene_objects}. Scene should feel dynamic, minimalistic, and consistent with educational or motivational animated explainer videos."""

                        # Store the prompt for reference
                        if hasattr(self, 'generated_prompts'):
                            self.generated_prompts.append({
                                'original_text': scene_text,
                                'generated_prompt': prompt
                            })

                        # Generate image using DALL-E with the original scene text for context
                        if self._generate_image_with_prompt(prompt, image_file, original_text=scene_text):
                            image_files.append(image_file)
                            scene_durations.append(scene_duration)
                        else:
                            self.log_message(f"Warning: Failed to generate image for scene {i+1}")

                        # Update progress
                        progress = 20 + (60 * (i + 1) / len(scenes))
                        self.progress_var.set(progress)

                    if not image_files:
                        raise Exception("Failed to generate any images")

                    # Create video with custom durations
                    self.log_message("Creating video with custom scene durations...")
                    output_file = os.path.join(output_dir, f"generated_video_{int(time.time())}.mp4")

                    if not self._create_video_with_custom_durations(audio_file, image_files, scene_durations, output_file):
                        raise Exception("Failed to create video")

            else:
                # Use regular splitting method
                # Generate images for each line
                self.log_message("Generating images for each line...")
                image_files = []

                for i, line in enumerate(lines):
                    self.log_message(f"Generating image {i+1}/{len(lines)}: {line[:50]}...")
                    image_file = os.path.join(self.temp_dir, f"image_{i:03d}.png")

                    if self._generate_image(line, image_file):
                        image_files.append(image_file)
                    else:
                        self.log_message(f"Warning: Failed to generate image for line {i+1}")

                    # Update progress
                    progress = 20 + (60 * (i + 1) / len(lines))
                    self.progress_var.set(progress)

                if not image_files:
                    raise Exception("Failed to generate any images")

                # Create video
                self.log_message("Creating video...")
                output_file = os.path.join(output_dir, f"generated_video_{int(time.time())}.mp4")

                if not self._create_video(audio_file, image_files, output_file, script=script, segments=lines):
                    raise Exception("Failed to create video")

            # Save generated prompts to a file
            if hasattr(self, 'generated_prompts') and self.generated_prompts:
                try:
                    # Make sure output directory exists
                    os.makedirs(output_dir, exist_ok=True)

                    # Save timestamp for consistent filenames
                    timestamp = int(time.time())
                    self.last_prompts_file = os.path.join(output_dir, f"prompts_{timestamp}.txt")

                    with open(self.last_prompts_file, 'w', encoding='utf-8') as f:
                        f.write("# Generated Prompts for DALL-E 3\n\n")
                        for i, prompt_data in enumerate(self.generated_prompts):
                            f.write(f"## Image {i+1}\n")
                            f.write(f"Original text: {prompt_data['original_text']}\n\n")
                            f.write(f"Generated prompt: {prompt_data['generated_prompt']}\n\n")
                            f.write("-" * 80 + "\n\n")
                    self.log_message(f"Saved generated prompts to {self.last_prompts_file}")

                    # Enable the view prompts button
                    self.root.after(0, lambda: self.view_prompts_button.config(state=tk.NORMAL))

                except Exception as e:
                    self.log_message(f"Error saving prompts: {str(e)}")

            self.progress_var.set(100)
            self.log_message(f"Video generation completed: {output_file}")

            # Open output directory
            if messagebox.askyesno("Success", f"Video generated successfully. Open output directory?"):
                os.startfile(output_dir)

        except Exception as e:
            self.log_message(f"Error generating video: {str(e)}")
            messagebox.showerror("Error", f"Failed to generate video: {str(e)}")

        finally:
            # We don't clean up the cache directory since we want to keep the files for reuse
            # But we can optionally clean up the project-specific directory if it's not needed
            # For now, we'll keep it for reference
            self.log_message(f"Cache directory with reusable files: {self.cache_dir}")
            self.log_message(f"Project directory with generated files: {self.temp_dir}")

    def _generate_tts(self, text, output_file):
        """Generate TTS audio using ElevenLabs"""
        try:
            # Get selected voice
            voice_name = self.voice_var.get()
            voice_id = None

            # Find voice ID by name
            for voice in self.elevenlabs_voices:
                if voice["name"] == voice_name:
                    voice_id = voice["id"]
                    break

            if not voice_id:
                self.log_message(f"Voice '{voice_name}' not found, using default voice")
                # Use a default voice if available
                if self.elevenlabs_voices:
                    voice_id = self.elevenlabs_voices[0]["id"]
                else:
                    raise Exception("No voices available")

            # Try using the new client API first (based on the example code)
            try:
                self.log_message(f"Using new ElevenLabs client API with voice ID: {voice_id}")

                # Generate audio using new client API
                audio_generator = self.elevenlabs_client.text_to_speech.convert(
                    text=text,
                    voice_id=voice_id,
                    model_id="eleven_multilingual_v2",
                    output_format="mp3_44100_128",
                )

                # Convert generator to bytes
                audio_bytes = b''
                try:
                    # If it's a generator, collect all chunks
                    if hasattr(audio_generator, '__iter__') and not isinstance(audio_generator, bytes):
                        self.log_message("Converting audio generator to bytes")
                        for chunk in audio_generator:
                            audio_bytes += chunk
                    else:
                        # If it's already bytes, use it directly
                        audio_bytes = audio_generator
                except Exception as gen_error:
                    self.log_message(f"Error processing audio generator: {str(gen_error)}")
                    raise

                # Save audio file
                with open(output_file, "wb") as f:
                    f.write(audio_bytes)

                self.log_message("Successfully generated audio using new client API")
                return os.path.exists(output_file)

            except Exception as e:
                self.log_message(f"Error using new client API: {str(e)}")
                self.log_message("Trying fallback methods...")

                # Try older API methods as fallback
                try:
                    # Check which API version to use
                    if hasattr(elevenlabs, "generate"):
                        # Old API
                        self.log_message("Using legacy ElevenLabs API")
                        audio = elevenlabs.generate(
                            text=text,
                            voice=voice_id,
                            model="eleven_monolingual_v1"
                        )

                        # Save audio file
                        with open(output_file, "wb") as f:
                            f.write(audio)
                    else:
                        # Try direct API call
                        self.log_message("Using direct API call")
                        import requests

                        url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"

                        headers = {
                            "Accept": "audio/mpeg",
                            "Content-Type": "application/json",
                            "xi-api-key": elevenlabs.api_key
                        }

                        data = {
                            "text": text,
                            "model_id": "eleven_multilingual_v2",
                            "voice_settings": {
                                "stability": 0.5,
                                "similarity_boost": 0.5
                            }
                        }

                        response = requests.post(url, json=data, headers=headers)

                        if response.status_code == 200:
                            with open(output_file, "wb") as f:
                                f.write(response.content)
                        else:
                            raise Exception(f"API call failed with status code {response.status_code}: {response.text}")

                    self.log_message("Successfully generated audio using fallback method")
                    return os.path.exists(output_file)

                except Exception as e2:
                    self.log_message(f"Error using fallback methods: {str(e2)}")
                    raise Exception(f"Failed to generate audio: {str(e)} | Fallback error: {str(e2)}")

        except Exception as e:
            self.log_message(f"Error generating TTS: {str(e)}")
            return False

    def _generate_image_prompt(self, line):
        """Generate a detailed image prompt using ChatGPT based on script text"""
        # If user chose not to use ChatGPT, return basic prompt with template
        if not self.use_chatgpt_var.get():
            basic_prompt = f"""A clean, cartoon-style black and white stick figure scene on a light beige background. Characters are simple stick figures with expressive faces and exaggerated motion lines. Use a hand-drawn aesthetic with smooth lines and minimal shading. The main figure is explaining the concept. Optional objects or characters include: elements related to "{line.strip()}". Scene should feel dynamic, minimalistic, and consistent with educational or motivational animated explainer videos."""
            self.log_message(f"Using basic prompt: {basic_prompt[:100]}...")
            return basic_prompt

        try:
            self.log_message(f"Generating image prompt for: {line[:50]}...")

            # Get selected ChatGPT model
            model = self.chatgpt_model_var.get()
            self.log_message(f"Using ChatGPT model: {model}")

            # Create system message for ChatGPT
            system_message = """
            You are an expert at analyzing script text and extracting the main action and objects.
            Your task is to identify:
            1. The main ACTION of the stick figure character (what they are doing or how they appear)
            2. The OBJECTS that should be included in the scene (objects, symbols, visual elements, etc.)

            Return ONLY a JSON object with two fields:
            - "ACTION": The main action or state of the stick figure (be descriptive and specific)
            - "OBJECTS": A comma-separated list of 2-4 objects or visual elements

            Do not include any explanations or additional text.
            """

            # Create user message
            user_message = f"""
            Extract the main ACTION and OBJECTS from this script text:
            "{line.strip()}"

            Return only a JSON object with "ACTION" and "OBJECTS" fields.
            Example:
            {{
              "ACTION": "jumping excitedly with arms raised",
              "OBJECTS": "thought bubble with light bulb, clock showing time passing, stack of books"
            }}
            """

            # Call ChatGPT API
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.7,
                max_tokens=300
            )

            # Extract the JSON from the response
            json_response = response.choices[0].message.content.strip()

            # Clean up the response if it contains markdown formatting
            if json_response.startswith("```") and ("json" in json_response.split("\n")[0].lower()):
                # Extract the JSON part from the markdown code block
                lines = json_response.split("\n")
                json_response = "\n".join(lines[1:-1] if lines[-1] == "```" else lines[1:])
                self.log_message("Cleaned up markdown formatting from JSON response")

            try:
                # Parse the JSON
                import json
                parsed_data = json.loads(json_response)

                # Extract ACTION and OBJECTS
                action = parsed_data.get("ACTION", "standing and gesturing")
                objects = parsed_data.get("OBJECTS", "thought bubble, notepad")

                # Format into the template prompt
                template_prompt = f"""A clean, cartoon-style black and white stick figure scene on a light beige background. Characters are simple stick figures with expressive faces and exaggerated motion lines. Use a hand-drawn aesthetic with smooth lines and minimal shading. The main figure is {action}. Optional objects or characters include: {objects}. Scene should feel dynamic, minimalistic, and consistent with educational or motivational animated explainer videos."""

                self.log_message(f"Generated prompt: {template_prompt[:100]}...")
                return template_prompt

            except Exception as e:
                self.log_message(f"Error parsing JSON response: {str(e)}")
                self.log_message(f"Raw response: {json_response}")

                # Try to extract information using regex as fallback
                import re
                try:
                    # Look for ACTION and OBJECTS using regex
                    action_match = re.search(r'"ACTION"\s*:\s*"([^"]+)"', json_response)
                    objects_match = re.search(r'"OBJECTS"\s*:\s*"([^"]+)"', json_response)

                    if action_match and objects_match:
                        action = action_match.group(1).strip()
                        objects = objects_match.group(1).strip()

                        template_prompt = f"""A clean, cartoon-style black and white stick figure scene on a light beige background. Characters are simple stick figures with expressive faces and exaggerated motion lines. Use a hand-drawn aesthetic with smooth lines and minimal shading. The main figure is {action}. Optional objects or characters include: {objects}. Scene should feel dynamic, minimalistic, and consistent with educational or motivational animated explainer videos."""

                        self.log_message(f"Generated prompt using regex parsing: {template_prompt[:100]}...")
                        return template_prompt
                except Exception as regex_error:
                    self.log_message(f"Error with regex parsing: {str(regex_error)}")

                # If regex fails, try simple string extraction
                try:
                    # Try to extract using string operations
                    if "ACTION" in json_response and "OBJECTS" in json_response:
                        # Find the position of "ACTION"
                        action_pos = json_response.find('"ACTION"')
                        if action_pos != -1:
                            # Find the position of the first colon after "ACTION"
                            colon_pos = json_response.find(':', action_pos)
                            if colon_pos != -1:
                                # Find the position of the first quote after the colon
                                quote_pos = json_response.find('"', colon_pos)
                                if quote_pos != -1:
                                    # Find the position of the closing quote
                                    end_quote_pos = json_response.find('"', quote_pos + 1)
                                    if end_quote_pos != -1:
                                        action = json_response[quote_pos + 1:end_quote_pos].strip()

                        # Find the position of "OBJECTS"
                        objects_pos = json_response.find('"OBJECTS"')
                        if objects_pos != -1:
                            # Find the position of the first colon after "OBJECTS"
                            colon_pos = json_response.find(':', objects_pos)
                            if colon_pos != -1:
                                # Find the position of the first quote after the colon
                                quote_pos = json_response.find('"', colon_pos)
                                if quote_pos != -1:
                                    # Find the position of the closing quote
                                    end_quote_pos = json_response.find('"', quote_pos + 1)
                                    if end_quote_pos != -1:
                                        objects = json_response[quote_pos + 1:end_quote_pos].strip()

                        # Check if we successfully extracted both values
                        if action and objects:
                            template_prompt = f"""A clean, cartoon-style black and white stick figure scene on a light beige background. Characters are simple stick figures with expressive faces and exaggerated motion lines. Use a hand-drawn aesthetic with smooth lines and minimal shading. The main figure is {action}. Optional objects or characters include: {objects}. Scene should feel dynamic, minimalistic, and consistent with educational or motivational animated explainer videos."""

                            self.log_message(f"Generated prompt using string extraction: {template_prompt[:100]}...")
                            return template_prompt
                except Exception as string_error:
                    self.log_message(f"Error with string extraction: {str(string_error)}")

                # If all parsing fails, use the raw response with the template
                self.log_message("Using raw response with template")
                return f"""A clean, cartoon-style black and white stick figure scene on a light beige background. Characters are simple stick figures with expressive faces and exaggerated motion lines. Use a hand-drawn aesthetic with smooth lines and minimal shading. The main figure is explaining the concept. Optional objects or characters include: elements related to "{line.strip()}". Scene should feel dynamic, minimalistic, and consistent with educational or motivational animated explainer videos."""

        except Exception as e:
            self.log_message(f"Error generating image prompt: {str(e)}")
            # Fallback to basic prompt with template
            return f"""A clean, cartoon-style black and white stick figure scene on a light beige background. Characters are simple stick figures with expressive faces and exaggerated motion lines. Use a hand-drawn aesthetic with smooth lines and minimal shading. The main figure is explaining the concept. Optional objects or characters include: elements related to "{line.strip()}". Scene should feel dynamic, minimalistic, and consistent with educational or motivational animated explainer videos."""

    def _edit_prompt_dialog(self, prompt, line):
        """Show dialog to edit the prompt before generating image"""
        result = {"prompt": prompt, "cancelled": False}

        # Create a dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Edit Image Prompt")
        dialog.geometry("800x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # Create frames
        info_frame = ttk.Frame(dialog, padding=10)
        info_frame.pack(fill=tk.X)

        prompt_frame = ttk.LabelFrame(dialog, text="Edit Prompt", padding=10)
        prompt_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        buttons_frame = ttk.Frame(dialog, padding=10)
        buttons_frame.pack(fill=tk.X)

        # Add original text label
        ttk.Label(info_frame, text="Original Text:").pack(anchor=tk.W)
        original_text = scrolledtext.ScrolledText(info_frame, wrap=tk.WORD, height=3)
        original_text.pack(fill=tk.X, pady=5)
        original_text.insert(tk.END, line)
        original_text.config(state=tk.DISABLED)

        # Add prompt text area
        prompt_text = scrolledtext.ScrolledText(prompt_frame, wrap=tk.WORD)
        prompt_text.pack(fill=tk.BOTH, expand=True)
        prompt_text.insert(tk.END, prompt)

        # Add buttons
        def on_cancel():
            result["cancelled"] = True
            dialog.destroy()

        def on_ok():
            result["prompt"] = prompt_text.get(1.0, tk.END).strip()
            dialog.destroy()

        ttk.Button(buttons_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT, padx=5)
        ttk.Button(buttons_frame, text="OK", command=on_ok).pack(side=tk.RIGHT, padx=5)

        # Wait for the dialog to close
        self.root.wait_window(dialog)
        return result

    def _generate_image_with_prompt(self, prompt, output_file, original_text=""):
        """Generate image using OpenAI DALL-E with a specific prompt"""
        try:
            # Parse resolution - DALL-E 3 only supports specific resolutions
            resolution = self.resolution_var.get().strip()

            # Check if resolution is supported by DALL-E 3
            supported_resolutions = ["1024x1024", "1024x1792", "1792x1024"]
            if resolution not in supported_resolutions:
                self.log_message(f"Resolution {resolution} not supported by DALL-E 3. Using 1792x1024 instead.")
                resolution = "1792x1024"  # Use landscape format as default (closest to 16:9)

            # Show dialog to edit prompt
            edit_result = self._edit_prompt_dialog(prompt, original_text)

            # Check if user cancelled
            if edit_result["cancelled"]:
                self.log_message("Image generation cancelled by user")
                return False

            # Use the edited prompt
            final_prompt = edit_result["prompt"]

            # Create a clean filename from the prompt
            import re
            prompt_words = re.sub(r'[^\w\s]', '', final_prompt.strip().split('\n')[0])[:30]
            clean_prompt = re.sub(r'\s+', '_', prompt_words).lower()

            # Create a unique filename for the image
            timestamp = int(time.time())
            image_filename = f"image_{clean_prompt}_{timestamp}.png"
            cache_image_path = os.path.join(self.cache_dir, image_filename)

            # Log the full prompt for reference
            self.log_message(f"Full prompt for DALL-E: {final_prompt}")

            # Generate image with DALL-E 3
            response = self.openai_client.images.generate(
                model="dall-e-3",
                prompt=final_prompt,
                size=resolution,
                quality="standard",
                n=1,
            )

            # Get image URL
            image_url = response.data[0].url

            # Download image
            import requests
            image_data = requests.get(image_url).content

            # Save image to cache
            with open(cache_image_path, "wb") as f:
                f.write(image_data)

            # Save image to output location
            with open(output_file, "wb") as f:
                f.write(image_data)

            self.log_message(f"Image saved to cache as: {image_filename}")

            return os.path.exists(output_file)

        except Exception as e:
            self.log_message(f"Error generating image: {str(e)}")
            return False

    def _generate_image(self, line, output_file):
        """Generate image using DALL-E"""
        try:
            # Generate a detailed prompt using ChatGPT
            prompt = self._generate_image_prompt(line)

            # Store the prompt for reference
            if hasattr(self, 'generated_prompts'):
                self.generated_prompts.append({
                    'original_text': line.strip(),
                    'generated_prompt': prompt
                })

            # Use the _generate_image_with_prompt method to generate the image
            # Pass the original line text for context in the edit dialog
            return self._generate_image_with_prompt(prompt, output_file, original_text=line.strip())

        except Exception as e:
            self.log_message(f"Error generating image: {str(e)}")
            return False

    def view_generated_prompts(self):
        """View the generated prompts in a dialog"""
        if not hasattr(self, 'last_prompts_file') or not os.path.exists(self.last_prompts_file):
            messagebox.showerror("Error", "No prompts file available. Generate a video first.")
            return

        try:
            # Create a dialog to show prompts
            dialog = tk.Toplevel(self.root)
            dialog.title("Generated Prompts")
            dialog.geometry("800x600")
            dialog.transient(self.root)
            dialog.grab_set()

            # Create a scrolled text widget to show prompts
            prompts_text = scrolledtext.ScrolledText(dialog, wrap=tk.WORD)
            prompts_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Load and display prompts
            with open(self.last_prompts_file, 'r', encoding='utf-8') as f:
                prompts_content = f.read()
                prompts_text.insert(tk.END, prompts_content)

            # Make text read-only
            prompts_text.config(state=tk.DISABLED)

            # Add buttons
            button_frame = ttk.Frame(dialog)
            button_frame.pack(fill=tk.X, padx=10, pady=10)

            # Open file button
            open_file_button = ttk.Button(
                button_frame,
                text="Open File",
                command=lambda: os.startfile(self.last_prompts_file)
            )
            open_file_button.pack(side=tk.LEFT, padx=5)

            # Close button
            close_button = ttk.Button(button_frame, text="Close", command=dialog.destroy)
            close_button.pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to view prompts: {str(e)}")

    def _estimate_segment_durations(self, script, segments, audio_duration):
        """Estimate the duration of each segment based on its length relative to the total script"""
        if not segments or not script.strip():
            return []

        # Calculate the proportion of the total script for each segment
        total_chars = sum(len(segment) for segment in segments)
        segment_proportions = [len(segment) / total_chars for segment in segments]

        # Calculate the duration for each segment
        segment_durations = [proportion * audio_duration for proportion in segment_proportions]

        # Log the estimated durations
        for i, duration in enumerate(segment_durations):
            self.log_message(f"Segment {i+1} estimated duration: {duration:.2f} seconds")

        return segment_durations

    def _create_video_with_custom_durations(self, audio_file, image_files, durations, output_file):
        """Create video with custom durations for each image"""
        try:
            # Load audio
            audio = mp.AudioFileClip(audio_file)
            audio_duration = audio.duration

            # Validate durations
            if len(durations) != len(image_files):
                self.log_message(f"Warning: Number of durations ({len(durations)}) doesn't match number of images ({len(image_files)})")
                # Adjust durations to match number of images
                if len(durations) < len(image_files):
                    # Add equal durations for missing images
                    remaining_duration = audio_duration - sum(durations)
                    remaining_images = len(image_files) - len(durations)
                    if remaining_duration > 0 and remaining_images > 0:
                        equal_duration = remaining_duration / remaining_images
                        durations.extend([equal_duration] * remaining_images)
                    else:
                        # Fallback to equal durations for all
                        durations = [audio_duration / len(image_files)] * len(image_files)
                else:
                    # Truncate extra durations
                    durations = durations[:len(image_files)]

            # Check if total duration matches audio duration
            total_duration = sum(durations)
            if abs(total_duration - audio_duration) > 0.5:  # Allow 0.5 second difference
                self.log_message(f"Warning: Total scene duration ({total_duration:.1f}s) doesn't match audio duration ({audio_duration:.1f}s)")
                # Adjust the last duration to match audio duration
                durations[-1] += (audio_duration - total_duration)

            # Log the durations
            self.log_message(f"Using custom durations for {len(image_files)} images:")
            for i, duration in enumerate(durations):
                self.log_message(f"  Image {i+1}: {duration:.1f} seconds")

            # Create image clips
            image_clips = []
            current_time = 0

            for i, image_file in enumerate(image_files):
                # Get duration for this image
                duration = durations[i]

                # Create image clip
                img_clip = mp.ImageClip(image_file).set_start(current_time).set_duration(duration)
                image_clips.append(img_clip)

                # Update current time
                current_time += duration

            # Concatenate image clips
            video = mp.concatenate_videoclips(image_clips, method="compose")

            # Add audio
            video = video.set_audio(audio)

            # Write video file
            video.write_videofile(output_file, fps=24, codec='libx264', audio_codec='aac')

            return os.path.exists(output_file)

        except Exception as e:
            self.log_message(f"Error creating video with custom durations: {str(e)}")
            return False

    def _create_video(self, audio_file, image_files, output_file, script=None, segments=None):
        """Create video by synchronizing images with audio"""
        try:
            # Load audio
            audio = mp.AudioFileClip(audio_file)
            audio_duration = audio.duration

            # Calculate durations for each image
            if script and segments and len(segments) == len(image_files):
                # Use intelligent duration estimation based on segment length
                self.log_message("Using intelligent duration estimation based on segment length")
                segment_durations = self._estimate_segment_durations(script, segments, audio_duration)
            else:
                # Fallback to equal durations
                self.log_message("Using equal durations for all images")
                segment_durations = [audio_duration / len(image_files)] * len(image_files)

            # Create image clips
            image_clips = []
            current_time = 0

            for i, image_file in enumerate(image_files):
                # Get duration for this segment
                duration = segment_durations[i]

                # Create image clip
                img_clip = mp.ImageClip(image_file).set_start(current_time).set_duration(duration)
                image_clips.append(img_clip)

                # Update current time
                current_time += duration

            # Concatenate image clips
            video = mp.concatenate_videoclips(image_clips, method="compose")

            # Add audio
            video = video.set_audio(audio)

            # Write video file
            video.write_videofile(output_file, fps=24, codec='libx264', audio_codec='aac')

            return os.path.exists(output_file)

        except Exception as e:
            self.log_message(f"Error creating video: {str(e)}")
            return False


if __name__ == "__main__":
    # Start the application directly without checking dependencies
    root = tk.Tk()
    app = ScriptToVideoGenerator(root)
    root.mainloop()
