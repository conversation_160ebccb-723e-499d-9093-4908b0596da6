"""
Boring History Generator
Automated tool for creating YouTube "Boring History for Sleep" videos
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import json
import threading
from datetime import datetime
from openai import OpenAI

# Import our custom modules
from boring_history_templates import *
from flexible_templates import TemplateConfig
from template_configurator import TemplateConfigurator
from minimax_tts import MinimaxTTS
from leonardo_ai import LeonardoAI
from capcut_project_creator import CapCutProjectCreator


class BoringHistoryGenerator:
    """Main application class for Boring History Generator"""

    def __init__(self, root):
        self.root = root
        self.root.title("Boring History Generator - Tạo Video Lịch Sử Tự Động")
        self.root.geometry("1400x900")

        # Initialize variables
        self.openai_client = None
        self.minimax_client = None
        self.leonardo_client = None
        self.capcut_creator = CapCutProjectCreator()
        self.template_config = TemplateConfig()

        # Settings
        self.settings = {
            "openai_api_key": "",
            "openai_base_url": "https://api.openai.com/v1",
            "minimax_api_key": "",
            "minimax_base_url": "https://api.minimax.chat",
            "leonardo_api_key": "",
            "output_directory": "",
            "background_video": "",
            "voice_id": "male-qn-qingse",
            "num_images": 10
        }

        self.load_settings()
        self.create_gui()
        self.initialize_clients()

    def create_gui(self):
        """Create the GUI interface"""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Main tab
        self.main_frame = ttk.Frame(notebook)
        notebook.add(self.main_frame, text="🏠 Trang Chính")

        # Settings tab
        self.settings_frame = ttk.Frame(notebook)
        notebook.add(self.settings_frame, text="⚙️ Cài Đặt")

        # Logs tab
        self.logs_frame = ttk.Frame(notebook)
        notebook.add(self.logs_frame, text="📋 Nhật Ký")

        self.create_main_tab()
        self.create_settings_tab()
        self.create_logs_tab()

    def create_main_tab(self):
        """Create the main tab interface"""
        # Topic input
        topic_frame = ttk.LabelFrame(self.main_frame, text="📝 Chủ Đề Video", padding=10)
        topic_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(topic_frame, text="Nhập chủ đề cho video của bạn:").pack(anchor=tk.W)
        ttk.Label(topic_frame, text="💡 Ví dụ: 'Những Người Hề Cung Đình Thời Trung Cổ', 'Kỹ Thuật Xây Dựng La Mã Cổ Đại'",
                 font=("Arial", 8), foreground="gray").pack(anchor=tk.W, pady=(0,5))

        self.topic_var = tk.StringVar()
        topic_entry = ttk.Entry(topic_frame, textvariable=self.topic_var, width=80, font=("Arial", 11))
        topic_entry.pack(fill=tk.X, pady=5)

        # Generated content display
        content_frame = ttk.LabelFrame(self.main_frame, text="📄 Nội Dung Được Tạo", padding=10)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Title display
        title_label_frame = ttk.Frame(content_frame)
        title_label_frame.pack(fill=tk.X, pady=(0,5))
        ttk.Label(title_label_frame, text="🎬 Tiêu Đề Video:").pack(side=tk.LEFT)
        ttk.Label(title_label_frame, text="(Tiêu đề sẽ được tạo theo format 'Boring History for Sleep')",
                 font=("Arial", 8), foreground="gray").pack(side=tk.LEFT, padx=(10,0))

        self.title_var = tk.StringVar()
        title_entry = ttk.Entry(content_frame, textvariable=self.title_var, width=80, state="readonly", font=("Arial", 10))
        title_entry.pack(fill=tk.X, pady=2)

        # Script display
        script_label_frame = ttk.Frame(content_frame)
        script_label_frame.pack(fill=tk.X, pady=(10,5))
        ttk.Label(script_label_frame, text="📜 Kịch Bản:").pack(side=tk.LEFT)
        ttk.Label(script_label_frame, text="(15 phần, mỗi phần 1000-1100 từ, tổng cộng ~15,000 từ)",
                 font=("Arial", 8), foreground="gray").pack(side=tk.LEFT, padx=(10,0))

        self.script_text = scrolledtext.ScrolledText(content_frame, height=15, wrap=tk.WORD, font=("Arial", 10))
        self.script_text.pack(fill=tk.BOTH, expand=True, pady=2)

        # Control buttons
        button_frame = ttk.Frame(self.main_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # Button descriptions
        button_desc_frame = ttk.Frame(self.main_frame)
        button_desc_frame.pack(fill=tk.X, padx=10, pady=(0,10))

        ttk.Label(button_desc_frame, text="🔧 Chọn chức năng:", font=("Arial", 10, "bold")).pack(anchor=tk.W)

        # Button 1
        btn1_frame = ttk.Frame(button_frame)
        btn1_frame.pack(side=tk.LEFT, padx=5)
        ttk.Button(btn1_frame, text="1️⃣ Tạo Tiêu Đề",
                  command=self.generate_title, width=15).pack()
        ttk.Label(btn1_frame, text="Chỉ tạo tiêu đề video",
                 font=("Arial", 8), foreground="gray").pack()

        # Button 2
        btn2_frame = ttk.Frame(button_frame)
        btn2_frame.pack(side=tk.LEFT, padx=5)
        ttk.Button(btn2_frame, text="2️⃣ Tạo Kịch Bản",
                  command=self.generate_script, width=15).pack()
        ttk.Label(btn2_frame, text="Tạo outline kịch bản",
                 font=("Arial", 8), foreground="gray").pack()

        # Button 3
        btn3_frame = ttk.Frame(button_frame)
        btn3_frame.pack(side=tk.LEFT, padx=5)
        ttk.Button(btn3_frame, text="3️⃣ Tạo Tất Cả",
                  command=self.generate_all_content, width=15).pack()
        ttk.Label(btn3_frame, text="Tạo video hoàn chỉnh",
                 font=("Arial", 8), foreground="gray").pack()

        # Progress section
        progress_frame = ttk.LabelFrame(self.main_frame, text="📊 Tiến Trình", padding=5)
        progress_frame.pack(fill=tk.X, padx=10, pady=5)

        self.progress_var = tk.StringVar(value="🟢 Sẵn sàng tạo nội dung")
        ttk.Label(progress_frame, textvariable=self.progress_var, font=("Arial", 10)).pack(pady=2)
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=2)

    def create_settings_tab(self):
        """Create the settings tab interface"""
        # API Settings
        api_frame = ttk.LabelFrame(self.settings_frame, text="🔑 Cài Đặt API", padding=10)
        api_frame.pack(fill=tk.X, padx=10, pady=5)

        # API description
        api_desc = ttk.Label(api_frame, text="💡 Cần có API keys để sử dụng các dịch vụ AI. Xem hướng dẫn trong README.",
                            font=("Arial", 9), foreground="blue")
        api_desc.grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0,10))

        # OpenAI settings
        ttk.Label(api_frame, text="OpenAI API Key:", font=("Arial", 10, "bold")).grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Label(api_frame, text="(Để tạo tiêu đề, kịch bản, prompts)",
                 font=("Arial", 8), foreground="gray").grid(row=1, column=2, sticky=tk.W, padx=5)
        self.openai_key_var = tk.StringVar(value=self.settings["openai_api_key"])
        ttk.Entry(api_frame, textvariable=self.openai_key_var, width=50, show="*").grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=5)

        ttk.Label(api_frame, text="OpenAI Base URL:").grid(row=3, column=0, sticky=tk.W, pady=(10,2))
        self.openai_url_var = tk.StringVar(value=self.settings["openai_base_url"])
        ttk.Entry(api_frame, textvariable=self.openai_url_var, width=50).grid(row=4, column=0, columnspan=2, sticky=tk.W, padx=5)

        # Minimax settings
        ttk.Label(api_frame, text="Minimax API Key:", font=("Arial", 10, "bold")).grid(row=5, column=0, sticky=tk.W, pady=(15,2))
        ttk.Label(api_frame, text="(Để tạo giọng đọc/TTS)",
                 font=("Arial", 8), foreground="gray").grid(row=5, column=2, sticky=tk.W, padx=5)
        self.minimax_key_var = tk.StringVar(value=self.settings["minimax_api_key"])
        ttk.Entry(api_frame, textvariable=self.minimax_key_var, width=50, show="*").grid(row=6, column=0, columnspan=2, sticky=tk.W, padx=5)

        ttk.Label(api_frame, text="Minimax Base URL:").grid(row=7, column=0, sticky=tk.W, pady=(10,2))
        self.minimax_url_var = tk.StringVar(value=self.settings["minimax_base_url"])
        ttk.Entry(api_frame, textvariable=self.minimax_url_var, width=50).grid(row=8, column=0, columnspan=2, sticky=tk.W, padx=5)

        # Leonardo AI settings
        ttk.Label(api_frame, text="Leonardo AI API Key:", font=("Arial", 10, "bold")).grid(row=9, column=0, sticky=tk.W, pady=(15,2))
        ttk.Label(api_frame, text="(Để tạo hình ảnh minh họa)",
                 font=("Arial", 8), foreground="gray").grid(row=9, column=2, sticky=tk.W, padx=5)
        self.leonardo_key_var = tk.StringVar(value=self.settings["leonardo_api_key"])
        ttk.Entry(api_frame, textvariable=self.leonardo_key_var, width=50, show="*").grid(row=10, column=0, columnspan=2, sticky=tk.W, padx=5)

        # File Settings
        file_frame = ttk.LabelFrame(self.settings_frame, text="📁 Cài Đặt File", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)

        # Output directory
        ttk.Label(file_frame, text="Thư Mục Xuất File:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Label(file_frame, text="(Nơi lưu tất cả file được tạo)",
                 font=("Arial", 8), foreground="gray").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.output_dir_var = tk.StringVar(value=self.settings["output_directory"])
        ttk.Entry(file_frame, textvariable=self.output_dir_var, width=40).grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5)
        ttk.Button(file_frame, text="📂 Chọn",
                  command=self.browse_output_dir).grid(row=1, column=2, padx=5)

        # Background video
        ttk.Label(file_frame, text="Video Nền (Tùy Chọn):", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, pady=(15,2))
        ttk.Label(file_frame, text="(Video chạy nền trong suốt video)",
                 font=("Arial", 8), foreground="gray").grid(row=2, column=2, sticky=tk.W, padx=5)
        self.bg_video_var = tk.StringVar(value=self.settings["background_video"])
        ttk.Entry(file_frame, textvariable=self.bg_video_var, width=40).grid(row=3, column=0, columnspan=2, sticky=tk.W, padx=5)
        ttk.Button(file_frame, text="📂 Chọn",
                  command=self.browse_bg_video).grid(row=3, column=2, padx=5)

        # Generation Settings
        gen_frame = ttk.LabelFrame(self.settings_frame, text="🎛️ Cài Đặt Tạo Nội Dung", padding=10)
        gen_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(gen_frame, text="Voice ID:", font=("Arial", 10, "bold")).grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Label(gen_frame, text="(ID giọng đọc Minimax, mặc định: male-qn-qingse)",
                 font=("Arial", 8), foreground="gray").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.voice_id_var = tk.StringVar(value=self.settings["voice_id"])
        ttk.Entry(gen_frame, textvariable=self.voice_id_var, width=30).grid(row=1, column=0, sticky=tk.W, padx=5)

        ttk.Label(gen_frame, text="Số Lượng Hình Ảnh:", font=("Arial", 10, "bold")).grid(row=2, column=0, sticky=tk.W, pady=(10,2))
        ttk.Label(gen_frame, text="(Số ảnh minh họa sẽ tạo, 5-20 ảnh)",
                 font=("Arial", 8), foreground="gray").grid(row=2, column=2, sticky=tk.W, padx=5)
        self.num_images_var = tk.IntVar(value=self.settings["num_images"])
        ttk.Spinbox(gen_frame, from_=5, to=20, textvariable=self.num_images_var, width=10).grid(row=3, column=0, sticky=tk.W, padx=5)

        # Save settings button
        button_frame = ttk.Frame(self.settings_frame)
        button_frame.pack(pady=15)

        ttk.Button(button_frame, text="💾 Lưu Cài Đặt",
                  command=self.save_settings, width=15).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🎨 Tùy Chỉnh Templates",
                  command=self.open_template_configurator, width=20).pack(side=tk.LEFT, padx=5)

        # Help section
        help_frame = ttk.LabelFrame(self.settings_frame, text="❓ Trợ Giúp", padding=10)
        help_frame.pack(fill=tk.X, padx=10, pady=5)

        help_text = """
🔹 OpenAI API: Cần để tạo tiêu đề, kịch bản và prompts hình ảnh
🔹 Minimax API: Cần để tạo giọng đọc (Text-to-Speech)
🔹 Leonardo AI API: Cần để tạo hình ảnh minh họa
🔹 Tùy Chỉnh Templates: Điều chỉnh style và format của prompts
🔹 Xem file README để biết cách lấy API keys
        """
        ttk.Label(help_frame, text=help_text, font=("Arial", 9), justify=tk.LEFT).pack(anchor=tk.W)

    def create_logs_tab(self):
        """Create the logs tab interface"""
        # Log description
        desc_frame = ttk.Frame(self.logs_frame)
        desc_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(desc_frame, text="📋 Nhật Ký Hoạt Động", font=("Arial", 12, "bold")).pack(side=tk.LEFT)
        ttk.Label(desc_frame, text="(Theo dõi quá trình tạo nội dung)",
                 font=("Arial", 9), foreground="gray").pack(side=tk.LEFT, padx=(10,0))

        self.log_text = scrolledtext.ScrolledText(self.logs_frame, wrap=tk.WORD, font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Clear logs button
        button_frame = ttk.Frame(self.logs_frame)
        button_frame.pack(pady=5)
        ttk.Button(button_frame, text="🗑️ Xóa Nhật Ký",
                  command=self.clear_logs, width=15).pack()

    def log_message(self, message):
        """Add message to logs"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_logs(self):
        """Clear the logs"""
        self.log_text.delete(1.0, tk.END)

    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory()
        if directory:
            self.output_dir_var.set(directory)

    def browse_bg_video(self):
        """Browse for background video"""
        filename = filedialog.askopenfilename(
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv")]
        )
        if filename:
            self.bg_video_var.set(filename)

    def load_settings(self):
        """Load settings from file"""
        try:
            if os.path.exists("boring_history_settings.json"):
                with open("boring_history_settings.json", "r") as f:
                    saved_settings = json.load(f)
                    self.settings.update(saved_settings)
        except Exception as e:
            print(f"Error loading settings: {e}")

    def save_settings(self):
        """Save settings to file"""
        try:
            # Update settings from GUI
            self.settings["openai_api_key"] = self.openai_key_var.get()
            self.settings["openai_base_url"] = self.openai_url_var.get()
            self.settings["minimax_api_key"] = self.minimax_key_var.get()
            self.settings["minimax_base_url"] = self.minimax_url_var.get()
            self.settings["leonardo_api_key"] = self.leonardo_key_var.get()
            self.settings["output_directory"] = self.output_dir_var.get()
            self.settings["background_video"] = self.bg_video_var.get()
            self.settings["voice_id"] = self.voice_id_var.get()
            self.settings["num_images"] = self.num_images_var.get()

            # Save to file
            with open("boring_history_settings.json", "w") as f:
                json.dump(self.settings, f, indent=2)

            messagebox.showinfo("Thành Công", "Cài đặt đã được lưu thành công! ✅")
            self.initialize_clients()

        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi lưu cài đặt: {e}")

    def initialize_clients(self):
        """Initialize API clients"""
        try:
            # OpenAI client
            if self.settings["openai_api_key"]:
                self.openai_client = OpenAI(
                    api_key=self.settings["openai_api_key"],
                    base_url=self.settings["openai_base_url"]
                )

            # Minimax client
            if self.settings["minimax_api_key"]:
                self.minimax_client = MinimaxTTS(
                    api_key=self.settings["minimax_api_key"],
                    base_url=self.settings["minimax_base_url"]
                )

            # Leonardo AI client
            if self.settings["leonardo_api_key"]:
                self.leonardo_client = LeonardoAI(
                    api_key=self.settings["leonardo_api_key"]
                )

        except Exception as e:
            self.log_message(f"Error initializing clients: {e}")

    def open_template_configurator(self):
        """Open template configurator window"""
        try:
            configurator = TemplateConfigurator(self.root)

            # Set the config after GUI is created
            configurator.config = self.template_config

            # Update GUI after all widgets are created
            def update_gui_delayed():
                try:
                    configurator.update_gui_from_config()
                except Exception as e:
                    print(f"Error updating GUI: {e}")

            self.root.after(100, update_gui_delayed)

            # Wait for window to close
            self.root.wait_window(configurator.window)

            # Update our template config (get config before window is destroyed)
            try:
                self.template_config = configurator.config  # Use config directly instead of get_config()
                self.log_message("Template configuration updated")
            except Exception as e:
                self.log_message(f"Error updating template config: {e}")

        except Exception as e:
            self.log_message(f"Error opening template configurator: {e}")
            messagebox.showerror("Error", f"Error opening template configurator: {e}")

    def generate_title(self):
        """Generate video title"""
        if not self.topic_var.get().strip():
            messagebox.showwarning("Cảnh Báo", "Vui lòng nhập chủ đề trước! 📝")
            return

        if not self.openai_client:
            messagebox.showerror("Lỗi", "OpenAI client chưa được khởi tạo. Vui lòng kiểm tra cài đặt API. 🔑")
            return

        def generate():
            try:
                self.progress_var.set("🔄 Đang tạo tiêu đề...")
                self.progress_bar.start()

                prompt = self.template_config.get_title_prompt(self.topic_var.get().strip())

                response = self.openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=500,
                    temperature=0.8
                )

                titles = response.choices[0].message.content.strip()
                # Extract first title from the list
                first_title = titles.split('\n')[0].strip()
                if first_title.startswith('1.'):
                    first_title = first_title[2:].strip()

                self.title_var.set(first_title)
                self.log_message(f"✅ Đã tạo tiêu đề: {first_title}")
                self.progress_var.set("✅ Tạo tiêu đề thành công!")

            except Exception as e:
                self.log_message(f"❌ Lỗi tạo tiêu đề: {e}")
                messagebox.showerror("Lỗi", f"Lỗi khi tạo tiêu đề: {e}")
                self.progress_var.set("❌ Lỗi tạo tiêu đề")
            finally:
                self.progress_bar.stop()

        threading.Thread(target=generate, daemon=True).start()

    def generate_script(self):
        """Generate script outline"""
        if not self.topic_var.get().strip():
            messagebox.showwarning("Cảnh Báo", "Vui lòng nhập chủ đề trước! 📝")
            return

        if not self.openai_client:
            messagebox.showerror("Lỗi", "OpenAI client chưa được khởi tạo. Vui lòng kiểm tra cài đặt API. 🔑")
            return

        def generate():
            try:
                self.progress_var.set("🔄 Đang tạo outline kịch bản...")
                self.progress_bar.start()

                prompt = self.template_config.get_script_prompt(self.topic_var.get().strip())

                response = self.openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=2000,
                    temperature=0.7
                )

                script_outline = response.choices[0].message.content.strip()
                self.script_text.delete(1.0, tk.END)
                self.script_text.insert(1.0, script_outline)

                self.log_message("✅ Đã tạo outline kịch bản thành công")
                self.progress_var.set("✅ Tạo outline kịch bản thành công!")

            except Exception as e:
                self.log_message(f"❌ Lỗi tạo kịch bản: {e}")
                messagebox.showerror("Lỗi", f"Lỗi khi tạo kịch bản: {e}")
                self.progress_var.set("❌ Lỗi tạo kịch bản")
            finally:
                self.progress_bar.stop()

        threading.Thread(target=generate, daemon=True).start()

    def generate_all_content(self):
        """Generate all content and create project"""
        if not self.topic_var.get().strip():
            messagebox.showwarning("Cảnh Báo", "Vui lòng nhập chủ đề trước! 📝")
            return

        if not all([self.openai_client, self.minimax_client, self.leonardo_client]):
            messagebox.showerror("Lỗi", "Chưa đủ API clients. Vui lòng kiểm tra cài đặt API. 🔑")
            return

        if not self.settings["output_directory"]:
            messagebox.showwarning("Cảnh Báo", "Vui lòng chọn thư mục xuất file trong cài đặt! 📁")
            return

        def generate_all():
            try:
                topic = self.topic_var.get().strip()
                self.log_message(f"🚀 Bắt đầu tạo nội dung hoàn chỉnh cho chủ đề: {topic}")

                # Step 1: Generate title
                self.progress_var.set("🔄 Bước 1/7: Đang tạo tiêu đề...")
                title = self._generate_title_internal(topic)
                if not title:
                    raise Exception("Không thể tạo tiêu đề")

                self.title_var.set(title)
                self.log_message(f"✅ Đã tạo tiêu đề: {title}")

                # Step 2: Generate full script
                self.progress_var.set("🔄 Bước 2/7: Đang tạo kịch bản đầy đủ...")
                full_script = self._generate_full_script(topic)
                if not full_script:
                    raise Exception("Không thể tạo kịch bản")

                self.script_text.delete(1.0, tk.END)
                self.script_text.insert(1.0, full_script)
                self.log_message("✅ Đã tạo kịch bản đầy đủ thành công")

                # Step 3: Create project folder structure
                self.progress_var.set("🔄 Bước 3/7: Đang tạo cấu trúc thư mục...")
                project_name = self._sanitize_filename(title)
                folders = self.capcut_creator.create_project_folder_structure(
                    self.settings["output_directory"], project_name
                )
                self.log_message(f"✅ Đã tạo thư mục dự án: {folders['project']}")

                # Save script to file
                script_file = os.path.join(folders["scripts"], "full_script.txt")
                with open(script_file, "w", encoding="utf-8") as f:
                    f.write(full_script)

                # Step 4: Generate image prompts
                self.progress_var.set("🔄 Bước 4/7: Đang tạo prompts hình ảnh...")
                image_prompts = self._generate_image_prompts(full_script)
                if not image_prompts:
                    raise Exception("Không thể tạo prompts hình ảnh")

                # Save prompts to file
                prompts_file = os.path.join(folders["prompts"], "image_prompts.txt")
                with open(prompts_file, "w", encoding="utf-8") as f:
                    for i, prompt in enumerate(image_prompts, 1):
                        f.write(f"Prompt {i}: {prompt}\n\n")

                self.log_message(f"✅ Đã tạo {len(image_prompts)} prompts hình ảnh")

                # Step 5: Generate voice
                self.progress_var.set("🔄 Bước 5/7: Đang tạo giọng đọc...")
                audio_file = os.path.join(folders["audio"], "narration.mp3")
                if not self._generate_voice(full_script, audio_file):
                    raise Exception("Không thể tạo giọng đọc")

                self.log_message(f"✅ Đã tạo giọng đọc: {audio_file}")

                # Step 6: Generate images
                self.progress_var.set("🔄 Bước 6/7: Đang tạo hình ảnh...")
                image_files = self._generate_images(image_prompts, folders["images"])
                if not image_files:
                    raise Exception("Không thể tạo hình ảnh")

                self.log_message(f"✅ Đã tạo {len(image_files)} hình ảnh")

                # Step 7: Create CapCut project
                self.progress_var.set("🔄 Bước 7/7: Đang tạo project CapCut...")
                project_file = self.capcut_creator.create_project(
                    project_name=project_name,
                    audio_file=audio_file,
                    image_files=image_files,
                    output_dir=folders["capcut"],
                    background_video=self.settings["background_video"] if self.settings["background_video"] else None
                )

                if project_file:
                    self.log_message(f"✅ Đã tạo CapCut project: {project_file}")

                # Success message
                self.progress_var.set("🎉 Hoàn thành tạo nội dung!")
                messagebox.showinfo("Thành Công! 🎉",
                    f"Đã tạo nội dung thành công!\n\n"
                    f"📁 Thư mục dự án: {folders['project']}\n"
                    f"🎬 CapCut project: {project_file}\n\n"
                    f"💡 Mở CapCut PC và import file .cep để chỉnh sửa video!")

            except Exception as e:
                self.log_message(f"❌ Lỗi trong quá trình tạo nội dung: {e}")
                messagebox.showerror("Lỗi", f"Lỗi trong quá trình tạo nội dung: {e}")
                self.progress_var.set("❌ Lỗi tạo nội dung")
            finally:
                self.progress_bar.stop()

        threading.Thread(target=generate_all, daemon=True).start()

    def _generate_title_internal(self, topic):
        """Internal method to generate title"""
        try:
            prompt = self.template_config.get_title_prompt(topic)

            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.8
            )

            titles = response.choices[0].message.content.strip()
            # Extract first title from the list
            first_title = titles.split('\n')[0].strip()
            if first_title.startswith('1.'):
                first_title = first_title[2:].strip()

            return first_title

        except Exception as e:
            self.log_message(f"Error generating title: {e}")
            return None

    def _generate_full_script(self, topic):
        """Generate full script using template config"""
        try:
            # First get the outline
            outline_prompt = self.template_config.get_script_prompt(topic)

            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": outline_prompt}],
                max_tokens=2000,
                temperature=0.7
            )

            outline = response.choices[0].message.content.strip()
            self.log_message("✅ Đã tạo outline kịch bản")

            # Now generate each section
            full_script = outline + "\n\n" + "="*50 + "\nKỊCH BẢN ĐẦY ĐỦ:\n" + "="*50 + "\n\n"

            num_sections = self.template_config.script_num_sections
            for i in range(1, num_sections + 1):
                self.log_message(f"🔄 Đang tạo phần {i}/{num_sections}...")

                words_per_section = self.template_config.script_words_per_section
                section_prompt = f"""
                Based on the outline you provided earlier, write Section {i} of the script.
                Follow the exact specifications:
                - {words_per_section}-{words_per_section + 100} words
                - Second-person present tense
                - {self.template_config.script_style}
                - Include 1 mainstream historical fact, 1 quirky tidbit, 1 scholarly debate
                - 3 light jokes per section
                - Start with "Section {i}" heading only
                - End with [Word count: ####] and >>> Awaiting "CONTINUE"

                Write ONLY Section {i} content, nothing else.
                """

                section_response = self.openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[
                        {"role": "user", "content": outline_prompt},
                        {"role": "assistant", "content": outline},
                        {"role": "user", "content": section_prompt}
                    ],
                    max_tokens=1500,
                    temperature=0.7
                )

                section_content = section_response.choices[0].message.content.strip()
                full_script += section_content + "\n\n"

                # Small delay to avoid rate limiting
                import time
                time.sleep(1)

            return full_script

        except Exception as e:
            self.log_message(f"Error generating full script: {e}")
            return None

    def _generate_image_prompts(self, script):
        """Generate image prompts from script using template config"""
        try:
            # Split script into chunks for image generation
            script_sections = script.split("Section ")
            if len(script_sections) > 1:
                script_sections = script_sections[1:]  # Remove the part before first section

            # Take first few sections for image generation
            num_images = min(self.settings["num_images"], len(script_sections))
            selected_sections = script_sections[:num_images]

            all_prompts = []

            for i, section in enumerate(selected_sections):
                self.log_message(f"Generating image prompt {i+1}/{num_images}...")

                # Clean up section text
                section_text = section.split(">>>")[0].strip()  # Remove "Awaiting CONTINUE" part
                section_text = section_text[:1000]  # Limit length

                prompt = self.template_config.get_image_prompt(section_text, 1)

                response = self.openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=300,
                    temperature=0.7
                )

                image_prompt = response.choices[0].message.content.strip()
                # Extract just the prompt part
                if "Prompt 1:" in image_prompt:
                    image_prompt = image_prompt.split("Prompt 1:")[1].strip()

                all_prompts.append(image_prompt)

                import time
                time.sleep(0.5)

            return all_prompts

        except Exception as e:
            self.log_message(f"Error generating image prompts: {e}")
            return []

    def _generate_voice(self, script, output_file):
        """Generate voice from script"""
        try:
            # Clean script for TTS (remove section headers, word counts, etc.)
            clean_script = self._clean_script_for_tts(script)

            # Generate voice using Minimax
            success = self.minimax_client.generate_speech_long_text(
                text=clean_script,
                output_file=output_file,
                voice_id=self.settings["voice_id"]
            )

            return success

        except Exception as e:
            self.log_message(f"Error generating voice: {e}")
            return False

    def _generate_images(self, prompts, output_dir):
        """Generate images from prompts"""
        try:
            image_files = self.leonardo_client.batch_generate_images(
                prompts=prompts,
                output_dir=output_dir,
                width=1024,
                height=1024
            )

            return image_files

        except Exception as e:
            self.log_message(f"Error generating images: {e}")
            return []

    def _clean_script_for_tts(self, script):
        """Clean script for TTS by removing formatting"""
        lines = script.split('\n')
        clean_lines = []

        for line in lines:
            line = line.strip()
            # Skip section headers, word counts, and other formatting
            if (line.startswith("Section ") or
                line.startswith("[Word count:") or
                line.startswith(">>>") or
                line.startswith("=") or
                line == "FULL SCRIPT:" or
                not line):
                continue

            clean_lines.append(line)

        return ' '.join(clean_lines)

    def _sanitize_filename(self, filename):
        """Sanitize filename for file system"""
        import re
        # Remove invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        # Replace spaces and special chars with underscores
        filename = re.sub(r'[^\w\-_\.]', '_', filename)
        # Remove multiple underscores
        filename = re.sub(r'_+', '_', filename)
        # Limit length
        return filename[:100]


def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = BoringHistoryGenerator(root)
    root.mainloop()


if __name__ == "__main__":
    main()
