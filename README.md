# YouTube Channel Content Repurposer

A comprehensive tool for repurposing YouTube channel content by:
- Downloading transcripts from YouTube videos
- Rewriting content using AI (ChatGPT)
- Generating AI voice narration
- Creating subtitles
- Merging with background videos
- Uploading to YouTube

## Features

- **Channel Scraping**: Extract videos from a YouTube channel
- **Content Rewriting**: Use OpenAI's GPT models to rewrite content
- **Voice Generation**: Create natural-sounding voice narration using ElevenLabs, Google TTS, or Azure TTS
- **Subtitle Generation**: Create and format subtitles with proper timing
- **Video Composition**: Merge audio with background video, add subtitles, create intros/outros
- **YouTube Upload**: Upload videos directly to YouTube with metadata
- **Batch Processing**: Process multiple videos in one go
- **Project Management**: Organize files and track progress

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/youtube-channel-content-repurposer.git
cd youtube-channel-content-repurposer
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up API keys:
   - Create a `.env` file in the project root with the following variables:
   ```
   OPENAI_API_KEY=your_openai_api_key
   ELEVENLABS_API_KEY=your_elevenlabs_api_key
   YOUTUBE_API_KEY=your_youtube_api_key
   ```

4. For YouTube uploads:
   - Create a project in the [Google Cloud Console](https://console.cloud.google.com/)
   - Enable the YouTube Data API v3
   - Create OAuth 2.0 credentials and download the client secrets file

## Usage

### Running the Application

```bash
python channel_content_repurposer_app.py
```

### Setup Tab

1. Set your project directory where all files will be saved
2. Select a background video to use for all generated videos
3. Enter your API keys (OpenAI for content rewriting, ElevenLabs for voice generation)
4. Select voice preferences
5. Save settings

### Channel Tab

1. Enter a YouTube channel URL
2. Fetch videos from the channel
3. Select videos to process
4. Download transcripts for selected videos

### Content Tab

1. Select a video to work with
2. View the original transcript
3. Rewrite the content using ChatGPT
4. Save the rewritten content
5. Batch process all transcripts

### Voice Tab

1. Select a video to work with
2. View the rewritten content
3. Generate voice narration
4. Generate subtitles
5. Batch process all content

### Video Tab

1. Select a video to work with
2. Create the final video with audio and subtitles
3. Batch process all videos

### Upload Tab

1. Authenticate with YouTube
2. Select a video to upload
3. Edit metadata (title, description, tags)
4. Upload to YouTube

### Batch Tab

1. Enter a YouTube channel URL
2. Specify the number of videos to fetch
3. Select videos to process
4. Choose which steps to perform (download, rewrite, voice, subtitles, video, upload)
5. Start batch processing

## Original Tools

### Usage of getmp3.py
1. Enter a link of a youtube video which you want to download as MP3 file.
2. That's it!

### Usage of merge.py
1. Select the video file
2. Select the audio file
3. Specify the start time of the audio in the video
4. Merges the video and audio into one final video
5. Give a final video a name of your choice

### Usage of youtube_audio_video_merger.py
1. Enter YouTube URLs to download audio from
2. Select a background video
3. Merge the downloaded audio with the background video
4. Save the output videos

## Project Structure

```
youtube-channel-content-repurposer/
├── channel_content_repurposer.py       # Original GUI class
├── channel_content_repurposer_app.py   # Enhanced application
├── youtube_api_handler.py              # YouTube API interactions
├── ai_content_generator.py             # AI content generation
├── tts_engine.py                       # Text-to-speech functionality
├── subtitle_generator.py               # Subtitle generation and formatting
├── video_composer.py                   # Video composition and editing
├── project_manager.py                  # Project management
├── getmp3.py                           # Simple YouTube audio downloader
├── merge.py                            # Simple audio/video merger
├── youtube_audio_video_merger.py       # YouTube audio with video merger
├── requirements.txt                    # Dependencies
└── README.md                           # Documentation
```

## Dependencies

- **Core**: moviepy, requests, yt-dlp, python-dotenv
- **YouTube API**: google-api-python-client, google-auth-oauthlib
- **AI**: openai
- **TTS**: elevenlabs, gtts, pyttsx3, azure-cognitiveservices-speech
- **Subtitles**: pysrt, pydub
- **GUI**: tkinter

## Contributions
Contributions are always welcome. If you find a bug or have an idea for a new feature, feel free to open an issue or a pull request.

## License
This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [yt-dlp](https://github.com/yt-dlp/yt-dlp) for YouTube downloading
- [OpenAI](https://openai.com/) for content generation
- [ElevenLabs](https://elevenlabs.io/) for voice generation
- [MoviePy](https://zulko.github.io/moviepy/) for video editing
