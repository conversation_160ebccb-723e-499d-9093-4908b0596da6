"""
Test Vietnamese GUI
Simple test to check if the Vietnamese interface works correctly
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add current directory to path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from boring_history_generator import BoringHistoryGenerator
    GUI_AVAILABLE = True
except ImportError as e:
    print(f"Cannot import GUI: {e}")
    GUI_AVAILABLE = False


def test_gui_elements():
    """Test if GUI elements display Vietnamese text correctly"""
    print("=== Testing Vietnamese GUI Elements ===")
    
    if not GUI_AVAILABLE:
        print("❌ GUI not available for testing")
        return False
    
    try:
        # Create a test window
        root = tk.Tk()
        root.title("Test Vietnamese GUI")
        root.geometry("400x300")
        
        # Test Vietnamese text display
        test_frame = ttk.LabelFrame(root, text="🧪 Test Tiếng Việt", padding=10)
        test_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Test various Vietnamese characters and emojis
        test_texts = [
            "✅ Tạo tiêu đề thành công!",
            "🔄 Đang tạo kịch bản...",
            "📁 Thư mục xuất file",
            "⚙️ Cài đặt API",
            "🎬 Tiêu đề video",
            "📋 Nhật ký hoạt động",
            "💾 Lưu cài đặt",
            "🎨 Tùy chỉnh templates"
        ]
        
        for i, text in enumerate(test_texts):
            ttk.Label(test_frame, text=text, font=("Arial", 10)).pack(anchor=tk.W, pady=2)
        
        # Test button
        def show_success():
            messagebox.showinfo("Thành Công! 🎉", 
                "Giao diện tiếng Việt hoạt động tốt!\n\n"
                "✅ Hiển thị emoji\n"
                "✅ Hiển thị tiếng Việt\n"
                "✅ Font chữ rõ ràng")
            root.destroy()
        
        ttk.Button(test_frame, text="🧪 Test Messagebox", 
                  command=show_success).pack(pady=10)
        
        ttk.Label(test_frame, text="Đóng cửa sổ này để tiếp tục test...", 
                 font=("Arial", 9), foreground="gray").pack(pady=5)
        
        print("✅ Test window created successfully")
        print("📝 Check if Vietnamese text displays correctly")
        
        # Run the test window
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing GUI elements: {e}")
        return False


def test_main_application():
    """Test the main application with Vietnamese interface"""
    print("\n=== Testing Main Application ===")
    
    if not GUI_AVAILABLE:
        print("❌ Main application not available for testing")
        return False
    
    try:
        print("🚀 Starting main application...")
        print("📝 Check the following in the GUI:")
        print("   - Tab names are in Vietnamese")
        print("   - Button labels are in Vietnamese")
        print("   - Help text is in Vietnamese")
        print("   - Emojis display correctly")
        print("   - Progress messages are in Vietnamese")
        
        # Create and run the main application
        root = tk.Tk()
        app = BoringHistoryGenerator(root)
        
        print("✅ Main application started successfully")
        print("🔍 Manually test the interface and close when done")
        
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing main application: {e}")
        return False


def test_font_support():
    """Test if the system supports required fonts and characters"""
    print("\n=== Testing Font Support ===")
    
    try:
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Test fonts
        fonts_to_test = ["Arial", "Segoe UI", "Tahoma", "Verdana"]
        
        for font_name in fonts_to_test:
            try:
                test_label = tk.Label(root, text="Test Tiếng Việt 🎉", font=(font_name, 10))
                print(f"✅ Font '{font_name}' is available")
            except Exception:
                print(f"❌ Font '{font_name}' is not available")
        
        # Test emoji support
        emoji_test = "🎬🔄📁⚙️📋💾🎨✅❌🔑📝🚀"
        test_label = tk.Label(root, text=emoji_test, font=("Arial", 12))
        print(f"✅ Emoji test: {emoji_test}")
        
        # Test Vietnamese characters
        vietnamese_test = "àáảãạăắằẳẵặâấầẩẫậèéẻẽẹêếềểễệìíỉĩịòóỏõọôốồổỗộơớờởỡợùúủũụưứừửữựỳýỷỹỵđ"
        test_label = tk.Label(root, text=vietnamese_test, font=("Arial", 10))
        print(f"✅ Vietnamese characters test: {vietnamese_test[:20]}...")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Error testing font support: {e}")
        return False


def main():
    """Run all Vietnamese GUI tests"""
    print("🧪 Vietnamese GUI Test Suite")
    print("=" * 50)
    
    # Test 1: Font support
    font_ok = test_font_support()
    
    # Test 2: GUI elements
    if font_ok:
        elements_ok = test_gui_elements()
    else:
        print("⚠️ Skipping GUI elements test due to font issues")
        elements_ok = False
    
    # Test 3: Main application (optional)
    if elements_ok:
        response = input("\nDo you want to test the main application? (y/n): ")
        if response.lower() == 'y':
            app_ok = test_main_application()
        else:
            print("⏭️ Skipping main application test")
            app_ok = True
    else:
        print("⚠️ Skipping main application test due to GUI issues")
        app_ok = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   Font Support: {'✅ PASS' if font_ok else '❌ FAIL'}")
    print(f"   GUI Elements: {'✅ PASS' if elements_ok else '❌ FAIL'}")
    print(f"   Main App: {'✅ PASS' if app_ok else '⏭️ SKIPPED'}")
    
    if font_ok and elements_ok:
        print("\n🎉 Vietnamese GUI is working correctly!")
        print("💡 You can now run: python boring_history_generator.py")
    else:
        print("\n⚠️ Some issues detected with Vietnamese GUI")
        print("💡 Try installing additional fonts or updating your system")
    
    print("\n📚 Features of the Vietnamese GUI:")
    print("   🏠 Trang Chính - Main content creation")
    print("   ⚙️ Cài Đặt - API and file settings")
    print("   📋 Nhật Ký - Activity logs")
    print("   🎨 Tùy Chỉnh Templates - Customize prompts")
    print("   🔄 Real-time progress in Vietnamese")
    print("   💬 Vietnamese error/success messages")


if __name__ == "__main__":
    main()
