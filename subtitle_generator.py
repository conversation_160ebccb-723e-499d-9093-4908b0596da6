"""
Subtitle Generator for Channel Content Repurposer

This module handles subtitle generation and formatting:
- Creating SRT subtitle files from text
- Timing alignment with audio
- Formatting and styling subtitles
- Converting between subtitle formats
"""

import os
import re
import json
import time
from datetime import timedelta
from enum import Enum


class SubtitleFormat(Enum):
    """Enum for supported subtitle formats"""
    SRT = "srt"
    VTT = "vtt"
    ASS = "ass"


class SubtitleGenerator:
    """Main class for generating and managing subtitles"""

    def __init__(self, format=SubtitleFormat.SRT):
        """
        Initialize the Subtitle Generator

        Args:
            format (SubtitleFormat): The subtitle format to use
        """
        self.format = format

        # Try to import required packages
        try:
            import pysrt
            self.pysrt_available = True
        except ImportError:
            self.pysrt_available = False
            print("pysrt not available. Install with: pip install pysrt")

        try:
            from pydub import AudioSegment
            self.pydub_available = True
            self.AudioSegment = AudioSegment
        except ImportError:
            self.pydub_available = False
            print("pydub not available. Install with: pip install pydub")

    def generate_subtitles_from_text(self, text, output_file, words_per_subtitle=10,
                                    chars_per_second=15, style=None):
        """
        Generate subtitles from plain text

        Args:
            text (str): Text to convert to subtitles
            output_file (str): Path to save the subtitle file
            words_per_subtitle (int, optional): Number of words per subtitle
            chars_per_second (int, optional): Characters per second for timing
            style (dict, optional): Styling options for the subtitles

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Split text into sentences
            sentences = re.split(r'(?<=[.!?])\s+', text)

            # Create subtitles based on format
            if self.format == SubtitleFormat.SRT:
                return self._generate_srt(sentences, output_file, words_per_subtitle, chars_per_second, style)
            elif self.format == SubtitleFormat.VTT:
                return self._generate_vtt(sentences, output_file, words_per_subtitle, chars_per_second, style)
            elif self.format == SubtitleFormat.ASS:
                return self._generate_ass(sentences, output_file, words_per_subtitle, chars_per_second, style)
            else:
                print(f"Unsupported subtitle format: {self.format}")
                return False

        except Exception as e:
            print(f"Error generating subtitles: {str(e)}")
            return False

    def _generate_srt(self, sentences, output_file, words_per_subtitle, chars_per_second, style):
        """Generate SRT format subtitles"""
        if not self.pysrt_available:
            print("pysrt not available. Install with: pip install pysrt")
            return self._generate_srt_manual(sentences, output_file, words_per_subtitle, chars_per_second)

        import pysrt

        # Create a new SRT file
        subs = pysrt.SubRipFile()

        # Process sentences into subtitles
        current_time = 0
        subtitle_index = 1

        for sentence in sentences:
            if not sentence.strip():
                continue

            # Split long sentences into chunks
            words = sentence.split()
            for i in range(0, len(words), words_per_subtitle):
                chunk = ' '.join(words[i:i+words_per_subtitle])
                if not chunk.strip():
                    continue

                # Estimate duration based on character count
                char_count = len(chunk)
                duration = max(1, char_count / chars_per_second)  # At least 1 second per subtitle

                # Create subtitle
                start_time = timedelta(seconds=current_time)
                end_time = timedelta(seconds=current_time + duration)

                sub = pysrt.SubRipItem(
                    index=subtitle_index,
                    start=pysrt.SubRipTime(milliseconds=int(start_time.total_seconds() * 1000)),
                    end=pysrt.SubRipTime(milliseconds=int(end_time.total_seconds() * 1000)),
                    text=chunk
                )

                subs.append(sub)

                # Update current time and index
                current_time += duration
                subtitle_index += 1

        # Save SRT file
        subs.save(output_file, encoding='utf-8')

        return os.path.exists(output_file)

    def _generate_srt_manual(self, sentences, output_file, words_per_subtitle, chars_per_second):
        """Generate SRT format subtitles manually (without pysrt)"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                # Process sentences into subtitles
                current_time = 0
                subtitle_index = 1

                for sentence in sentences:
                    if not sentence.strip():
                        continue

                    # Split long sentences into chunks
                    words = sentence.split()
                    for i in range(0, len(words), words_per_subtitle):
                        chunk = ' '.join(words[i:i+words_per_subtitle])
                        if not chunk.strip():
                            continue

                        # Estimate duration based on character count
                        char_count = len(chunk)
                        duration = max(1, char_count / chars_per_second)  # At least 1 second per subtitle

                        # Create subtitle
                        start_time = self._format_time(current_time)
                        end_time = self._format_time(current_time + duration)

                        # Write subtitle to file
                        f.write(f"{subtitle_index}\n")
                        f.write(f"{start_time} --> {end_time}\n")
                        f.write(f"{chunk}\n\n")

                        # Update current time and index
                        current_time += duration
                        subtitle_index += 1

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error generating SRT manually: {str(e)}")
            return False

    def _format_time(self, seconds):
        """Format time in SRT format (HH:MM:SS,mmm)"""
        hours = int(seconds / 3600)
        minutes = int((seconds % 3600) / 60)
        seconds = seconds % 60
        milliseconds = int((seconds - int(seconds)) * 1000)

        return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"

    def _generate_vtt(self, sentences, output_file, words_per_subtitle, chars_per_second, style):
        """Generate WebVTT format subtitles"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                # Write VTT header
                f.write("WEBVTT\n\n")

                # Process sentences into subtitles
                current_time = 0

                for sentence in sentences:
                    if not sentence.strip():
                        continue

                    # Split long sentences into chunks
                    words = sentence.split()
                    for i in range(0, len(words), words_per_subtitle):
                        chunk = ' '.join(words[i:i+words_per_subtitle])
                        if not chunk.strip():
                            continue

                        # Estimate duration based on character count
                        char_count = len(chunk)
                        duration = max(1, char_count / chars_per_second)  # At least 1 second per subtitle

                        # Create subtitle
                        start_time = self._format_time_vtt(current_time)
                        end_time = self._format_time_vtt(current_time + duration)

                        # Write subtitle to file
                        f.write(f"{start_time} --> {end_time}\n")
                        f.write(f"{chunk}\n\n")

                        # Update current time
                        current_time += duration

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error generating VTT: {str(e)}")
            return False

    def _format_time_vtt(self, seconds):
        """Format time in WebVTT format (HH:MM:SS.mmm)"""
        hours = int(seconds / 3600)
        minutes = int((seconds % 3600) / 60)
        seconds = seconds % 60
        milliseconds = int((seconds - int(seconds)) * 1000)

        return f"{hours:02d}:{minutes:02d}:{int(seconds):02d}.{milliseconds:03d}"

    def _generate_ass(self, sentences, output_file, words_per_subtitle, chars_per_second, style):
        """Generate ASS format subtitles"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                # Write ASS header
                f.write("[Script Info]\n")
                f.write("Title: Generated Subtitles\n")
                f.write("ScriptType: v4.00+\n")
                f.write("WrapStyle: 0\n")
                f.write("ScaledBorderAndShadow: yes\n")
                f.write("YCbCr Matrix: TV.601\n")
                f.write("PlayResX: 1920\n")
                f.write("PlayResY: 1080\n\n")

                # Write style section
                f.write("[V4+ Styles]\n")
                f.write("Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\n")

                # Default style
                font_name = "Arial"
                font_size = 48
                primary_color = "&H00FFFFFF"  # White
                outline_color = "&H000000FF"  # Black

                # Apply custom style if provided
                if style:
                    if 'font_name' in style:
                        font_name = style['font_name']
                    if 'font_size' in style:
                        font_size = style['font_size']
                    if 'primary_color' in style:
                        primary_color = style['primary_color']
                    if 'outline_color' in style:
                        outline_color = style['outline_color']

                f.write(f"Style: Default,{font_name},{font_size},{primary_color},&H00000000,{outline_color},&H00000000,0,0,0,0,100,100,0,0,1,2,2,2,10,10,10,1\n\n")

                # Write events section
                f.write("[Events]\n")
                f.write("Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n")

                # Process sentences into subtitles
                current_time = 0

                for sentence in sentences:
                    if not sentence.strip():
                        continue

                    # Split long sentences into chunks
                    words = sentence.split()
                    for i in range(0, len(words), words_per_subtitle):
                        chunk = ' '.join(words[i:i+words_per_subtitle])
                        if not chunk.strip():
                            continue

                        # Estimate duration based on character count
                        char_count = len(chunk)
                        duration = max(1, char_count / chars_per_second)  # At least 1 second per subtitle

                        # Create subtitle
                        start_time = self._format_time_ass(current_time)
                        end_time = self._format_time_ass(current_time + duration)

                        # Write subtitle to file
                        f.write(f"Dialogue: 0,{start_time},{end_time},Default,,0,0,0,,{chunk}\n")

                        # Update current time
                        current_time += duration

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error generating ASS: {str(e)}")
            return False

    def _format_time_ass(self, seconds):
        """Format time in ASS format (H:MM:SS.mm)"""
        hours = int(seconds / 3600)
        minutes = int((seconds % 3600) / 60)
        seconds = seconds % 60
        centiseconds = int((seconds - int(seconds)) * 100)

        return f"{hours}:{minutes:02d}:{int(seconds):02d}.{centiseconds:02d}"

    def align_subtitles_with_audio(self, subtitle_file, audio_file, output_file=None):
        """
        Align subtitles with audio using audio duration

        Args:
            subtitle_file (str): Path to the subtitle file
            audio_file (str): Path to the audio file
            output_file (str, optional): Path to save the aligned subtitle file

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.pydub_available:
            print("pydub not available. Install with: pip install pydub")
            return False

        if not os.path.exists(subtitle_file):
            print(f"Subtitle file not found: {subtitle_file}")
            return False

        if not os.path.exists(audio_file):
            print(f"Audio file not found: {audio_file}")
            return False

        # If no output file specified, overwrite the input file
        if not output_file:
            output_file = subtitle_file

        try:
            # Get audio duration
            audio = self.AudioSegment.from_file(audio_file)
            audio_duration = len(audio) / 1000.0  # Convert to seconds

            # Process subtitles based on format
            if subtitle_file.endswith('.srt'):
                return self._align_srt(subtitle_file, output_file, audio_duration)
            elif subtitle_file.endswith('.vtt'):
                return self._align_vtt(subtitle_file, output_file, audio_duration)
            elif subtitle_file.endswith('.ass'):
                return self._align_ass(subtitle_file, output_file, audio_duration)
            else:
                print(f"Unsupported subtitle format: {subtitle_file}")
                return False

        except Exception as e:
            print(f"Error aligning subtitles: {str(e)}")
            return False

    def _align_srt(self, subtitle_file, output_file, audio_duration):
        """Align SRT subtitles with audio duration"""
        if not self.pysrt_available:
            print("pysrt not available. Install with: pip install pysrt")
            return False

        import pysrt

        try:
            # Load subtitles
            subs = pysrt.open(subtitle_file)

            if not subs:
                print("No subtitles found in file")
                return False

            # Get the end time of the last subtitle
            last_sub_end = subs[-1].end.ordinal / 1000.0  # Convert to seconds

            # Calculate scaling factor
            scale_factor = audio_duration / last_sub_end

            # Apply scaling to all subtitles
            for sub in subs:
                # Scale start and end times
                sub.start = pysrt.SubRipTime(milliseconds=int(sub.start.ordinal * scale_factor))
                sub.end = pysrt.SubRipTime(milliseconds=int(sub.end.ordinal * scale_factor))

            # Save aligned subtitles
            subs.save(output_file, encoding='utf-8')

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error aligning SRT: {str(e)}")
            return False

    def _align_vtt(self, subtitle_file, output_file, audio_duration):
        """Align WebVTT subtitles with audio duration"""
        try:
            # Read the VTT file
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parse the VTT content
            lines = content.split('\n')
            header_lines = []
            subtitle_blocks = []
            current_block = []
            in_header = True

            for line in lines:
                if in_header:
                    if line.strip() and not line.startswith('WEBVTT'):
                        in_header = False
                    else:
                        header_lines.append(line)
                        continue

                if not line.strip() and current_block:
                    subtitle_blocks.append(current_block)
                    current_block = []
                else:
                    current_block.append(line)

            if current_block:
                subtitle_blocks.append(current_block)

            # Find the last timestamp
            last_timestamp = None
            for block in reversed(subtitle_blocks):
                for line in block:
                    if '-->' in line:
                        timestamp_parts = line.split('-->')
                        if len(timestamp_parts) == 2:
                            last_timestamp = timestamp_parts[1].strip()
                            break
                if last_timestamp:
                    break

            if not last_timestamp:
                print("No timestamps found in VTT file")
                return False

            # Convert last timestamp to seconds
            last_seconds = self._vtt_timestamp_to_seconds(last_timestamp)

            # Calculate scaling factor
            scale_factor = audio_duration / last_seconds

            # Apply scaling to all timestamps
            for i, block in enumerate(subtitle_blocks):
                for j, line in enumerate(block):
                    if '-->' in line:
                        timestamp_parts = line.split('-->')
                        if len(timestamp_parts) == 2:
                            start_time = self._vtt_timestamp_to_seconds(timestamp_parts[0].strip())
                            end_time = self._vtt_timestamp_to_seconds(timestamp_parts[1].strip())

                            # Scale times
                            new_start = self._seconds_to_vtt_timestamp(start_time * scale_factor)
                            new_end = self._seconds_to_vtt_timestamp(end_time * scale_factor)

                            # Replace line
                            subtitle_blocks[i][j] = f"{new_start} --> {new_end}"

            # Write aligned VTT file
            with open(output_file, 'w', encoding='utf-8') as f:
                # Write header
                for line in header_lines:
                    f.write(f"{line}\n")

                # Write subtitle blocks
                for block in subtitle_blocks:
                    for line in block:
                        f.write(f"{line}\n")
                    f.write("\n")

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error aligning VTT: {str(e)}")
            return False

    def _vtt_timestamp_to_seconds(self, timestamp):
        """Convert WebVTT timestamp to seconds"""
        parts = timestamp.split(':')

        if len(parts) == 3:
            # Format: HH:MM:SS.mmm
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds_parts = parts[2].split('.')
            seconds = int(seconds_parts[0])
            milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0

            return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000
        elif len(parts) == 2:
            # Format: MM:SS.mmm
            minutes = int(parts[0])
            seconds_parts = parts[1].split('.')
            seconds = int(seconds_parts[0])
            milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0

            return minutes * 60 + seconds + milliseconds / 1000

        return 0

    def _seconds_to_vtt_timestamp(self, seconds):
        """Convert seconds to WebVTT timestamp"""
        hours = int(seconds / 3600)
        minutes = int((seconds % 3600) / 60)
        seconds_remainder = seconds % 60
        milliseconds = int((seconds_remainder - int(seconds_remainder)) * 1000)

        return f"{hours:02d}:{minutes:02d}:{int(seconds_remainder):02d}.{milliseconds:03d}"

    def _align_ass(self, subtitle_file, output_file, audio_duration):
        """Align ASS subtitles with audio duration"""
        try:
            # Read the ASS file
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Split into sections
            sections = {}
            current_section = None
            section_content = []

            for line in content.split('\n'):
                if line.startswith('[') and line.endswith(']'):
                    if current_section:
                        sections[current_section] = section_content

                    current_section = line
                    section_content = []
                elif current_section:
                    section_content.append(line)

            if current_section:
                sections[current_section] = section_content

            # Find the last dialogue line
            events_section = sections.get('[Events]', [])
            last_timestamp = None

            for line in reversed(events_section):
                if line.startswith('Dialogue:'):
                    parts = line.split(',')
                    if len(parts) >= 3:
                        last_timestamp = parts[2]
                        break

            if not last_timestamp:
                print("No dialogue lines found in ASS file")
                return False

            # Convert last timestamp to seconds
            last_seconds = self._ass_timestamp_to_seconds(last_timestamp)

            # Calculate scaling factor
            scale_factor = audio_duration / last_seconds

            # Apply scaling to all dialogue lines
            for i, line in enumerate(events_section):
                if line.startswith('Dialogue:'):
                    parts = line.split(',', 10)  # Split only the first 10 commas
                    if len(parts) >= 10:
                        start_time = self._ass_timestamp_to_seconds(parts[1])
                        end_time = self._ass_timestamp_to_seconds(parts[2])

                        # Scale times
                        new_start = self._seconds_to_ass_timestamp(start_time * scale_factor)
                        new_end = self._seconds_to_ass_timestamp(end_time * scale_factor)

                        # Replace timestamps
                        parts[1] = new_start
                        parts[2] = new_end

                        # Reconstruct line
                        events_section[i] = ','.join(parts)

            # Update events section
            sections['[Events]'] = events_section

            # Write aligned ASS file
            with open(output_file, 'w', encoding='utf-8') as f:
                for section, lines in sections.items():
                    f.write(f"{section}\n")
                    for line in lines:
                        f.write(f"{line}\n")
                    f.write("\n")

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error aligning ASS: {str(e)}")
            return False

    def _ass_timestamp_to_seconds(self, timestamp):
        """Convert ASS timestamp to seconds"""
        parts = timestamp.split(':')

        if len(parts) == 3:
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds_parts = parts[2].split('.')
            seconds = int(seconds_parts[0])
            centiseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0

            return hours * 3600 + minutes * 60 + seconds + centiseconds / 100

        return 0

    def _seconds_to_ass_timestamp(self, seconds):
        """Convert seconds to ASS timestamp"""
        hours = int(seconds / 3600)
        minutes = int((seconds % 3600) / 60)
        seconds_remainder = seconds % 60
        centiseconds = int((seconds_remainder - int(seconds_remainder)) * 100)

        return f"{hours}:{minutes:02d}:{int(seconds_remainder):02d}.{centiseconds:02d}"

    def convert_subtitle_format(self, input_file, output_file):
        """
        Convert between subtitle formats

        Args:
            input_file (str): Path to the input subtitle file
            output_file (str): Path to save the converted subtitle file

        Returns:
            bool: True if successful, False otherwise
        """
        # Determine input and output formats from file extensions
        input_ext = os.path.splitext(input_file)[1].lower()
        output_ext = os.path.splitext(output_file)[1].lower()

        if input_ext == output_ext:
            # Just copy the file if formats are the same
            try:
                with open(input_file, 'r', encoding='utf-8') as f_in:
                    content = f_in.read()

                with open(output_file, 'w', encoding='utf-8') as f_out:
                    f_out.write(content)

                return os.path.exists(output_file)
            except Exception as e:
                print(f"Error copying subtitle file: {str(e)}")
                return False

        # Convert between formats
        try:
            if input_ext == '.srt':
                if output_ext == '.vtt':
                    return self._convert_srt_to_vtt(input_file, output_file)
                elif output_ext == '.ass':
                    return self._convert_srt_to_ass(input_file, output_file)
            elif input_ext == '.vtt':
                if output_ext == '.srt':
                    return self._convert_vtt_to_srt(input_file, output_file)
                elif output_ext == '.ass':
                    return self._convert_vtt_to_ass(input_file, output_file)
            elif input_ext == '.ass':
                if output_ext == '.srt':
                    return self._convert_ass_to_srt(input_file, output_file)
                elif output_ext == '.vtt':
                    return self._convert_ass_to_vtt(input_file, output_file)

            print(f"Unsupported conversion: {input_ext} to {output_ext}")
            return False

        except Exception as e:
            print(f"Error converting subtitle format: {str(e)}")
            return False
