import os
import subprocess
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import threading
import moviepy.editor as mp
import json
import re
from datetime import datetime

class YouTubeAudioVideoMerger:
    def __init__(self, root):
        self.root = root
        self.root.title("YouTube Audio + Video Merger")
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        # Set style
        self.style = ttk.Style()
        self.style.configure("TButton", padding=6, relief="flat", background="#ccc")
        self.style.configure("TLabel", padding=6)
        self.style.configure("TEntry", padding=6)

        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create notebook (tabs)
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create tabs
        self.download_tab = ttk.Frame(self.notebook)
        self.merge_tab = ttk.Frame(self.notebook)

        self.notebook.add(self.download_tab, text="Download Audio")
        self.notebook.add(self.merge_tab, text="Merge Audio & Video")

        # Setup download tab
        self.setup_download_tab()

        # Setup merge tab
        self.setup_merge_tab()

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Variables to store paths
        self.downloaded_audio_path = ""

    def setup_download_tab(self):
        # Create notebook for single and batch downloads
        download_notebook = ttk.Notebook(self.download_tab)
        download_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create tabs for single and batch downloads
        single_download_tab = ttk.Frame(download_notebook)
        batch_download_tab = ttk.Frame(download_notebook)

        download_notebook.add(single_download_tab, text="Single Download")
        download_notebook.add(batch_download_tab, text="Batch Download")

        # Setup single download tab
        self.setup_single_download_tab(single_download_tab)

        # Setup batch download tab
        self.setup_batch_download_tab(batch_download_tab)

    def setup_single_download_tab(self, parent):
        # YouTube URL
        url_frame = ttk.Frame(parent)
        url_frame.pack(fill=tk.X, padx=5, pady=5)

        url_label = ttk.Label(url_frame, text="YouTube URL:")
        url_label.pack(side=tk.LEFT, padx=5)

        self.url_var = tk.StringVar()
        url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=50)
        url_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Output filename
        output_frame = ttk.Frame(parent)
        output_frame.pack(fill=tk.X, padx=5, pady=5)

        output_label = ttk.Label(output_frame, text="Output Filename:")
        output_label.pack(side=tk.LEFT, padx=5)

        self.output_var = tk.StringVar()
        output_entry = ttk.Entry(output_frame, textvariable=self.output_var, width=40)
        output_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        output_label2 = ttk.Label(output_frame, text=".mp3")
        output_label2.pack(side=tk.LEFT)

        # Download button
        download_button = ttk.Button(parent, text="Download Audio", command=self.download_audio)
        download_button.pack(padx=5, pady=10)

        # Progress
        progress_frame = ttk.Frame(parent)
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress.pack(fill=tk.X, padx=5, pady=5)

        # Log
        log_frame = ttk.LabelFrame(parent, text="Download Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        scrollbar = ttk.Scrollbar(self.log_text, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

    def setup_batch_download_tab(self, parent):
        # Instructions
        instructions = ttk.Label(parent, text="Enter one YouTube URL per line:")
        instructions.pack(anchor=tk.W, padx=5, pady=5)

        # URLs text area
        urls_frame = ttk.Frame(parent)
        urls_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.batch_urls_text = tk.Text(urls_frame, wrap=tk.WORD, height=10)
        self.batch_urls_text.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

        urls_scrollbar = ttk.Scrollbar(urls_frame, command=self.batch_urls_text.yview)
        urls_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.batch_urls_text.config(yscrollcommand=urls_scrollbar.set)

        # Output directory
        output_dir_frame = ttk.Frame(parent)
        output_dir_frame.pack(fill=tk.X, padx=5, pady=5)

        output_dir_label = ttk.Label(output_dir_frame, text="Output Directory:")
        output_dir_label.pack(side=tk.LEFT, padx=5)

        self.output_dir_var = tk.StringVar()
        output_dir_entry = ttk.Entry(output_dir_frame, textvariable=self.output_dir_var, width=40)
        output_dir_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        output_dir_button = ttk.Button(output_dir_frame, text="Browse", command=self.browse_output_dir)
        output_dir_button.pack(side=tk.LEFT, padx=5)

        # Download button
        batch_download_button = ttk.Button(parent, text="Download All", command=self.batch_download_audio)
        batch_download_button.pack(padx=5, pady=10)

        # Batch progress
        batch_progress_frame = ttk.Frame(parent)
        batch_progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.batch_progress_var = tk.DoubleVar()
        self.batch_progress = ttk.Progressbar(batch_progress_frame, variable=self.batch_progress_var, maximum=100)
        self.batch_progress.pack(fill=tk.X, padx=5, pady=5)

        # Batch status
        self.batch_status_var = tk.StringVar(value="Ready")
        batch_status = ttk.Label(parent, textvariable=self.batch_status_var)
        batch_status.pack(padx=5, pady=5)

    def setup_merge_tab(self):
        # Create a notebook for the merge tab
        merge_notebook = ttk.Notebook(self.merge_tab)
        merge_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create tabs
        basic_tab = ttk.Frame(merge_notebook)
        metadata_tab = ttk.Frame(merge_notebook)

        merge_notebook.add(basic_tab, text="Basic Settings")
        merge_notebook.add(metadata_tab, text="Metadata")

        # Setup basic tab
        self.setup_basic_merge_tab(basic_tab)

        # Setup metadata tab
        self.setup_metadata_tab(metadata_tab)

    def setup_basic_merge_tab(self, parent):
        # Audio file
        audio_frame = ttk.Frame(parent)
        audio_frame.pack(fill=tk.X, padx=5, pady=5)

        audio_label = ttk.Label(audio_frame, text="Audio File:")
        audio_label.pack(side=tk.LEFT, padx=5)

        self.audio_var = tk.StringVar()
        audio_entry = ttk.Entry(audio_frame, textvariable=self.audio_var, width=40)
        audio_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        audio_button = ttk.Button(audio_frame, text="Browse", command=self.browse_audio)
        audio_button.pack(side=tk.LEFT, padx=5)

        use_downloaded_button = ttk.Button(audio_frame, text="Use Downloaded", command=self.use_downloaded_audio)
        use_downloaded_button.pack(side=tk.LEFT, padx=5)

        # Video file
        video_frame = ttk.Frame(parent)
        video_frame.pack(fill=tk.X, padx=5, pady=5)

        video_label = ttk.Label(video_frame, text="Video File:")
        video_label.pack(side=tk.LEFT, padx=5)

        self.video_var = tk.StringVar()
        video_entry = ttk.Entry(video_frame, textvariable=self.video_var, width=40)
        video_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        video_button = ttk.Button(video_frame, text="Browse", command=self.browse_video)
        video_button.pack(side=tk.LEFT, padx=5)

        # Start time
        start_frame = ttk.Frame(parent)
        start_frame.pack(fill=tk.X, padx=5, pady=5)

        start_label = ttk.Label(start_frame, text="Audio Start Time (seconds):")
        start_label.pack(side=tk.LEFT, padx=5)

        self.start_var = tk.StringVar(value="0")
        start_entry = ttk.Entry(start_frame, textvariable=self.start_var, width=10)
        start_entry.pack(side=tk.LEFT, padx=5)

        # Loop video option
        self.loop_var = tk.BooleanVar(value=True)
        loop_check = ttk.Checkbutton(start_frame, text="Loop video until audio ends", variable=self.loop_var)
        loop_check.pack(side=tk.LEFT, padx=20)

        # Output file
        output_frame = ttk.Frame(parent)
        output_frame.pack(fill=tk.X, padx=5, pady=5)

        output_label = ttk.Label(output_frame, text="Output File:")
        output_label.pack(side=tk.LEFT, padx=5)

        self.merge_output_var = tk.StringVar()
        output_entry = ttk.Entry(output_frame, textvariable=self.merge_output_var, width=40)
        output_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        output_button = ttk.Button(output_frame, text="Browse", command=self.browse_output)
        output_button.pack(side=tk.LEFT, padx=5)

        # Buttons frame
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(padx=5, pady=10)

        # Merge button
        merge_button = ttk.Button(buttons_frame, text="Merge Audio & Video", command=self.merge_files)
        merge_button.pack(side=tk.LEFT, padx=5)

        # Upload to YouTube checkbox
        self.upload_var = tk.BooleanVar(value=False)
        upload_check = ttk.Checkbutton(buttons_frame, text="Upload to YouTube after merging", variable=self.upload_var)
        upload_check.pack(side=tk.LEFT, padx=20)

        # Status
        self.merge_status_var = tk.StringVar(value="Ready to merge")
        merge_status = ttk.Label(parent, textvariable=self.merge_status_var)
        merge_status.pack(padx=5, pady=5)

    def setup_metadata_tab(self, parent):
        # Title
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, padx=5, pady=5)

        title_label = ttk.Label(title_frame, text="Title:")
        title_label.pack(side=tk.LEFT, padx=5)

        self.title_var = tk.StringVar()
        title_entry = ttk.Entry(title_frame, textvariable=self.title_var, width=40)
        title_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Artist
        artist_frame = ttk.Frame(parent)
        artist_frame.pack(fill=tk.X, padx=5, pady=5)

        artist_label = ttk.Label(artist_frame, text="Artist:")
        artist_label.pack(side=tk.LEFT, padx=5)

        self.artist_var = tk.StringVar()
        artist_entry = ttk.Entry(artist_frame, textvariable=self.artist_var, width=40)
        artist_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Album
        album_frame = ttk.Frame(parent)
        album_frame.pack(fill=tk.X, padx=5, pady=5)

        album_label = ttk.Label(album_frame, text="Album:")
        album_label.pack(side=tk.LEFT, padx=5)

        self.album_var = tk.StringVar()
        album_entry = ttk.Entry(album_frame, textvariable=self.album_var, width=40)
        album_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Year
        year_frame = ttk.Frame(parent)
        year_frame.pack(fill=tk.X, padx=5, pady=5)

        year_label = ttk.Label(year_frame, text="Year:")
        year_label.pack(side=tk.LEFT, padx=5)

        self.year_var = tk.StringVar()
        year_entry = ttk.Entry(year_frame, textvariable=self.year_var, width=10)
        year_entry.pack(side=tk.LEFT, padx=5)

        # Genre
        genre_frame = ttk.Frame(parent)
        genre_frame.pack(fill=tk.X, padx=5, pady=5)

        genre_label = ttk.Label(genre_frame, text="Genre:")
        genre_label.pack(side=tk.LEFT, padx=5)

        self.genre_var = tk.StringVar()
        genre_entry = ttk.Entry(genre_frame, textvariable=self.genre_var, width=20)
        genre_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Comment
        comment_frame = ttk.Frame(parent)
        comment_frame.pack(fill=tk.X, padx=5, pady=5)

        comment_label = ttk.Label(comment_frame, text="Comment:")
        comment_label.pack(side=tk.LEFT, padx=5)

        self.comment_var = tk.StringVar()
        comment_entry = ttk.Entry(comment_frame, textvariable=self.comment_var, width=40)
        comment_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Auto-fill from YouTube
        autofill_frame = ttk.Frame(parent)
        autofill_frame.pack(fill=tk.X, padx=5, pady=10)

        autofill_button = ttk.Button(autofill_frame, text="Auto-fill from YouTube URL", command=self.autofill_metadata)
        autofill_button.pack(side=tk.LEFT, padx=5)

        self.youtube_url_var = tk.StringVar()
        youtube_url_entry = ttk.Entry(autofill_frame, textvariable=self.youtube_url_var, width=40)
        youtube_url_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Help text
        help_text = ttk.Label(parent, text="Note: Metadata will be applied to the output file when merging.")
        help_text.pack(padx=5, pady=10)

    def download_audio(self):
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter a YouTube URL")
            return

        output_name = self.output_var.get().strip()
        if not output_name:
            output_name = "output"

        # Ensure .mp3 extension
        if not output_name.endswith('.mp3'):
            output_name += '.mp3'

        self.log_text.delete(1.0, tk.END)
        self.log_text.insert(tk.END, f"Downloading audio from: {url}\n")
        self.log_text.insert(tk.END, f"Output will be saved as: {output_name}\n")
        self.log_text.see(tk.END)

        self.status_var.set("Downloading...")
        self.progress_var.set(0)

        # Start download in a separate thread
        threading.Thread(target=self._download_audio_thread, args=(url, output_name), daemon=True).start()

    def _download_audio_thread(self, url, output_name):
        try:
            # Build the yt-dlp command
            command = [
                "yt-dlp",
                "--no-check-certificate",
                "--no-playlist",
                "--ignore-errors",
                "--format", "bestaudio",
                "--output", output_name,
                url
            ]

            # Run the command
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Update log with output
            for line in process.stdout:
                self.log_text.insert(tk.END, line)
                self.log_text.see(tk.END)

                # Try to update progress bar based on output
                if "%" in line:
                    try:
                        percent = float(line.split("%")[0].split()[-1])
                        self.progress_var.set(percent)
                    except:
                        pass

            # Wait for process to complete
            process.wait()

            # Check if download was successful
            if process.returncode == 0:
                self.status_var.set("Download completed!")
                self.progress_var.set(100)
                self.log_text.insert(tk.END, f"\nDownload completed successfully!\n")

                # Store the path for later use
                self.downloaded_audio_path = os.path.abspath(output_name)
                self.log_text.insert(tk.END, f"Audio saved to: {self.downloaded_audio_path}\n")

                # Suggest next steps
                self.log_text.insert(tk.END, "\nYou can now go to the 'Merge Audio & Video' tab to combine this audio with a video.\n")
            else:
                self.status_var.set("Download failed")
                self.log_text.insert(tk.END, "\nDownload failed. Error details:\n")
                for line in process.stderr:
                    self.log_text.insert(tk.END, line)

        except Exception as e:
            self.status_var.set("Error")
            self.log_text.insert(tk.END, f"\nAn error occurred: {str(e)}\n")

    def browse_audio(self):
        file_path = filedialog.askopenfilename(filetypes=[("Audio Files", "*.mp3;*.wav;*.m4a;*.webm")])
        if file_path:
            self.audio_var.set(file_path)

    def browse_video(self):
        file_path = filedialog.askopenfilename(filetypes=[("Video Files", "*.mp4;*.avi;*.mkv")])
        if file_path:
            self.video_var.set(file_path)

    def browse_output(self):
        file_path = filedialog.asksaveasfilename(defaultextension=".mp4", filetypes=[("MP4 Video", "*.mp4")])
        if file_path:
            self.merge_output_var.set(file_path)

    def browse_output_dir(self):
        dir_path = filedialog.askdirectory()
        if dir_path:
            self.output_dir_var.set(dir_path)

    def use_downloaded_audio(self):
        if self.downloaded_audio_path:
            self.audio_var.set(self.downloaded_audio_path)
        else:
            messagebox.showinfo("Info", "No audio has been downloaded yet. Please download an audio file first.")

    def batch_download_audio(self):
        # Get URLs from text area
        urls_text = self.batch_urls_text.get(1.0, tk.END).strip()
        if not urls_text:
            messagebox.showerror("Error", "Please enter at least one YouTube URL")
            return

        # Split by newlines and filter out empty lines
        urls = [url.strip() for url in urls_text.split('\n') if url.strip()]

        # Get output directory
        output_dir = self.output_dir_var.get().strip()
        if not output_dir:
            messagebox.showerror("Error", "Please select an output directory")
            return

        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
            except Exception as e:
                messagebox.showerror("Error", f"Could not create output directory: {str(e)}")
                return

        # Start batch download in a separate thread
        self.batch_status_var.set(f"Starting batch download of {len(urls)} URLs...")
        self.batch_progress_var.set(0)

        threading.Thread(target=self._batch_download_thread, args=(urls, output_dir), daemon=True).start()

    def _batch_download_thread(self, urls, output_dir):
        total_urls = len(urls)
        successful = 0
        failed = 0

        for i, url in enumerate(urls):
            try:
                # Update status
                self.batch_status_var.set(f"Downloading {i+1}/{total_urls}: {url}")
                self.batch_progress_var.set((i / total_urls) * 100)

                # Build the yt-dlp command
                command = [
                    "yt-dlp",
                    "--no-check-certificate",
                    "--no-playlist",
                    "--ignore-errors",
                    "--format", "bestaudio",
                    "--extract-audio",
                    "--audio-format", "mp3",
                    "--output", os.path.join(output_dir, "%(title)s.%(ext)s"),
                    url
                ]

                # Run the command
                result = subprocess.run(command, capture_output=True, text=True)

                if result.returncode == 0:
                    successful += 1
                else:
                    failed += 1
                    self.batch_status_var.set(f"Error downloading {url}: {result.stderr[:100]}...")

            except Exception as e:
                failed += 1
                self.batch_status_var.set(f"Error downloading {url}: {str(e)}")

        # Update final status
        self.batch_progress_var.set(100)
        self.batch_status_var.set(f"Batch download complete. Success: {successful}, Failed: {failed}")

        # Show completion message
        messagebox.showinfo("Batch Download Complete",
                           f"Downloaded {successful} of {total_urls} files.\n"
                           f"Failed: {failed}\n\n"
                           f"Files saved to: {output_dir}")

    def autofill_metadata(self):
        url = self.youtube_url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter a YouTube URL")
            return

        try:
            # Use yt-dlp to get video info in JSON format
            command = [
                "yt-dlp",
                "--no-check-certificate",
                "--skip-download",
                "--print", "%(title)s",
                "--print", "%(uploader)s",
                "--print", "%(upload_date)s",
                url
            ]

            result = subprocess.run(command, capture_output=True, text=True)

            if result.returncode == 0:
                # Parse the output (title, uploader, upload_date)
                lines = result.stdout.strip().split('\n')

                if len(lines) >= 3:
                    title = lines[0]
                    artist = lines[1]
                    upload_date = lines[2]  # Format: YYYYMMDD

                    # Convert upload_date to year
                    year = upload_date[:4] if len(upload_date) >= 4 else ""

                    # Set the metadata fields
                    self.title_var.set(title)
                    self.artist_var.set(artist)
                    self.year_var.set(year)

                    messagebox.showinfo("Success", "Metadata retrieved successfully!")
                else:
                    messagebox.showerror("Error", "Could not parse video information")
            else:
                messagebox.showerror("Error", f"Failed to retrieve video information: {result.stderr}")

        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {str(e)}")

    def merge_files(self):
        audio_file = self.audio_var.get().strip()
        video_file = self.video_var.get().strip()
        output_file = self.merge_output_var.get().strip()
        loop_video = self.loop_var.get()
        upload_to_youtube = self.upload_var.get()

        # Get metadata
        metadata = {
            "title": self.title_var.get().strip(),
            "artist": self.artist_var.get().strip(),
            "album": self.album_var.get().strip(),
            "year": self.year_var.get().strip(),
            "genre": self.genre_var.get().strip(),
            "comment": self.comment_var.get().strip()
        }

        try:
            start_time = float(self.start_var.get().strip())
        except ValueError:
            messagebox.showerror("Error", "Start time must be a number")
            return

        if not audio_file or not os.path.exists(audio_file):
            messagebox.showerror("Error", "Please select a valid audio file")
            return

        if not video_file or not os.path.exists(video_file):
            messagebox.showerror("Error", "Please select a valid video file")
            return

        if not output_file:
            messagebox.showerror("Error", "Please specify an output file")
            return

        # If uploading to YouTube, make sure we have a title
        if upload_to_youtube and not metadata["title"]:
            # Ask for title if not provided
            title = simpledialog.askstring("YouTube Upload", "Please enter a title for the YouTube video:")
            if not title:
                messagebox.showerror("Error", "A title is required for YouTube upload")
                return
            metadata["title"] = title
            self.title_var.set(title)

        self.merge_status_var.set("Merging files...")
        self.status_var.set("Merging audio and video...")

        # Start merge in a separate thread
        threading.Thread(target=self._merge_files_thread,
                        args=(audio_file, video_file, output_file, start_time, loop_video, metadata, upload_to_youtube),
                        daemon=True).start()

    def _merge_files_thread(self, audio_file, video_file, output_file, start_time, loop_video=True, metadata=None, upload_to_youtube=False):
        try:
            # Update status
            self.merge_status_var.set("Loading files...")

            # Load the video
            video = mp.VideoFileClip(video_file)

            # Load the audio
            audio = mp.AudioFileClip(audio_file)

            # Calculate durations
            video_duration = video.duration
            audio_duration = audio.duration + start_time  # Total duration needed

            self.merge_status_var.set(f"Video duration: {video_duration:.2f}s, Audio duration: {audio_duration:.2f}s")

            # Check if we need to loop the video and if looping is enabled
            if loop_video and audio_duration > video_duration:
                self.merge_status_var.set("Looping video to match audio duration...")

                # Calculate how many times we need to loop the video
                loop_count = int(audio_duration / video_duration) + 1

                # Create a list of video clips to concatenate
                video_clips = []
                for i in range(loop_count):
                    video_clips.append(video)

                # Concatenate the video clips
                final_video = mp.concatenate_videoclips(video_clips)

                # Trim the final video to match the audio duration
                final_video = final_video.subclip(0, audio_duration)

                # Set the audio with the specified start time
                final_video = final_video.set_audio(audio.set_start(start_time))
            else:
                if audio_duration > video_duration and not loop_video:
                    self.merge_status_var.set("Warning: Audio is longer than video but looping is disabled.")
                    # Trim the audio to match the video duration if needed
                    if start_time < video_duration:
                        max_audio_duration = video_duration - start_time
                        if audio.duration > max_audio_duration:
                            audio = audio.subclip(0, max_audio_duration)
                            self.merge_status_var.set(f"Audio trimmed to fit video duration (new duration: {max_audio_duration:.2f}s)")

                # If video is longer than audio or looping is disabled, just use the video as is
                self.merge_status_var.set("Using video without looping...")
                final_video = video.set_audio(audio.set_start(start_time))

                # Trim the final video if needed
                if audio_duration < video_duration:
                    final_video = final_video.subclip(0, audio_duration)

            # Update status
            self.merge_status_var.set("Writing output file... This may take a while.")

            # Prepare ffmpeg arguments for metadata
            ffmpeg_args = []
            if metadata:
                self.merge_status_var.set("Adding metadata to output file...")

                # Add metadata using ffmpeg arguments
                for key, value in metadata.items():
                    if value:  # Only add non-empty metadata
                        ffmpeg_args.extend(["-metadata", f"{key}={value}"])

            # Write the result to a file with metadata
            if ffmpeg_args:
                final_video.write_videofile(
                    output_file,
                    ffmpeg_params=ffmpeg_args
                )
            else:
                final_video.write_videofile(output_file)

            self.merge_status_var.set("Merge completed successfully!")
            self.status_var.set("Merge completed!")

            # Prepare metadata summary for message
            metadata_summary = ""
            if metadata:
                for key, value in metadata.items():
                    if value:
                        metadata_summary += f"{key.capitalize()}: {value}\n"

            # Upload to YouTube if requested
            if upload_to_youtube:
                self.upload_to_youtube(output_file, metadata)
            else:
                if metadata_summary:
                    messagebox.showinfo("Success",
                                       f"Files merged successfully!\n\n"
                                       f"Output saved to: {output_file}\n\n"
                                       f"Metadata added:\n{metadata_summary}")
                else:
                    messagebox.showinfo("Success", f"Files merged successfully!\nOutput saved to: {output_file}")

        except Exception as e:
            self.merge_status_var.set("Merge failed")
            self.status_var.set("Merge failed")
            messagebox.showerror("Error", f"An error occurred during merging:\n{str(e)}")

    def upload_to_youtube(self, video_file, metadata):
        try:
            self.merge_status_var.set("Preparing to upload to YouTube...")

            # Get video title and description
            title = metadata.get("title", os.path.basename(video_file))
            description = f"Created with YouTube Audio + Video Merger\n\n"

            if metadata.get("artist"):
                description += f"Artist: {metadata['artist']}\n"
            if metadata.get("album"):
                description += f"Album: {metadata['album']}\n"
            if metadata.get("year"):
                description += f"Year: {metadata['year']}\n"
            if metadata.get("comment"):
                description += f"\n{metadata['comment']}"

            # Ask for privacy status
            privacy_options = ["public", "unlisted", "private"]
            privacy = simpledialog.askstring(
                "YouTube Upload",
                "Choose privacy setting (public, unlisted, private):",
                initialvalue="unlisted"
            )

            if not privacy or privacy.lower() not in privacy_options:
                privacy = "unlisted"  # Default to unlisted if invalid

            # Ask for category
            category = simpledialog.askstring(
                "YouTube Upload",
                "Enter YouTube category ID (optional):",
                initialvalue="10"  # Default to Music
            )

            category_arg = ["--category", category] if category else []

            # Ask for tags
            tags = simpledialog.askstring(
                "YouTube Upload",
                "Enter tags (comma separated):",
                initialvalue=""
            )

            tags_arg = ["--tags", tags] if tags else []

            # Build the upload command using yt-dlp
            self.merge_status_var.set("Uploading to YouTube... This may take a while.")

            # Create a temporary file for the description
            desc_file = os.path.join(os.path.dirname(video_file), "description.txt")
            with open(desc_file, "w", encoding="utf-8") as f:
                f.write(description)

            # Build the command
            command = [
                "yt-dlp",
                "--verbose",
                "--username", "USERNAME",  # This will prompt for username
                "--password", "PASSWORD",  # This will prompt for password
                "--title", title,
                "--description-file", desc_file,
                "--privacy", privacy.lower(),
            ]

            # Add optional arguments
            command.extend(category_arg)
            command.extend(tags_arg)

            # Add the video file
            command.append(video_file)

            # Show a message to the user
            messagebox.showinfo(
                "YouTube Upload",
                "The video will now be uploaded to YouTube.\n\n"
                "You will be prompted to log in to your YouTube account.\n\n"
                "This process may take some time depending on your internet connection and video size."
            )

            # Run the command in a new window
            upload_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )

            # Clean up the description file
            if os.path.exists(desc_file):
                os.remove(desc_file)

            self.merge_status_var.set("YouTube upload initiated. Check the console window for progress.")

        except Exception as e:
            self.merge_status_var.set("YouTube upload failed")
            messagebox.showerror("Upload Error", f"An error occurred during YouTube upload:\n{str(e)}")

            # Still show the success message for the merge
            messagebox.showinfo("Merge Success", f"Files merged successfully!\nOutput saved to: {video_file}")

if __name__ == "__main__":
    root = tk.Tk()
    app = YouTubeAudioVideoMerger(root)
    root.mainloop()
