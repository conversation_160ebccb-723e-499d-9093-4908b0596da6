# Boring History Generator - Tạo Video Lịch Sử Tự Động

🎬 **Tool tự động tạo video "Boring History for Sleep" hoàn chỉnh bằng AI**

## 🌟 Tính Năng Chính

- ✅ **Tạo tiêu đề video** theo format "Boring History for Sleep"
- ✅ **Tạo kịch bản 15,000-16,500 từ** với 15 phần theo chuẩn YouTube
- ✅ **Tạo giọng đọc** bằng Minimax TTS API
- ✅ **Tạo hình ảnh minh họa** bằng Leonardo AI API  
- ✅ **Tạo project CapCut** tự động với timeline đã setup
- ✅ **Giao diện tiếng Việt** thân thiện với người dùng
- ✅ **Tùy chỉnh templates** linh hoạt cho prompts AI

## 🔧 Yêu Cầu Hệ Thống

### API Keys Cần Thiết:
1. **🤖 OpenAI API Key** - Tạo tiêu đề, kị<PERSON> bả<PERSON>, prompts hình ảnh
2. **🎤 Minimax API Key** - Tạo giọng đọc (Text-to-Speech)
3. **🎨 Leonardo AI API Key** - T<PERSON><PERSON> hình ảnh minh họa

### Phần Mềm:
- **Python 3.8+** 
- **CapCut PC** (để chỉnh sửa video cuối cùng)

## 📥 Cài Đặt

### Bước 1: Tải về và cài đặt
```bash
# Clone repository
git clone <repository-url>
cd mergeaudiowithvideo

# Cài đặt dependencies
pip install -r requirements.txt
```

### Bước 2: Chạy ứng dụng
```bash
# Cách 1: Chạy trực tiếp
python boring_history_generator.py

# Cách 2: Sử dụng file batch (Windows)
run_boring_history_generator.bat
```

## 🚀 Hướng Dẫn Sử Dụng

### 1️⃣ Cấu Hình API Keys

1. Mở ứng dụng và chuyển đến tab **"⚙️ Cài Đặt"**
2. Nhập các API keys:
   - **OpenAI API Key**: Lấy từ [platform.openai.com](https://platform.openai.com)
   - **Minimax API Key**: Lấy từ [api.minimax.chat](https://api.minimax.chat)
   - **Leonardo AI API Key**: Lấy từ [leonardo.ai](https://leonardo.ai)

3. Cấu hình thư mục và file:
   - **Thư Mục Xuất File**: Chọn nơi lưu tất cả file được tạo
   - **Video Nền** (tùy chọn): Chọn video chạy nền
   - **Voice ID**: ID giọng đọc Minimax (mặc định: `male-qn-qingse`)
   - **Số Lượng Hình Ảnh**: 5-20 ảnh minh họa

4. Click **"💾 Lưu Cài Đặt"**

### 2️⃣ Tùy Chỉnh Templates (Tùy Chọn)

1. Click **"🎨 Tùy Chỉnh Templates"** trong tab Cài Đặt
2. Điều chỉnh các thông số:
   - **Title Settings**: Số lượng tiêu đề, độ dài hook, tone
   - **Script Settings**: Số từ, số phần, style kể chuyện
   - **Image Settings**: Style nghệ thuật, màu sắc, tỷ lệ
   - **Thumbnail Settings**: Style thumbnail, background

3. Xem preview và lưu cấu hình

### 3️⃣ Tạo Nội Dung

1. Chuyển về tab **"🏠 Trang Chính"**
2. Nhập chủ đề vào ô **"Nhập chủ đề cho video của bạn"**
   
   💡 **Ví dụ chủ đề hay:**
   - "Những Người Hề Cung Đình Thời Trung Cổ"
   - "Kỹ Thuật Xây Dựng La Mã Cổ Đại"
   - "Cuộc Sống Hàng Ngày Của Samurai Nhật Bản"

3. Chọn chức năng:
   - **1️⃣ Tạo Tiêu Đề**: Chỉ tạo tiêu đề video
   - **2️⃣ Tạo Kịch Bản**: Tạo outline kịch bản 15 phần
   - **3️⃣ Tạo Tất Cả**: Tạo video hoàn chỉnh (khuyến nghị)

### 4️⃣ Quy Trình Tự Động (Tạo Tất Cả)

Khi chọn **"3️⃣ Tạo Tất Cả"**, tool sẽ thực hiện 7 bước:

1. **🔄 Bước 1/7**: Tạo tiêu đề theo format "Boring History for Sleep"
2. **🔄 Bước 2/7**: Tạo kịch bản đầy đủ 15 phần (~15,000 từ)
3. **🔄 Bước 3/7**: Tạo cấu trúc thư mục dự án
4. **🔄 Bước 4/7**: Tạo prompts cho hình ảnh minh họa
5. **🔄 Bước 5/7**: Tạo giọng đọc từ kịch bản
6. **🔄 Bước 6/7**: Tạo hình ảnh minh họa
7. **🔄 Bước 7/7**: Tạo project CapCut với timeline setup

⏱️ **Thời gian**: 30-60 phút tùy độ dài nội dung

### 5️⃣ Chỉnh Sửa Video Trong CapCut

1. Mở **CapCut PC**
2. Import file `.cep` từ thư mục `capcut/`
3. Timeline đã được setup với:
   - **Audio track**: Giọng đọc narration
   - **Video track 1**: Video nền (nếu có)
   - **Video track 2**: Hình ảnh minh họa với timing tự động
4. Chỉnh sửa theo ý muốn và xuất video

## 📁 Cấu Trúc File Đầu Ra

```
Thư_Mục_Xuất/
├── Tên_Dự_Án/
│   ├── 📁 audio/
│   │   └── narration.mp3          # Giọng đọc
│   ├── 📁 images/
│   │   ├── scene_001_1.jpg        # Hình ảnh minh họa
│   │   ├── scene_002_1.jpg
│   │   └── ...
│   ├── 📁 video/                  # Thư mục cho video xuất
│   ├── 📁 capcut/
│   │   └── Tên_Dự_Án.cep         # Project CapCut
│   ├── 📁 scripts/
│   │   └── full_script.txt        # Kịch bản đầy đủ
│   └── 📁 prompts/
│       └── image_prompts.txt      # Prompts hình ảnh
```

## 🎨 Templates Có Thể Tùy Chỉnh

### 📝 Title Generation
- **Số lượng tiêu đề**: 1-10 tiêu đề
- **Độ dài hook**: 5-30 từ
- **Tone**: Từ hài hước đến nghiêm túc
- **Vùng địa lý**: Tập trung vào khu vực cụ thể
- **Prefixes tùy chỉnh**: Thay đổi format tiêu đề

### 📜 Script Generation  
- **Tổng số từ**: 5,000-30,000 từ
- **Số phần**: 5-25 phần
- **Từ mỗi phần**: 500-2,000 từ
- **Style kể chuyện**: Từ ASMR đến học thuật
- **Intro/Outro**: Bật/tắt template intro

### 🎨 Image Generation
- **Style nghệ thuật**: Medieval, Renaissance, Victorian, v.v.
- **Medium**: Tempera, oil, watercolor, digital
- **Perspective**: Medieval flat, one-point, bird's eye
- **Tỷ lệ**: 16:9, 4:3, 1:1, v.v.
- **Palette màu**: Tùy chỉnh màu sắc chủ đạo

## 🔍 Theo Dõi Tiến Trình

- **📊 Tiến Trình**: Hiển thị bước hiện tại và trạng thái
- **📋 Nhật Ký**: Theo dõi chi tiết quá trình tạo nội dung
- **🔄 Progress Bar**: Hiển thị hoạt động đang diễn ra

## ⚠️ Xử Lý Lỗi Thường Gặp

### 🔑 Lỗi API
- **Kiểm tra API keys** đã nhập đúng chưa
- **Kiểm tra balance/credits** của các dịch vụ API
- **Kiểm tra kết nối mạng**

### 📁 Lỗi File
- **Đảm bảo thư mục xuất** có quyền ghi
- **Kiểm tra dung lượng ổ cứng** đủ cho file ảnh và audio
- **Đóng các ứng dụng** đang sử dụng file

### 🤖 Lỗi Generation
- **Xem tab Nhật Ký** để biết chi tiết lỗi
- **Thử giảm số lượng ảnh** nếu Leonardo AI bị rate limit
- **Thử chủ đề ngắn gọn hơn** nếu script generation thất bại

## ⏱️ Thời Gian Ước Tính

- **Tạo tiêu đề**: 30 giây - 1 phút
- **Tạo kịch bản outline**: 1-2 phút  
- **Tạo kịch bản đầy đủ**: 15-30 phút (15 phần)
- **Tạo giọng đọc**: 5-15 phút
- **Tạo hình ảnh**: 10-20 phút (tùy số lượng)
- **Tổng thời gian**: 30-60 phút

## 📚 API Rate Limits

- **OpenAI**: Tuân theo rate limits của plan đã đăng ký
- **Minimax**: Có delay giữa các requests để tránh limit
- **Leonardo AI**: Delay 2 giây giữa các lần tạo ảnh

## 💡 Tips Sử Dụng Hiệu Quả

1. **Chọn chủ đề cụ thể**: "Kỹ thuật xây dựng La Mã" thay vì "Lịch sử La Mã"
2. **Kiểm tra API credits** trước khi bắt đầu
3. **Backup cài đặt**: Lưu file `boring_history_settings.json`
4. **Test với ít ảnh**: Thử 5 ảnh trước khi tạo 20 ảnh
5. **Sử dụng SSD**: Tăng tốc độ xử lý file

## 🆘 Hỗ Trợ

Nếu gặp vấn đề:
1. **Kiểm tra tab Nhật Ký** trong ứng dụng
2. **Xem file README gốc** để biết thêm chi tiết kỹ thuật
3. **Kiểm tra API documentation** của các dịch vụ
4. **Đảm bảo kết nối mạng** ổn định

## 🎉 Kết Quả Mong Đợi

Sau khi hoàn thành, bạn sẽ có:
- ✅ **Video title** chuẩn "Boring History for Sleep"
- ✅ **Kịch bản 15,000+ từ** chất lượng cao
- ✅ **File audio MP3** giọng đọc tự nhiên
- ✅ **10-20 hình ảnh** minh họa đẹp mắt
- ✅ **CapCut project** sẵn sàng chỉnh sửa
- ✅ **Cấu trúc file** có tổ chức

**🎬 Chỉ cần import vào CapCut và xuất video thôi!**
