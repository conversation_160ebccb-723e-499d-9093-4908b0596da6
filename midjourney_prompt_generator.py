"""
Midjourney Prompt Generator

This module provides functionality to analyze scripts using ChatGPT,
generate Midjourney prompts, and interact with the Midjourney API.

Features:
- Script analysis with ChatGPT
- Midjourney prompt generation
- Prompt editing and management
- Midjourney API integration
- Image saving and organization
"""

import os
import json
import requests
import time
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

class MidjourneyPromptGenerator:
    """Main class for generating and managing Midjourney prompts"""

    def __init__(self, openai_client=None, openai_api_key=None, openai_base_url=None,
                 midjourney_api_key=None, midjourney_base_url=None):
        """Initialize the Midjourney Prompt Generator

        Args:
            openai_client: Initialized OpenAI client (optional)
            openai_api_key: OpenAI API key (optional)
            openai_base_url: OpenAI base URL (optional)
            midjourney_api_key: Midjourney API key (optional)
            midjourney_base_url: Midjourney API base URL (optional)
        """
        self.openai_client = openai_client
        self.openai_api_key = openai_api_key
        self.openai_base_url = openai_base_url
        self.midjourney_api_key = midjourney_api_key
        self.midjourney_base_url = midjourney_base_url

        # Initialize cache for generated prompts
        self.prompt_cache = {}

    def analyze_script(self, script: str, model: str = "gpt-4o") -> List[Dict[str, Any]]:
        """Analyze a script using ChatGPT to extract key concepts and themes

        Args:
            script: The script text to analyze
            model: The ChatGPT model to use

        Returns:
            A list of dictionaries containing analysis results
        """
        if not self.openai_client:
            raise ValueError("OpenAI client not initialized")

        system_message = """
        You are an expert at analyzing scripts and extracting key visual concepts.
        Your task is to analyze a script and identify the main themes, concepts, and visual elements.
        For each segment of the script, identify:
        1. The main subject or topic
        2. Key visual elements that represent the content
        3. The emotional tone or mood
        4. Any specific imagery mentioned or implied
        """

        user_message = f"""
        Analyze this script and extract key visual concepts for creating Midjourney prompts:

        SCRIPT:
        "{script.strip()}"

        Return a JSON array where each segment is an object with these properties:
        - "segment_text": The text segment being analyzed
        - "main_subject": The main subject or topic of this segment
        - "visual_elements": Key visual elements that represent the content (3-5 items)
        - "mood": The emotional tone or mood of this segment
        - "imagery": Specific imagery mentioned or implied

        Make sure to divide the script into logical segments based on topic changes or natural breaks.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.7,
                max_tokens=1500,
                response_format={"type": "json_object"}
            )

            # Extract and parse the JSON response
            json_response = response.choices[0].message.content.strip()

            # Clean up the response if it contains markdown formatting
            if json_response.startswith("```") and ("json" in json_response.split("\n")[0].lower()):
                # Extract the JSON part from the markdown code block
                lines = json_response.split("\n")
                json_response = "\n".join(lines[1:-1])  # Remove the first and last lines (```json and ```)

            # Parse the JSON
            analysis_results = json.loads(json_response)

            # Check if the response has the expected structure
            if "segments" in analysis_results:
                return analysis_results["segments"]
            else:
                return analysis_results

        except Exception as e:
            print(f"Error analyzing script: {str(e)}")
            return []

    def generate_midjourney_prompts(self, analysis_results: List[Dict[str, Any]],
                                   style_preset: str = "minimalist") -> List[Dict[str, Any]]:
        """Generate Midjourney prompts based on script analysis

        Args:
            analysis_results: The results from script analysis
            style_preset: Style preset to use for the prompts

        Returns:
            A list of dictionaries containing generated prompts
        """
        if not self.openai_client:
            raise ValueError("OpenAI client not initialized")

        # Define style presets
        style_presets = {
            "minimalist": "minimalist, clean lines, simple shapes, black and white, high contrast",
            "sketch": "hand-drawn sketch, pencil lines, rough texture, artistic, expressive",
            "watercolor": "watercolor painting, soft edges, gentle colors, artistic, flowing",
            "3d_render": "3D render, clean surfaces, subtle lighting, professional, modern",
            "comic": "comic book style, bold outlines, flat colors, dynamic, expressive",
            "photorealistic": "photorealistic, detailed, high resolution, natural lighting, lifelike"
        }

        style_description = style_presets.get(style_preset, style_presets["minimalist"])

        system_message = """
        You are an expert at creating Midjourney prompts based on script analysis.
        Your task is to create detailed, effective prompts that will generate images
        matching the concepts and themes identified in the script analysis.

        A good Midjourney prompt should:
        1. Be clear and specific about the main subject
        2. Include important visual elements and composition
        3. Specify style, mood, and artistic direction
        4. Use appropriate technical parameters (like --ar 16:9)
        """

        prompts = []

        for segment in analysis_results:
            user_message = f"""
            Create a Midjourney prompt based on this script analysis:

            SEGMENT TEXT: "{segment.get('segment_text', '')}"
            MAIN SUBJECT: {segment.get('main_subject', '')}
            VISUAL ELEMENTS: {segment.get('visual_elements', [])}
            MOOD: {segment.get('mood', '')}
            IMAGERY: {segment.get('imagery', '')}

            STYLE DIRECTION: {style_description}

            Return a JSON object with:
            - "segment_text": The original segment text
            - "prompt": The complete Midjourney prompt
            - "description": A brief description of what this image should show

            The prompt should be detailed and specific, formatted for optimal results with Midjourney.
            """

            try:
                response = self.openai_client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": user_message}
                    ],
                    temperature=0.7,
                    max_tokens=500,
                    response_format={"type": "json_object"}
                )

                # Extract and parse the JSON response
                json_response = response.choices[0].message.content.strip()

                # Clean up the response if it contains markdown formatting
                if json_response.startswith("```") and ("json" in json_response.split("\n")[0].lower()):
                    # Extract the JSON part from the markdown code block
                    lines = json_response.split("\n")
                    json_response = "\n".join(lines[1:-1])  # Remove the first and last lines

                # Parse the JSON
                prompt_data = json.loads(json_response)

                # Add to prompts list
                prompts.append(prompt_data)

            except Exception as e:
                print(f"Error generating prompt: {str(e)}")
                # Add a placeholder for failed prompts
                prompts.append({
                    "segment_text": segment.get("segment_text", ""),
                    "prompt": "Error generating prompt",
                    "description": "Failed to generate prompt for this segment"
                })

        return prompts

    def send_to_midjourney(self, prompt: str) -> Dict[str, Any]:
        """Send a prompt to the Midjourney API (Note: Midjourney doesn't have public API)

        Args:
            prompt: The Midjourney prompt to send

        Returns:
            A dictionary containing the API response
        """
        # Note: Midjourney doesn't have a public API
        # This is a placeholder for potential third-party services
        return {
            "error": "Midjourney API not available",
            "message": "Midjourney doesn't have a public API. Consider using DALL-E 3 instead.",
            "prompt": prompt
        }

    def generate_image_with_dalle(self, prompt: str, size: str = "1792x1024") -> Dict[str, Any]:
        """Generate image using DALL-E 3 instead of Midjourney

        Args:
            prompt: The image prompt
            size: Image size (1024x1024, 1024x1792, or 1792x1024)

        Returns:
            A dictionary containing the API response
        """
        if not self.openai_client:
            raise ValueError("OpenAI client not initialized")

        try:
            response = self.openai_client.images.generate(
                model="dall-e-3",
                prompt=prompt,
                size=size,
                quality="standard",
                n=1,
            )

            image_url = response.data[0].url
            return {
                "success": True,
                "image_url": image_url,
                "prompt": prompt,
                "size": size
            }

        except Exception as e:
            print(f"Error generating image with DALL-E: {str(e)}")
            return {"error": "DALL-E generation failed", "message": str(e)}

    def save_prompt(self, prompt_data: Dict[str, Any], output_dir: str) -> str:
        """Save a prompt to a file

        Args:
            prompt_data: The prompt data to save
            output_dir: Directory to save the prompt file

        Returns:
            The path to the saved file
        """
        os.makedirs(output_dir, exist_ok=True)

        # Create a filename based on the first few words of the segment text
        segment_text = prompt_data.get("segment_text", "unknown_segment")
        words = segment_text.split()[:5]  # Take first 5 words
        filename_base = "_".join(words).lower()

        # Remove special characters
        filename_base = "".join(c if c.isalnum() or c == "_" else "_" for c in filename_base)

        # Add timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_base}_{timestamp}.json"

        # Save to file
        filepath = os.path.join(output_dir, filename)
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(prompt_data, f, indent=4)

        return filepath
