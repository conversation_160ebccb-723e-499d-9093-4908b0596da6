"""
Text-to-Speech Engine for Channel Content Repurposer

This module handles text-to-speech conversion using various providers:
- ElevenLabs for high-quality voices
- Google Text-to-Speech as a fallback
- Azure Text-to-Speech as an alternative
- Local processing options
"""

import os
import json
import time
import tempfile
from enum import Enum
from abc import ABC, abstractmethod


class TTSProvider(Enum):
    """Enum for supported TTS providers"""
    ELEVENLABS = "elevenlabs"
    GOOGLE = "google"
    AZURE = "azure"
    LOCAL = "local"


class TTSEngine:
    """Main TTS Engine that handles different providers"""

    def __init__(self, provider=TTSProvider.ELEVENLABS, api_key=None, voice_id=None):
        """
        Initialize the TTS Engine

        Args:
            provider (TTSProvider): The TTS provider to use
            api_key (str, optional): API key for the selected provider
            voice_id (str, optional): Voice ID for the selected provider
        """
        self.provider = provider
        self.api_key = api_key
        self.voice_id = voice_id
        self.tts_instance = None

        # Initialize the appropriate TTS provider
        self._init_provider()

    def _init_provider(self):
        """Initialize the selected TTS provider"""
        if self.provider == TTSProvider.ELEVENLABS:
            try:
                self.tts_instance = ElevenLabsTTS(self.api_key, self.voice_id)
                return
            except ImportError:
                print("ElevenLabs not available, falling back to Google TTS")
                self.provider = TTSProvider.GOOGLE

        if self.provider == TTSProvider.GOOGLE:
            try:
                self.tts_instance = GoogleTTS()
                return
            except ImportError:
                print("Google TTS not available, falling back to Azure TTS")
                self.provider = TTSProvider.AZURE

        if self.provider == TTSProvider.AZURE:
            try:
                self.tts_instance = AzureTTS(self.api_key, self.voice_id)
                return
            except ImportError:
                print("Azure TTS not available, falling back to local TTS")
                self.provider = TTSProvider.LOCAL

        # Local TTS as last resort
        self.tts_instance = LocalTTS()

    def set_provider(self, provider, api_key=None, voice_id=None):
        """
        Change the TTS provider

        Args:
            provider (TTSProvider): The TTS provider to use
            api_key (str, optional): API key for the selected provider
            voice_id (str, optional): Voice ID for the selected provider

        Returns:
            bool: True if successful, False otherwise
        """
        self.provider = provider

        if api_key:
            self.api_key = api_key

        if voice_id:
            self.voice_id = voice_id

        # Re-initialize the provider
        self._init_provider()

        return self.tts_instance is not None

    def generate_speech(self, text, output_file, voice_id=None, options=None):
        """
        Generate speech from text

        Args:
            text (str): Text to convert to speech
            output_file (str): Path to save the audio file
            voice_id (str, optional): Override the default voice ID
            options (dict, optional): Additional options for the TTS provider

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.tts_instance:
            print("TTS provider not initialized")
            return False

        return self.tts_instance.generate_speech(text, output_file, voice_id, options)

    def get_available_voices(self):
        """
        Get available voices from the current provider

        Returns:
            list: List of available voices or None if failed
        """
        if not self.tts_instance:
            print("TTS provider not initialized")
            return None

        return self.tts_instance.get_available_voices()


class BaseTTS(ABC):
    """Base class for TTS providers"""

    @abstractmethod
    def generate_speech(self, text, output_file, voice_id=None, options=None):
        """Generate speech from text"""
        pass

    @abstractmethod
    def get_available_voices(self):
        """Get available voices"""
        pass


class ElevenLabsTTS(BaseTTS):
    """ElevenLabs TTS provider"""

    def __init__(self, api_key=None, voice_id=None):
        """
        Initialize ElevenLabs TTS

        Args:
            api_key (str, optional): ElevenLabs API key
            voice_id (str, optional): ElevenLabs voice ID
        """
        try:
            from elevenlabs import set_api_key, voices, generate, save
            self.elevenlabs = True
            self.set_api_key = set_api_key
            self.voices_func = voices
            self.generate = generate
            self.save = save

            if api_key:
                self.set_api_key(api_key)

            self.voice_id = voice_id or "21m00Tcm4TlvDq8ikWAM"  # Default voice (Rachel)

            # Voice ID to name mapping for common voices
            self.voice_map = {
                "Rachel": "21m00Tcm4TlvDq8ikWAM",
                "Domi": "AZnzlk1XvdvUeBnXmlld",
                "Bella": "EXAVITQu4vr4xnSDxMaL",
                "Antoni": "ErXwobaYiN019PkySvjV",
                "Elli": "MF3mGyEYCl7XYWbV9V6O",
                "Josh": "TxGEqnHWrfWFTfGW9XjX",
                "Arnold": "VR6AewLTigWG4xSOukaG",
                "Adam": "pNInz6obpgDQGcFmaJgB",
                "Sam": "yoZ06aMxZJJ28mfd3POQ"
            }

        except ImportError:
            raise ImportError("ElevenLabs package not installed. Install with: pip install elevenlabs")

    def generate_speech(self, text, output_file, voice_id=None, options=None):
        """
        Generate speech using ElevenLabs

        Args:
            text (str): Text to convert to speech
            output_file (str): Path to save the audio file
            voice_id (str, optional): Override the default voice ID
            options (dict, optional): Additional options for ElevenLabs

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Use provided voice_id, or instance voice_id, or default
            voice = voice_id or self.voice_id

            # If voice is a name, convert to ID
            if voice in self.voice_map:
                voice = self.voice_map[voice]

            # Set default options
            default_options = {
                "model": "eleven_monolingual_v1",
                "stability": 0.5,
                "similarity_boost": 0.75
            }

            # Update with user options if provided
            if options:
                default_options.update(options)

            # Generate audio
            audio = self.generate(
                text=text,
                voice=voice,
                model=default_options["model"],
                stability=default_options["stability"],
                similarity_boost=default_options["similarity_boost"]
            )

            # Save audio file
            self.save(audio, output_file)

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error generating speech with ElevenLabs: {str(e)}")
            return False

    def get_available_voices(self):
        """
        Get available voices from ElevenLabs

        Returns:
            list: List of voice dictionaries with id and name
        """
        try:
            all_voices = self.voices_func()
            voice_list = []

            for voice in all_voices:
                voice_list.append({
                    "id": voice.voice_id,
                    "name": voice.name
                })

            return voice_list

        except Exception as e:
            print(f"Error getting ElevenLabs voices: {str(e)}")
            return list(self.voice_map.items())


class GoogleTTS(BaseTTS):
    """Google Text-to-Speech provider"""

    def __init__(self):
        """Initialize Google TTS"""
        try:
            from gtts import gTTS
            self.gTTS = gTTS
        except ImportError:
            raise ImportError("gTTS package not installed. Install with: pip install gtts")

    def generate_speech(self, text, output_file, voice_id=None, options=None):
        """
        Generate speech using Google TTS

        Args:
            text (str): Text to convert to speech
            output_file (str): Path to save the audio file
            voice_id (str, optional): Not used for Google TTS
            options (dict, optional): Additional options for Google TTS

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Set default options
            lang = 'en'
            slow = False

            # Update with user options if provided
            if options:
                if 'lang' in options:
                    lang = options['lang']
                if 'slow' in options:
                    slow = options['slow']

            # Generate and save audio
            tts = self.gTTS(text=text, lang=lang, slow=slow)
            tts.save(output_file)

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error generating speech with Google TTS: {str(e)}")
            return False

    def get_available_voices(self):
        """
        Get available voices from Google TTS

        Returns:
            list: List of available languages
        """
        # Google TTS doesn't have voice selection, just languages
        languages = [
            {"id": "en", "name": "English"},
            {"id": "fr", "name": "French"},
            {"id": "es", "name": "Spanish"},
            {"id": "de", "name": "German"},
            {"id": "it", "name": "Italian"},
            {"id": "ja", "name": "Japanese"},
            {"id": "ko", "name": "Korean"},
            {"id": "pt", "name": "Portuguese"},
            {"id": "ru", "name": "Russian"},
            {"id": "zh-CN", "name": "Chinese (Simplified)"}
        ]

        return languages


class AzureTTS(BaseTTS):
    """Azure Text-to-Speech provider"""

    def __init__(self, api_key=None, voice_id=None):
        """
        Initialize Azure TTS

        Args:
            api_key (str, optional): Azure Speech Service key
            voice_id (str, optional): Azure voice name
        """
        try:
            import azure.cognitiveservices.speech as speechsdk
            self.speechsdk = speechsdk

            self.api_key = api_key
            self.region = "eastus"  # Default region
            self.voice_id = voice_id or "en-US-JennyNeural"  # Default voice

            # Common Azure neural voices
            self.voice_map = {
                "Jenny": "en-US-JennyNeural",
                "Guy": "en-US-GuyNeural",
                "Aria": "en-US-AriaNeural",
                "Davis": "en-US-DavisNeural",
                "Jane": "en-GB-SoniaNeural",
                "Ryan": "en-GB-RyanNeural",
                "Clara": "en-CA-ClaraNeural",
                "Liam": "en-CA-LiamNeural"
            }

        except ImportError:
            raise ImportError("Azure Speech SDK not installed. Install with: pip install azure-cognitiveservices-speech")

    def generate_speech(self, text, output_file, voice_id=None, options=None):
        """
        Generate speech using Azure TTS

        Args:
            text (str): Text to convert to speech
            output_file (str): Path to save the audio file
            voice_id (str, optional): Override the default voice ID
            options (dict, optional): Additional options for Azure TTS

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.api_key:
            print("Azure Speech Service key not set")
            return False

        try:
            # Use provided voice_id, or instance voice_id, or default
            voice = voice_id or self.voice_id

            # If voice is a name, convert to ID
            if voice in self.voice_map:
                voice = self.voice_map[voice]

            # Set default options
            region = self.region

            # Update with user options if provided
            if options and 'region' in options:
                region = options['region']

            # Configure speech service
            speech_config = self.speechsdk.SpeechConfig(subscription=self.api_key, region=region)
            speech_config.speech_synthesis_voice_name = voice

            # Configure audio output
            audio_config = self.speechsdk.audio.AudioOutputConfig(filename=output_file)

            # Create speech synthesizer
            synthesizer = self.speechsdk.SpeechSynthesizer(speech_config=speech_config, audio_config=audio_config)

            # Synthesize speech
            result = synthesizer.speak_text_async(text).get()

            # Check result
            if result.reason == self.speechsdk.ResultReason.SynthesizingAudioCompleted:
                return os.path.exists(output_file)
            else:
                print(f"Azure TTS error: {result.reason}")
                return False

        except Exception as e:
            print(f"Error generating speech with Azure TTS: {str(e)}")
            return False

    def get_available_voices(self):
        """
        Get available voices from Azure TTS

        Returns:
            list: List of voice dictionaries with id and name
        """
        if not self.api_key:
            return list(self.voice_map.items())

        try:
            # Configure speech service
            speech_config = self.speechsdk.SpeechConfig(subscription=self.api_key, region=self.region)

            # Create speech synthesizer
            synthesizer = self.speechsdk.SpeechSynthesizer(speech_config=speech_config)

            # Get available voices
            result = synthesizer.get_voices_async().get()

            voice_list = []
            if result.voices:
                for voice in result.voices:
                    voice_list.append({
                        "id": voice.name,
                        "name": voice.display_name,
                        "locale": voice.locale
                    })

            return voice_list

        except Exception as e:
            print(f"Error getting Azure TTS voices: {str(e)}")
            return list(self.voice_map.items())


class LocalTTS(BaseTTS):
    """Local Text-to-Speech provider using pyttsx3"""

    def __init__(self):
        """Initialize Local TTS"""
        try:
            import pyttsx3
            self.pyttsx3 = pyttsx3
            self.engine = pyttsx3.init()

            # Get available voices
            self.available_voices = {}
            voices = self.engine.getProperty('voices')
            for i, voice in enumerate(voices):
                self.available_voices[str(i)] = voice.id

            # Set default voice (usually index 0)
            if self.available_voices:
                self.voice_id = "0"

        except ImportError:
            raise ImportError("pyttsx3 package not installed. Install with: pip install pyttsx3")

    def generate_speech(self, text, output_file, voice_id=None, options=None):
        """
        Generate speech using Local TTS

        Args:
            text (str): Text to convert to speech
            output_file (str): Path to save the audio file
            voice_id (str, optional): Override the default voice ID
            options (dict, optional): Additional options for Local TTS

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Use provided voice_id, or instance voice_id
            voice_index = voice_id or self.voice_id

            # Set voice
            if voice_index in self.available_voices:
                self.engine.setProperty('voice', self.available_voices[voice_index])

            # Set default options
            rate = 150  # Default speech rate
            volume = 1.0  # Default volume

            # Update with user options if provided
            if options:
                if 'rate' in options:
                    rate = options['rate']
                if 'volume' in options:
                    volume = options['volume']

            # Apply options
            self.engine.setProperty('rate', rate)
            self.engine.setProperty('volume', volume)

            # Save to file
            self.engine.save_to_file(text, output_file)
            self.engine.runAndWait()

            return os.path.exists(output_file)

        except Exception as e:
            print(f"Error generating speech with Local TTS: {str(e)}")
            return False

    def get_available_voices(self):
        """
        Get available voices from Local TTS

        Returns:
            list: List of voice dictionaries with id and name
        """
        voice_list = []

        try:
            voices = self.engine.getProperty('voices')
            for i, voice in enumerate(voices):
                voice_list.append({
                    "id": str(i),
                    "name": voice.name,
                    "language": voice.languages[0] if voice.languages else "Unknown"
                })

            return voice_list

        except Exception as e:
            print(f"Error getting Local TTS voices: {str(e)}")
            return voice_list
