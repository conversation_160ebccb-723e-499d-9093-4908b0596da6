import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
import re
import time
import subprocess
import requests
from urllib.parse import urlparse, parse_qs

class ChannelContentRepurposer:
    def __init__(self, root):
        self.root = root
        self.root.title("YouTube Channel Content Repurposer")
        self.root.geometry("800x700")
        self.root.resizable(True, True)

        # Set style
        self.style = ttk.Style()
        self.style.configure("TButton", padding=6, relief="flat", background="#ccc")
        self.style.configure("TLabel", padding=6)
        self.style.configure("TEntry", padding=6)

        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create notebook (tabs)
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create tabs
        self.setup_tab = ttk.Frame(self.notebook)
        self.channel_tab = ttk.Frame(self.notebook)
        self.content_tab = ttk.Frame(self.notebook)
        self.voice_tab = ttk.Frame(self.notebook)
        self.video_tab = ttk.Frame(self.notebook)

        self.notebook.add(self.setup_tab, text="Setup")
        self.notebook.add(self.channel_tab, text="Channel")
        self.notebook.add(self.content_tab, text="Content")
        self.notebook.add(self.voice_tab, text="Voice")
        self.notebook.add(self.video_tab, text="Video")

        # Setup each tab
        self.setup_setup_tab()
        self.setup_channel_tab()
        self.setup_content_tab()
        self.setup_voice_tab()
        self.setup_video_tab()

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Initialize variables
        self.processing_queue = []
        self.currently_processing = False
        self.channel_videos = []
        self.video_transcripts = {}
        self.rewritten_content = {}
        self.generated_voices = {}

    def setup_setup_tab(self):
        # Project directory
        dir_frame = ttk.LabelFrame(self.setup_tab, text="Project Directory")
        dir_frame.pack(fill=tk.X, padx=5, pady=5)

        dir_inner_frame = ttk.Frame(dir_frame)
        dir_inner_frame.pack(fill=tk.X, padx=5, pady=5)

        self.project_dir_var = tk.StringVar()
        dir_entry = ttk.Entry(dir_inner_frame, textvariable=self.project_dir_var, width=50)
        dir_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        dir_button = ttk.Button(dir_inner_frame, text="Browse", command=self.browse_project_dir)
        dir_button.pack(side=tk.LEFT, padx=5)

        # Background video
        bg_frame = ttk.LabelFrame(self.setup_tab, text="Background Video")
        bg_frame.pack(fill=tk.X, padx=5, pady=5)

        bg_inner_frame = ttk.Frame(bg_frame)
        bg_inner_frame.pack(fill=tk.X, padx=5, pady=5)

        self.bg_video_var = tk.StringVar()
        bg_entry = ttk.Entry(bg_inner_frame, textvariable=self.bg_video_var, width=50)
        bg_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        bg_button = ttk.Button(bg_inner_frame, text="Browse", command=self.browse_bg_video)
        bg_button.pack(side=tk.LEFT, padx=5)

        # API Keys
        api_frame = ttk.LabelFrame(self.setup_tab, text="API Keys")
        api_frame.pack(fill=tk.X, padx=5, pady=5)

        # OpenAI API Key
        openai_frame = ttk.Frame(api_frame)
        openai_frame.pack(fill=tk.X, padx=5, pady=5)

        openai_label = ttk.Label(openai_frame, text="OpenAI API Key:")
        openai_label.pack(side=tk.LEFT, padx=5)

        self.openai_api_key_var = tk.StringVar()
        openai_entry = ttk.Entry(openai_frame, textvariable=self.openai_api_key_var, width=40, show="*")
        openai_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # TTS API Key (e.g., ElevenLabs)
        tts_frame = ttk.Frame(api_frame)
        tts_frame.pack(fill=tk.X, padx=5, pady=5)

        tts_label = ttk.Label(tts_frame, text="TTS API Key:")
        tts_label.pack(side=tk.LEFT, padx=5)

        self.tts_api_key_var = tk.StringVar()
        tts_entry = ttk.Entry(tts_frame, textvariable=self.tts_api_key_var, width=40, show="*")
        tts_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # TTS Voice Selection
        voice_frame = ttk.Frame(api_frame)
        voice_frame.pack(fill=tk.X, padx=5, pady=5)

        voice_label = ttk.Label(voice_frame, text="TTS Voice:")
        voice_label.pack(side=tk.LEFT, padx=5)

        self.tts_voice_var = tk.StringVar()
        voice_combo = ttk.Combobox(voice_frame, textvariable=self.tts_voice_var)
        voice_combo['values'] = ('Default', 'Male', 'Female', 'Custom')
        voice_combo.current(0)
        voice_combo.pack(side=tk.LEFT, padx=5)

        # Save settings button
        save_button = ttk.Button(self.setup_tab, text="Save Settings", command=self.save_settings)
        save_button.pack(padx=5, pady=10)

        # Load settings if they exist
        self.load_settings()

    def setup_channel_tab(self):
        # Channel URL
        url_frame = ttk.Frame(self.channel_tab)
        url_frame.pack(fill=tk.X, padx=5, pady=5)

        url_label = ttk.Label(url_frame, text="YouTube Channel URL:")
        url_label.pack(side=tk.LEFT, padx=5)

        self.channel_url_var = tk.StringVar()
        url_entry = ttk.Entry(url_frame, textvariable=self.channel_url_var, width=50)
        url_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Fetch videos button
        fetch_button = ttk.Button(self.channel_tab, text="Fetch Channel Videos", command=self.fetch_channel_videos)
        fetch_button.pack(padx=5, pady=5)

        # Videos listbox with scrollbar
        videos_frame = ttk.LabelFrame(self.channel_tab, text="Channel Videos")
        videos_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.videos_listbox = tk.Listbox(videos_frame, selectmode=tk.EXTENDED)
        self.videos_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        videos_scrollbar = ttk.Scrollbar(videos_frame, command=self.videos_listbox.yview)
        videos_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.videos_listbox.config(yscrollcommand=videos_scrollbar.set)

        # Selection buttons
        selection_frame = ttk.Frame(self.channel_tab)
        selection_frame.pack(fill=tk.X, padx=5, pady=5)

        select_all_button = ttk.Button(selection_frame, text="Select All", command=self.select_all_videos)
        select_all_button.pack(side=tk.LEFT, padx=5)

        deselect_all_button = ttk.Button(selection_frame, text="Deselect All", command=self.deselect_all_videos)
        deselect_all_button.pack(side=tk.LEFT, padx=5)

        # Download transcripts button
        download_button = ttk.Button(self.channel_tab, text="Download Selected Video Transcripts",
                                    command=self.download_transcripts)
        download_button.pack(padx=5, pady=10)

    def setup_content_tab(self):
        # Video selection
        video_frame = ttk.Frame(self.content_tab)
        video_frame.pack(fill=tk.X, padx=5, pady=5)

        video_label = ttk.Label(video_frame, text="Select Video:")
        video_label.pack(side=tk.LEFT, padx=5)

        self.content_video_var = tk.StringVar()
        self.video_combo = ttk.Combobox(video_frame, textvariable=self.content_video_var, width=50)
        self.video_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.video_combo.bind("<<ComboboxSelected>>", self.load_transcript)

        # Original transcript
        original_frame = ttk.LabelFrame(self.content_tab, text="Original Transcript")
        original_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.original_text = scrolledtext.ScrolledText(original_frame, wrap=tk.WORD, height=10)
        self.original_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Rewrite button
        rewrite_button = ttk.Button(self.content_tab, text="Rewrite Content with ChatGPT",
                                   command=self.rewrite_content)
        rewrite_button.pack(padx=5, pady=5)

        # Rewritten content
        rewritten_frame = ttk.LabelFrame(self.content_tab, text="Rewritten Content")
        rewritten_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.rewritten_text = scrolledtext.ScrolledText(rewritten_frame, wrap=tk.WORD, height=10)
        self.rewritten_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Save rewritten content button
        save_content_button = ttk.Button(self.content_tab, text="Save Rewritten Content",
                                        command=self.save_rewritten_content)
        save_content_button.pack(padx=5, pady=5)

        # Batch process button
        batch_button = ttk.Button(self.content_tab, text="Batch Process All Transcripts",
                                 command=self.batch_process_content)
        batch_button.pack(padx=5, pady=10)

    def setup_voice_tab(self):
        # Video selection
        video_frame = ttk.Frame(self.voice_tab)
        video_frame.pack(fill=tk.X, padx=5, pady=5)

        video_label = ttk.Label(video_frame, text="Select Video:")
        video_label.pack(side=tk.LEFT, padx=5)

        self.voice_video_var = tk.StringVar()
        self.voice_video_combo = ttk.Combobox(video_frame, textvariable=self.voice_video_var, width=50)
        self.voice_video_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.voice_video_combo.bind("<<ComboboxSelected>>", self.load_rewritten_content)

        # Content to convert
        content_frame = ttk.LabelFrame(self.voice_tab, text="Content to Convert")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.voice_content_text = scrolledtext.ScrolledText(content_frame, wrap=tk.WORD, height=10)
        self.voice_content_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Generate voice button
        generate_button = ttk.Button(self.voice_tab, text="Generate Voice",
                                    command=self.generate_voice)
        generate_button.pack(padx=5, pady=5)

        # Generate subtitles button
        subtitle_button = ttk.Button(self.voice_tab, text="Generate Subtitles",
                                    command=self.generate_subtitles)
        subtitle_button.pack(padx=5, pady=5)

        # Batch process button
        batch_button = ttk.Button(self.voice_tab, text="Batch Process All Content",
                                 command=self.batch_process_voice)
        batch_button.pack(padx=5, pady=10)

    def setup_video_tab(self):
        # Video selection
        video_frame = ttk.Frame(self.video_tab)
        video_frame.pack(fill=tk.X, padx=5, pady=5)

        video_label = ttk.Label(video_frame, text="Select Video:")
        video_label.pack(side=tk.LEFT, padx=5)

        self.final_video_var = tk.StringVar()
        self.final_video_combo = ttk.Combobox(video_frame, textvariable=self.final_video_var, width=50)
        self.final_video_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Create video button
        create_button = ttk.Button(self.video_tab, text="Create Final Video",
                                  command=self.create_final_video)
        create_button.pack(padx=5, pady=5)

        # Batch process button
        batch_button = ttk.Button(self.video_tab, text="Batch Process All Videos",
                                 command=self.batch_process_videos)
        batch_button.pack(padx=5, pady=5)

        # Progress
        progress_frame = ttk.Frame(self.video_tab)
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress.pack(fill=tk.X, padx=5, pady=5)

        # Log
        log_frame = ttk.LabelFrame(self.video_tab, text="Processing Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # Placeholder methods for functionality
    def browse_project_dir(self):
        dir_path = filedialog.askdirectory()
        if dir_path:
            self.project_dir_var.set(dir_path)
            self.log_message(f"Project directory set to: {dir_path}")

            # Create necessary subdirectories
            os.makedirs(os.path.join(dir_path, "transcripts"), exist_ok=True)
            os.makedirs(os.path.join(dir_path, "rewritten"), exist_ok=True)
            os.makedirs(os.path.join(dir_path, "audio"), exist_ok=True)
            os.makedirs(os.path.join(dir_path, "subtitles"), exist_ok=True)
            os.makedirs(os.path.join(dir_path, "videos"), exist_ok=True)

    def browse_bg_video(self):
        file_path = filedialog.askopenfilename(filetypes=[("Video Files", "*.mp4;*.avi;*.mkv")])
        if file_path:
            self.bg_video_var.set(file_path)
            self.log_message(f"Background video set to: {os.path.basename(file_path)}")

    def save_settings(self):
        project_dir = self.project_dir_var.get().strip()
        if not project_dir:
            messagebox.showerror("Error", "Please set a project directory first")
            return

        # Create settings dictionary
        settings = {
            "project_dir": project_dir,
            "bg_video": self.bg_video_var.get().strip(),
            "openai_api_key": self.openai_api_key_var.get().strip(),
            "tts_api_key": self.tts_api_key_var.get().strip(),
            "tts_voice": self.tts_voice_var.get()
        }

        # Save settings to file
        settings_file = os.path.join(project_dir, "settings.json")
        try:
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4)

            self.log_message(f"Settings saved to: {settings_file}")
            self.status_var.set("Settings saved")
            messagebox.showinfo("Success", "Settings saved successfully")

        except Exception as e:
            self.log_message(f"Error saving settings: {str(e)}")
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")

    def load_settings(self):
        # Try to find settings.json in the current directory
        settings_file = "settings.json"

        if not os.path.exists(settings_file):
            # If not found, check if project_dir is set and look there
            project_dir = self.project_dir_var.get().strip()
            if project_dir:
                settings_file = os.path.join(project_dir, "settings.json")

        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # Apply settings
                if "project_dir" in settings and settings["project_dir"]:
                    self.project_dir_var.set(settings["project_dir"])

                if "bg_video" in settings and settings["bg_video"]:
                    self.bg_video_var.set(settings["bg_video"])

                if "openai_api_key" in settings and settings["openai_api_key"]:
                    self.openai_api_key_var.set(settings["openai_api_key"])

                if "tts_api_key" in settings and settings["tts_api_key"]:
                    self.tts_api_key_var.set(settings["tts_api_key"])

                if "tts_voice" in settings and settings["tts_voice"]:
                    self.tts_voice_var.set(settings["tts_voice"])

                self.log_message(f"Settings loaded from: {settings_file}")
                self.status_var.set("Settings loaded")

            except Exception as e:
                self.log_message(f"Error loading settings: {str(e)}")
                self.status_var.set("Error loading settings")

    def fetch_channel_videos(self):
        channel_url = self.channel_url_var.get().strip()
        if not channel_url:
            messagebox.showerror("Error", "Please enter a YouTube channel URL")
            return

        project_dir = self.project_dir_var.get().strip()
        if not project_dir:
            messagebox.showerror("Error", "Please set a project directory in the Setup tab")
            return

        self.status_var.set("Fetching channel videos...")
        self.log_message(f"Fetching videos from channel: {channel_url}")

        # Clear existing videos
        self.videos_listbox.delete(0, tk.END)
        self.channel_videos = []

        # Start fetching in a separate thread
        threading.Thread(target=self._fetch_channel_videos_thread,
                        args=(channel_url, project_dir),
                        daemon=True).start()

    def _fetch_channel_videos_thread(self, channel_url, project_dir):
        try:
            # Use yt-dlp to get channel video information
            command = [
                "yt-dlp",
                "--flat-playlist",
                "--print", "id",
                "--print", "title",
                channel_url
            ]

            result = subprocess.run(command, capture_output=True, text=True)

            if result.returncode != 0:
                self.log_message(f"Error fetching channel videos: {result.stderr}")
                self.status_var.set("Error fetching channel videos")
                return

            # Parse the output (alternating lines of video ID and title)
            lines = result.stdout.strip().split('\n')
            videos = []

            for i in range(0, len(lines), 2):
                if i+1 < len(lines):
                    video_id = lines[i].strip()
                    video_title = lines[i+1].strip()
                    videos.append({
                        "id": video_id,
                        "title": video_title,
                        "url": f"https://www.youtube.com/watch?v={video_id}"
                    })

            # Update the UI
            self.root.after(0, self._update_videos_list, videos)

        except Exception as e:
            self.log_message(f"Error fetching channel videos: {str(e)}")
            self.status_var.set("Error fetching channel videos")

    def _update_videos_list(self, videos):
        self.channel_videos = videos

        # Update the listbox
        for video in videos:
            self.videos_listbox.insert(tk.END, f"{video['title']} ({video['id']})")

        # Update the video comboboxes in other tabs
        video_titles = [f"{video['title']} ({video['id']})" for video in videos]
        self.video_combo['values'] = video_titles
        self.voice_video_combo['values'] = video_titles
        self.final_video_combo['values'] = video_titles

        self.log_message(f"Found {len(videos)} videos in the channel")
        self.status_var.set(f"Found {len(videos)} videos")

    def select_all_videos(self):
        self.videos_listbox.select_set(0, tk.END)

    def deselect_all_videos(self):
        self.videos_listbox.selection_clear(0, tk.END)

    def download_transcripts(self):
        selected_indices = self.videos_listbox.curselection()
        if not selected_indices:
            messagebox.showerror("Error", "Please select at least one video")
            return

        project_dir = self.project_dir_var.get().strip()
        if not project_dir:
            messagebox.showerror("Error", "Please set a project directory in the Setup tab")
            return

        # Get selected videos
        selected_videos = [self.channel_videos[i] for i in selected_indices]

        self.status_var.set(f"Downloading transcripts for {len(selected_videos)} videos...")
        self.log_message(f"Starting transcript download for {len(selected_videos)} videos")

        # Start downloading in a separate thread
        threading.Thread(target=self._download_transcripts_thread,
                        args=(selected_videos, project_dir),
                        daemon=True).start()

    def _download_transcripts_thread(self, videos, project_dir):
        transcripts_dir = os.path.join(project_dir, "transcripts")
        os.makedirs(transcripts_dir, exist_ok=True)

        successful = 0
        failed = 0

        for video in videos:
            try:
                video_id = video["id"]
                video_title = video["title"]
                video_url = video["url"]

                self.log_message(f"Downloading transcript for: {video_title}")

                # Use yt-dlp to download subtitles/transcript
                command = [
                    "yt-dlp",
                    "--skip-download",
                    "--write-auto-sub",
                    "--sub-format", "vtt",
                    "--sub-lang", "en",
                    "--output", os.path.join(transcripts_dir, f"{video_id}"),
                    video_url
                ]

                result = subprocess.run(command, capture_output=True, text=True)

                if result.returncode == 0:
                    # Check if the subtitle file was created
                    subtitle_file = os.path.join(transcripts_dir, f"{video_id}.en.vtt")
                    if os.path.exists(subtitle_file):
                        # Convert VTT to plain text
                        with open(subtitle_file, 'r', encoding='utf-8') as f:
                            vtt_content = f.read()

                        # Simple VTT to text conversion (can be improved)
                        text_content = self._convert_vtt_to_text(vtt_content)

                        # Save as plain text
                        text_file = os.path.join(transcripts_dir, f"{video_id}.txt")
                        with open(text_file, 'w', encoding='utf-8') as f:
                            f.write(text_content)

                        # Store in memory
                        self.video_transcripts[video_id] = {
                            "title": video_title,
                            "transcript": text_content
                        }

                        successful += 1
                        self.log_message(f"Successfully downloaded transcript for: {video_title}")
                    else:
                        failed += 1
                        self.log_message(f"No transcript found for: {video_title}")
                else:
                    failed += 1
                    self.log_message(f"Failed to download transcript for: {video_title}")

            except Exception as e:
                failed += 1
                self.log_message(f"Error downloading transcript for {video_title}: {str(e)}")

        self.log_message(f"Transcript download complete. Success: {successful}, Failed: {failed}")
        self.status_var.set(f"Downloaded {successful} transcripts")

    def _convert_vtt_to_text(self, vtt_content):
        """Convert VTT subtitle content to plain text"""
        # Remove header
        if "WEBVTT" in vtt_content:
            vtt_content = vtt_content.split("WEBVTT")[1]

        # Remove timestamps and formatting
        lines = []
        for line in vtt_content.split('\n'):
            # Skip timestamp lines and empty lines
            if re.match(r'^\d{2}:\d{2}:\d{2}', line) or re.match(r'^\d{2}:\d{2}', line) or line.strip() == '':
                continue

            # Remove HTML tags
            line = re.sub(r'<[^>]+>', '', line)

            if line.strip():
                lines.append(line.strip())

        # Join lines into paragraphs
        text = ' '.join(lines)

        # Clean up multiple spaces
        text = re.sub(r' +', ' ', text)

        return text

    def load_transcript(self, event=None):
        selected_video = self.content_video_var.get()
        if not selected_video:
            return

        # Extract video ID from the selected item
        match = re.search(r'\(([^)]+)\)$', selected_video)
        if not match:
            return

        video_id = match.group(1)

        # Check if we have the transcript in memory
        if video_id in self.video_transcripts:
            transcript = self.video_transcripts[video_id]["transcript"]
            self.original_text.delete(1.0, tk.END)
            self.original_text.insert(tk.END, transcript)

            # Check if we have rewritten content
            if video_id in self.rewritten_content:
                rewritten = self.rewritten_content[video_id]["content"]
                self.rewritten_text.delete(1.0, tk.END)
                self.rewritten_text.insert(tk.END, rewritten)
            else:
                self.rewritten_text.delete(1.0, tk.END)
        else:
            # Try to load from file
            project_dir = self.project_dir_var.get().strip()
            if not project_dir:
                return

            transcript_file = os.path.join(project_dir, "transcripts", f"{video_id}.txt")
            if os.path.exists(transcript_file):
                with open(transcript_file, 'r', encoding='utf-8') as f:
                    transcript = f.read()

                self.original_text.delete(1.0, tk.END)
                self.original_text.insert(tk.END, transcript)

                # Store in memory
                self.video_transcripts[video_id] = {
                    "title": selected_video.split(" (")[0],
                    "transcript": transcript
                }

                # Check for rewritten content
                rewritten_file = os.path.join(project_dir, "rewritten", f"{video_id}.txt")
                if os.path.exists(rewritten_file):
                    with open(rewritten_file, 'r', encoding='utf-8') as f:
                        rewritten = f.read()

                    self.rewritten_text.delete(1.0, tk.END)
                    self.rewritten_text.insert(tk.END, rewritten)

                    # Store in memory
                    self.rewritten_content[video_id] = {
                        "title": selected_video.split(" (")[0],
                        "content": rewritten
                    }
                else:
                    self.rewritten_text.delete(1.0, tk.END)
            else:
                self.original_text.delete(1.0, tk.END)
                self.original_text.insert(tk.END, "No transcript available for this video.")
                self.rewritten_text.delete(1.0, tk.END)

    def rewrite_content(self):
        selected_video = self.content_video_var.get()
        if not selected_video:
            messagebox.showerror("Error", "Please select a video")
            return

        # Extract video ID from the selected item
        match = re.search(r'\(([^)]+)\)$', selected_video)
        if not match:
            messagebox.showerror("Error", "Invalid video selection")
            return

        video_id = match.group(1)

        # Get the original transcript
        original_text = self.original_text.get(1.0, tk.END).strip()
        if not original_text or original_text == "No transcript available for this video.":
            messagebox.showerror("Error", "No transcript available to rewrite")
            return

        # Check if OpenAI API key is set
        openai_api_key = self.openai_api_key_var.get().strip()
        if not openai_api_key:
            messagebox.showerror("Error", "Please enter your OpenAI API key in the Setup tab")
            return

        self.status_var.set("Rewriting content with ChatGPT...")
        self.log_message(f"Rewriting content for video: {selected_video}")

        # Start rewriting in a separate thread
        threading.Thread(target=self._rewrite_content_thread,
                        args=(video_id, selected_video.split(" (")[0], original_text, openai_api_key),
                        daemon=True).start()

    def _rewrite_content_thread(self, video_id, video_title, original_text, api_key):
        try:
            import openai
            openai.api_key = api_key

            # Prepare the prompt
            prompt = f"""
            I have a transcript from a YouTube video titled "{video_title}".
            Please rewrite this content in your own words, maintaining the same information and key points,
            but making it more engaging and well-structured. The content should be suitable for a new video.

            Here's the original transcript:

            {original_text[:3000]}  # Limit to 3000 chars to avoid token limits

            Please provide only the rewritten content without any explanations or notes.
            """

            # Call the OpenAI API
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that rewrites video content."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=2000,
                temperature=0.7
            )

            # Extract the rewritten content
            rewritten_content = response.choices[0].message.content.strip()

            # Update the UI
            self.root.after(0, self._update_rewritten_content, video_id, video_title, rewritten_content)

        except Exception as e:
            self.log_message(f"Error rewriting content: {str(e)}")
            self.status_var.set("Error rewriting content")

    def _update_rewritten_content(self, video_id, video_title, rewritten_content):
        # Update the text widget
        self.rewritten_text.delete(1.0, tk.END)
        self.rewritten_text.insert(tk.END, rewritten_content)

        # Store in memory
        self.rewritten_content[video_id] = {
            "title": video_title,
            "content": rewritten_content
        }

        # Save to file
        project_dir = self.project_dir_var.get().strip()
        if project_dir:
            rewritten_dir = os.path.join(project_dir, "rewritten")
            os.makedirs(rewritten_dir, exist_ok=True)

            rewritten_file = os.path.join(rewritten_dir, f"{video_id}.txt")
            with open(rewritten_file, 'w', encoding='utf-8') as f:
                f.write(rewritten_content)

            self.log_message(f"Saved rewritten content to: {rewritten_file}")

        self.log_message(f"Successfully rewrote content for: {video_title}")
        self.status_var.set("Content rewritten successfully")

    def save_rewritten_content(self):
        selected_video = self.content_video_var.get()
        if not selected_video:
            messagebox.showerror("Error", "Please select a video")
            return

        # Extract video ID from the selected item
        match = re.search(r'\(([^)]+)\)$', selected_video)
        if not match:
            messagebox.showerror("Error", "Invalid video selection")
            return

        video_id = match.group(1)
        video_title = selected_video.split(" (")[0]

        # Get the rewritten content
        rewritten_content = self.rewritten_text.get(1.0, tk.END).strip()
        if not rewritten_content:
            messagebox.showerror("Error", "No rewritten content to save")
            return

        # Store in memory
        self.rewritten_content[video_id] = {
            "title": video_title,
            "content": rewritten_content
        }

        # Save to file
        project_dir = self.project_dir_var.get().strip()
        if not project_dir:
            messagebox.showerror("Error", "Please set a project directory in the Setup tab")
            return

        rewritten_dir = os.path.join(project_dir, "rewritten")
        os.makedirs(rewritten_dir, exist_ok=True)

        rewritten_file = os.path.join(rewritten_dir, f"{video_id}.txt")
        with open(rewritten_file, 'w', encoding='utf-8') as f:
            f.write(rewritten_content)

        self.log_message(f"Saved rewritten content to: {rewritten_file}")
        self.status_var.set("Rewritten content saved")
        messagebox.showinfo("Success", "Rewritten content saved successfully")

    def batch_process_content(self):
        # Check if OpenAI API key is set
        openai_api_key = self.openai_api_key_var.get().strip()
        if not openai_api_key:
            messagebox.showerror("Error", "Please enter your OpenAI API key in the Setup tab")
            return

        # Get all videos with transcripts
        videos_with_transcripts = []
        for video_id, data in self.video_transcripts.items():
            videos_with_transcripts.append({
                "id": video_id,
                "title": data["title"],
                "transcript": data["transcript"]
            })

        if not videos_with_transcripts:
            messagebox.showerror("Error", "No transcripts available to process")
            return

        self.status_var.set(f"Batch processing {len(videos_with_transcripts)} transcripts...")
        self.log_message(f"Starting batch processing of {len(videos_with_transcripts)} transcripts")

        # Start processing in a separate thread
        threading.Thread(target=self._batch_process_content_thread,
                        args=(videos_with_transcripts, openai_api_key),
                        daemon=True).start()

    def _batch_process_content_thread(self, videos, api_key):
        import openai
        openai.api_key = api_key

        project_dir = self.project_dir_var.get().strip()
        rewritten_dir = os.path.join(project_dir, "rewritten")
        os.makedirs(rewritten_dir, exist_ok=True)

        successful = 0
        failed = 0

        for video in videos:
            try:
                video_id = video["id"]
                video_title = video["title"]
                transcript = video["transcript"]

                # Skip if already processed
                if video_id in self.rewritten_content:
                    self.log_message(f"Skipping already processed video: {video_title}")
                    successful += 1
                    continue

                self.log_message(f"Rewriting content for: {video_title}")

                # Prepare the prompt
                prompt = f"""
                I have a transcript from a YouTube video titled "{video_title}".
                Please rewrite this content in your own words, maintaining the same information and key points,
                but making it more engaging and well-structured. The content should be suitable for a new video.

                Here's the original transcript:

                {transcript[:3000]}  # Limit to 3000 chars to avoid token limits

                Please provide only the rewritten content without any explanations or notes.
                """

                # Call the OpenAI API
                response = openai.ChatCompletion.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant that rewrites video content."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=2000,
                    temperature=0.7
                )

                # Extract the rewritten content
                rewritten_content = response.choices[0].message.content.strip()

                # Store in memory
                self.rewritten_content[video_id] = {
                    "title": video_title,
                    "content": rewritten_content
                }

                # Save to file
                rewritten_file = os.path.join(rewritten_dir, f"{video_id}.txt")
                with open(rewritten_file, 'w', encoding='utf-8') as f:
                    f.write(rewritten_content)

                successful += 1
                self.log_message(f"Successfully rewrote content for: {video_title}")

                # Add a small delay to avoid rate limits
                time.sleep(1)

            except Exception as e:
                failed += 1
                self.log_message(f"Error rewriting content for {video_title}: {str(e)}")

        self.log_message(f"Batch processing complete. Success: {successful}, Failed: {failed}")
        self.status_var.set(f"Processed {successful} transcripts")

    def load_rewritten_content(self, event=None):
        selected_video = self.voice_video_var.get()
        if not selected_video:
            return

        # Extract video ID from the selected item
        match = re.search(r'\(([^)]+)\)$', selected_video)
        if not match:
            return

        video_id = match.group(1)

        # Check if we have the rewritten content in memory
        if video_id in self.rewritten_content:
            content = self.rewritten_content[video_id]["content"]
            self.voice_content_text.delete(1.0, tk.END)
            self.voice_content_text.insert(tk.END, content)
        else:
            # Try to load from file
            project_dir = self.project_dir_var.get().strip()
            if not project_dir:
                return

            rewritten_file = os.path.join(project_dir, "rewritten", f"{video_id}.txt")
            if os.path.exists(rewritten_file):
                with open(rewritten_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                self.voice_content_text.delete(1.0, tk.END)
                self.voice_content_text.insert(tk.END, content)

                # Store in memory
                self.rewritten_content[video_id] = {
                    "title": selected_video.split(" (")[0],
                    "content": content
                }
            else:
                self.voice_content_text.delete(1.0, tk.END)
                self.voice_content_text.insert(tk.END, "No rewritten content available for this video.")

    def generate_voice(self):
        selected_video = self.voice_video_var.get()
        if not selected_video:
            messagebox.showerror("Error", "Please select a video")
            return

        # Extract video ID from the selected item
        match = re.search(r'\(([^)]+)\)$', selected_video)
        if not match:
            messagebox.showerror("Error", "Invalid video selection")
            return

        video_id = match.group(1)
        video_title = selected_video.split(" (")[0]

        # Get the content to convert
        content = self.voice_content_text.get(1.0, tk.END).strip()
        if not content or content == "No rewritten content available for this video.":
            messagebox.showerror("Error", "No content available to convert to voice")
            return

        # Check if TTS API key is set
        tts_api_key = self.tts_api_key_var.get().strip()
        if not tts_api_key:
            messagebox.showerror("Error", "Please enter your TTS API key in the Setup tab")
            return

        # Get voice selection
        voice_selection = self.tts_voice_var.get()

        self.status_var.set("Generating voice...")
        self.log_message(f"Generating voice for video: {video_title}")

        # Start voice generation in a separate thread
        threading.Thread(target=self._generate_voice_thread,
                        args=(video_id, video_title, content, tts_api_key, voice_selection),
                        daemon=True).start()

    def _generate_voice_thread(self, video_id, video_title, content, api_key, voice_selection):
        try:
            project_dir = self.project_dir_var.get().strip()
            audio_dir = os.path.join(project_dir, "audio")
            os.makedirs(audio_dir, exist_ok=True)

            output_file = os.path.join(audio_dir, f"{video_id}.mp3")

            # Use ElevenLabs API for TTS
            try:
                from elevenlabs import generate, save, set_api_key
                set_api_key(api_key)

                # Map voice selection to ElevenLabs voice IDs
                voice_map = {
                    "Default": "21m00Tcm4TlvDq8ikWAM",  # Rachel
                    "Male": "TxGEqnHWrfWFTfGW9XjX",     # Josh
                    "Female": "EXAVITQu4vr4xnSDxMaL",   # Bella
                    "Custom": "pNInz6obpgDQGcFmaJgB"    # Adam
                }

                voice_id = voice_map.get(voice_selection, voice_map["Default"])

                # Generate audio
                audio = generate(
                    text=content,
                    voice=voice_id,
                    model="eleven_monolingual_v1"
                )

                # Save audio file
                save(audio, output_file)

                self.log_message(f"Voice generated successfully: {output_file}")

                # Store in memory
                self.generated_voices[video_id] = {
                    "title": video_title,
                    "path": output_file
                }

                self.status_var.set("Voice generated successfully")

            except ImportError:
                # Fallback to gTTS if ElevenLabs is not available
                self.log_message("ElevenLabs not available, falling back to gTTS")

                from gtts import gTTS

                tts = gTTS(text=content, lang='en', slow=False)
                tts.save(output_file)

                self.log_message(f"Voice generated successfully with gTTS: {output_file}")

                # Store in memory
                self.generated_voices[video_id] = {
                    "title": video_title,
                    "path": output_file
                }

                self.status_var.set("Voice generated successfully (using gTTS)")

        except Exception as e:
            self.log_message(f"Error generating voice: {str(e)}")
            self.status_var.set("Error generating voice")

    def generate_subtitles(self):
        selected_video = self.voice_video_var.get()
        if not selected_video:
            messagebox.showerror("Error", "Please select a video")
            return

        # Extract video ID from the selected item
        match = re.search(r'\(([^)]+)\)$', selected_video)
        if not match:
            messagebox.showerror("Error", "Invalid video selection")
            return

        video_id = match.group(1)
        video_title = selected_video.split(" (")[0]

        # Get the content to convert
        content = self.voice_content_text.get(1.0, tk.END).strip()
        if not content or content == "No rewritten content available for this video.":
            messagebox.showerror("Error", "No content available to generate subtitles")
            return

        project_dir = self.project_dir_var.get().strip()
        if not project_dir:
            messagebox.showerror("Error", "Please set a project directory in the Setup tab")
            return

        self.status_var.set("Generating subtitles...")
        self.log_message(f"Generating subtitles for video: {video_title}")

        # Start subtitle generation in a separate thread
        threading.Thread(target=self._generate_subtitles_thread,
                        args=(video_id, video_title, content, project_dir),
                        daemon=True).start()

    def _generate_subtitles_thread(self, video_id, video_title, content, project_dir):
        try:
            import pysrt
            from datetime import timedelta

            subtitles_dir = os.path.join(project_dir, "subtitles")
            os.makedirs(subtitles_dir, exist_ok=True)

            output_file = os.path.join(subtitles_dir, f"{video_id}.srt")

            # Create a new SRT file
            subs = pysrt.SubRipFile()

            # Split content into sentences
            sentences = re.split(r'(?<=[.!?])\s+', content)

            # Estimate duration for each sentence (approx. 15 chars per second)
            current_time = 0
            for i, sentence in enumerate(sentences):
                if not sentence.strip():
                    continue

                # Estimate duration based on character count
                char_count = len(sentence)
                duration = max(1, char_count / 15)  # At least 1 second per subtitle

                # Create subtitle
                start_time = timedelta(seconds=current_time)
                end_time = timedelta(seconds=current_time + duration)

                sub = pysrt.SubRipItem(
                    index=i+1,
                    start=pysrt.SubRipTime(milliseconds=int(start_time.total_seconds() * 1000)),
                    end=pysrt.SubRipTime(milliseconds=int(end_time.total_seconds() * 1000)),
                    text=sentence
                )

                subs.append(sub)

                # Update current time
                current_time += duration

            # Save SRT file
            subs.save(output_file, encoding='utf-8')

            self.log_message(f"Subtitles generated successfully: {output_file}")
            self.status_var.set("Subtitles generated successfully")

        except Exception as e:
            self.log_message(f"Error generating subtitles: {str(e)}")
            self.status_var.set("Error generating subtitles")

    def batch_process_voice(self):
        # Check if TTS API key is set
        tts_api_key = self.tts_api_key_var.get().strip()
        if not tts_api_key:
            messagebox.showerror("Error", "Please enter your TTS API key in the Setup tab")
            return

        # Get voice selection
        voice_selection = self.tts_voice_var.get()

        # Get all videos with rewritten content
        videos_with_content = []
        for video_id, data in self.rewritten_content.items():
            videos_with_content.append({
                "id": video_id,
                "title": data["title"],
                "content": data["content"]
            })

        if not videos_with_content:
            messagebox.showerror("Error", "No rewritten content available to process")
            return

        self.status_var.set(f"Batch processing {len(videos_with_content)} videos...")
        self.log_message(f"Starting batch voice and subtitle generation for {len(videos_with_content)} videos")

        # Start processing in a separate thread
        threading.Thread(target=self._batch_process_voice_thread,
                        args=(videos_with_content, tts_api_key, voice_selection),
                        daemon=True).start()

    def _batch_process_voice_thread(self, videos, api_key, voice_selection):
        project_dir = self.project_dir_var.get().strip()
        audio_dir = os.path.join(project_dir, "audio")
        subtitles_dir = os.path.join(project_dir, "subtitles")

        os.makedirs(audio_dir, exist_ok=True)
        os.makedirs(subtitles_dir, exist_ok=True)

        successful_voice = 0
        failed_voice = 0
        successful_subs = 0
        failed_subs = 0

        for video in videos:
            video_id = video["id"]
            video_title = video["title"]
            content = video["content"]

            # Skip if already processed
            if video_id in self.generated_voices:
                self.log_message(f"Skipping already processed voice for: {video_title}")
                successful_voice += 1
            else:
                # Generate voice
                try:
                    self.log_message(f"Generating voice for: {video_title}")

                    output_file = os.path.join(audio_dir, f"{video_id}.mp3")

                    # Try ElevenLabs first
                    try:
                        from elevenlabs import generate, save, set_api_key
                        set_api_key(api_key)

                        # Map voice selection to ElevenLabs voice IDs
                        voice_map = {
                            "Default": "21m00Tcm4TlvDq8ikWAM",  # Rachel
                            "Male": "TxGEqnHWrfWFTfGW9XjX",     # Josh
                            "Female": "EXAVITQu4vr4xnSDxMaL",   # Bella
                            "Custom": "pNInz6obpgDQGcFmaJgB"    # Adam
                        }

                        voice_id = voice_map.get(voice_selection, voice_map["Default"])

                        # Generate audio
                        audio = generate(
                            text=content,
                            voice=voice_id,
                            model="eleven_monolingual_v1"
                        )

                        # Save audio file
                        save(audio, output_file)

                    except ImportError:
                        # Fallback to gTTS
                        self.log_message("ElevenLabs not available, falling back to gTTS")

                        from gtts import gTTS

                        tts = gTTS(text=content, lang='en', slow=False)
                        tts.save(output_file)

                    # Store in memory
                    self.generated_voices[video_id] = {
                        "title": video_title,
                        "path": output_file
                    }

                    successful_voice += 1
                    self.log_message(f"Successfully generated voice for: {video_title}")

                except Exception as e:
                    failed_voice += 1
                    self.log_message(f"Error generating voice for {video_title}: {str(e)}")

            # Generate subtitles
            try:
                import pysrt
                from datetime import timedelta

                output_file = os.path.join(subtitles_dir, f"{video_id}.srt")

                # Skip if already exists
                if os.path.exists(output_file):
                    self.log_message(f"Skipping existing subtitles for: {video_title}")
                    successful_subs += 1
                    continue

                self.log_message(f"Generating subtitles for: {video_title}")

                # Create a new SRT file
                subs = pysrt.SubRipFile()

                # Split content into sentences
                sentences = re.split(r'(?<=[.!?])\s+', content)

                # Estimate duration for each sentence (approx. 15 chars per second)
                current_time = 0
                for i, sentence in enumerate(sentences):
                    if not sentence.strip():
                        continue

                    # Estimate duration based on character count
                    char_count = len(sentence)
                    duration = max(1, char_count / 15)  # At least 1 second per subtitle

                    # Create subtitle
                    start_time = timedelta(seconds=current_time)
                    end_time = timedelta(seconds=current_time + duration)

                    sub = pysrt.SubRipItem(
                        index=i+1,
                        start=pysrt.SubRipTime(milliseconds=int(start_time.total_seconds() * 1000)),
                        end=pysrt.SubRipTime(milliseconds=int(end_time.total_seconds() * 1000)),
                        text=sentence
                    )

                    subs.append(sub)

                    # Update current time
                    current_time += duration

                # Save SRT file
                subs.save(output_file, encoding='utf-8')

                successful_subs += 1
                self.log_message(f"Successfully generated subtitles for: {video_title}")

            except Exception as e:
                failed_subs += 1
                self.log_message(f"Error generating subtitles for {video_title}: {str(e)}")

            # Add a small delay to avoid rate limits
            time.sleep(1)

        self.log_message(f"Batch processing complete.")
        self.log_message(f"Voice generation: Success: {successful_voice}, Failed: {failed_voice}")
        self.log_message(f"Subtitle generation: Success: {successful_subs}, Failed: {failed_subs}")
        self.status_var.set(f"Processed {successful_voice} voices, {successful_subs} subtitles")

    def create_final_video(self):
        selected_video = self.final_video_var.get()
        if not selected_video:
            messagebox.showerror("Error", "Please select a video")
            return

        # Extract video ID from the selected item
        match = re.search(r'\(([^)]+)\)$', selected_video)
        if not match:
            messagebox.showerror("Error", "Invalid video selection")
            return

        video_id = match.group(1)
        video_title = selected_video.split(" (")[0]

        # Check if we have the generated voice
        if video_id not in self.generated_voices:
            # Try to find the audio file
            project_dir = self.project_dir_var.get().strip()
            if not project_dir:
                messagebox.showerror("Error", "Please set a project directory in the Setup tab")
                return

            audio_file = os.path.join(project_dir, "audio", f"{video_id}.mp3")
            if not os.path.exists(audio_file):
                messagebox.showerror("Error", "No generated voice found for this video. Please generate the voice first.")
                return

            # Store in memory
            self.generated_voices[video_id] = {
                "title": video_title,
                "path": audio_file
            }

        # Check if we have the background video
        bg_video = self.bg_video_var.get().strip()
        if not bg_video:
            messagebox.showerror("Error", "Please select a background video in the Setup tab")
            return

        if not os.path.exists(bg_video):
            messagebox.showerror("Error", "The selected background video file does not exist")
            return

        # Check if we have subtitles
        project_dir = self.project_dir_var.get().strip()
        if not project_dir:
            messagebox.showerror("Error", "Please set a project directory in the Setup tab")
            return

        subtitle_file = os.path.join(project_dir, "subtitles", f"{video_id}.srt")
        if not os.path.exists(subtitle_file):
            response = messagebox.askyesno("Warning",
                                         "No subtitles found for this video. Do you want to continue without subtitles?")
            if not response:
                return
            subtitle_file = None

        self.status_var.set("Creating final video...")
        self.log_message(f"Creating final video for: {video_title}")

        # Start video creation in a separate thread
        threading.Thread(target=self._create_final_video_thread,
                        args=(video_id, video_title, self.generated_voices[video_id]["path"],
                             bg_video, subtitle_file, project_dir),
                        daemon=True).start()

    def _create_final_video_thread(self, video_id, video_title, audio_file, bg_video, subtitle_file, project_dir):
        try:
            import moviepy.editor as mp
            from moviepy.video.tools.subtitles import SubtitlesClip

            videos_dir = os.path.join(project_dir, "videos")
            os.makedirs(videos_dir, exist_ok=True)

            output_file = os.path.join(videos_dir, f"{video_id}.mp4")

            # Load the audio file
            self.log_message("Loading audio file...")
            audio = mp.AudioFileClip(audio_file)
            audio_duration = audio.duration

            # Load the background video
            self.log_message("Loading background video...")
            video = mp.VideoFileClip(bg_video)
            video_duration = video.duration

            # Check if we need to loop the video
            final_video = None
            if audio_duration > video_duration:
                self.log_message("Audio is longer than video, looping background video...")

                # Calculate how many times we need to loop the video
                loop_count = int(audio_duration / video_duration) + 1

                # Create a list of video clips to concatenate
                video_clips = []
                for i in range(loop_count):
                    video_clips.append(video)

                # Concatenate the video clips
                final_video = mp.concatenate_videoclips(video_clips)

                # Trim the final video to match the audio duration
                final_video = final_video.subclip(0, audio_duration)
            else:
                # If video is longer than audio, just use the video as is
                self.log_message("Video is longer than audio, trimming video...")
                final_video = video.subclip(0, audio_duration)

            # Set the audio
            self.log_message("Adding audio to video...")
            final_video = final_video.set_audio(audio)

            # Add subtitles if available
            if subtitle_file and os.path.exists(subtitle_file):
                try:
                    self.log_message("Adding subtitles...")

                    # Define a generator function for subtitles
                    def generator(txt):
                        return mp.TextClip(txt, font='Arial', fontsize=24, color='white',
                                         stroke_color='black', stroke_width=1,
                                         method='caption', align='center', size=final_video.size)

                    # Create subtitles clip
                    subtitles = SubtitlesClip(subtitle_file, generator)

                    # Add subtitles to video
                    final_video = mp.CompositeVideoClip([final_video, subtitles.set_position(('center', 'bottom'))])

                except Exception as e:
                    self.log_message(f"Error adding subtitles: {str(e)}")
                    self.log_message("Continuing without subtitles...")

            # Write the result to a file
            self.log_message(f"Writing final video to: {output_file}")
            final_video.write_videofile(output_file, codec='libx264', audio_codec='aac')

            self.log_message(f"Final video created successfully: {output_file}")
            self.status_var.set("Final video created successfully")

            # Close the clips to free up resources
            final_video.close()
            video.close()
            audio.close()

        except Exception as e:
            self.log_message(f"Error creating final video: {str(e)}")
            self.status_var.set("Error creating final video")

    def batch_process_videos(self):
        # Check if we have the background video
        bg_video = self.bg_video_var.get().strip()
        if not bg_video:
            messagebox.showerror("Error", "Please select a background video in the Setup tab")
            return

        if not os.path.exists(bg_video):
            messagebox.showerror("Error", "The selected background video file does not exist")
            return

        # Get project directory
        project_dir = self.project_dir_var.get().strip()
        if not project_dir:
            messagebox.showerror("Error", "Please set a project directory in the Setup tab")
            return

        # Get all videos with generated voices
        videos_with_voices = []
        for video_id, data in self.generated_voices.items():
            videos_with_voices.append({
                "id": video_id,
                "title": data["title"],
                "audio_path": data["path"]
            })

        # If no voices in memory, scan the audio directory
        if not videos_with_voices:
            audio_dir = os.path.join(project_dir, "audio")
            if os.path.exists(audio_dir):
                for file in os.listdir(audio_dir):
                    if file.endswith('.mp3'):
                        video_id = os.path.splitext(file)[0]
                        videos_with_voices.append({
                            "id": video_id,
                            "title": f"Video {video_id}",
                            "audio_path": os.path.join(audio_dir, file)
                        })

        if not videos_with_voices:
            messagebox.showerror("Error", "No generated voices found to process")
            return

        self.status_var.set(f"Batch processing {len(videos_with_voices)} videos...")
        self.log_message(f"Starting batch video creation for {len(videos_with_voices)} videos")

        # Start processing in a separate thread
        threading.Thread(target=self._batch_process_videos_thread,
                        args=(videos_with_voices, bg_video, project_dir),
                        daemon=True).start()

    def _batch_process_videos_thread(self, videos, bg_video, project_dir):
        import moviepy.editor as mp
        from moviepy.video.tools.subtitles import SubtitlesClip

        videos_dir = os.path.join(project_dir, "videos")
        subtitles_dir = os.path.join(project_dir, "subtitles")

        os.makedirs(videos_dir, exist_ok=True)

        successful = 0
        failed = 0

        for video in videos:
            try:
                video_id = video["id"]
                video_title = video["title"]
                audio_file = video["audio_path"]

                # Skip if already processed
                output_file = os.path.join(videos_dir, f"{video_id}.mp4")
                if os.path.exists(output_file):
                    self.log_message(f"Skipping already processed video: {video_title}")
                    successful += 1
                    continue

                self.log_message(f"Creating final video for: {video_title}")

                # Check for subtitles
                subtitle_file = os.path.join(subtitles_dir, f"{video_id}.srt")
                has_subtitles = os.path.exists(subtitle_file)

                # Load the audio file
                audio = mp.AudioFileClip(audio_file)
                audio_duration = audio.duration

                # Load the background video
                video_clip = mp.VideoFileClip(bg_video)
                video_duration = video_clip.duration

                # Check if we need to loop the video
                final_video = None
                if audio_duration > video_duration:
                    # Calculate how many times we need to loop the video
                    loop_count = int(audio_duration / video_duration) + 1

                    # Create a list of video clips to concatenate
                    video_clips = []
                    for i in range(loop_count):
                        video_clips.append(video_clip)

                    # Concatenate the video clips
                    final_video = mp.concatenate_videoclips(video_clips)

                    # Trim the final video to match the audio duration
                    final_video = final_video.subclip(0, audio_duration)
                else:
                    # If video is longer than audio, just use the video as is
                    final_video = video_clip.subclip(0, audio_duration)

                # Set the audio
                final_video = final_video.set_audio(audio)

                # Add subtitles if available
                if has_subtitles:
                    try:
                        # Define a generator function for subtitles
                        def generator(txt):
                            return mp.TextClip(txt, font='Arial', fontsize=24, color='white',
                                             stroke_color='black', stroke_width=1,
                                             method='caption', align='center', size=final_video.size)

                        # Create subtitles clip
                        subtitles = SubtitlesClip(subtitle_file, generator)

                        # Add subtitles to video
                        final_video = mp.CompositeVideoClip([final_video, subtitles.set_position(('center', 'bottom'))])

                    except Exception as e:
                        self.log_message(f"Error adding subtitles for {video_title}: {str(e)}")
                        self.log_message("Continuing without subtitles...")

                # Write the result to a file
                final_video.write_videofile(output_file, codec='libx264', audio_codec='aac')

                # Close the clips to free up resources
                final_video.close()

                successful += 1
                self.log_message(f"Successfully created final video for: {video_title}")

            except Exception as e:
                failed += 1
                self.log_message(f"Error creating final video for {video_title}: {str(e)}")

            # Close the clips to free up resources
            try:
                video_clip.close()
                audio.close()
            except:
                pass

        self.log_message(f"Batch processing complete. Success: {successful}, Failed: {failed}")
        self.status_var.set(f"Created {successful} videos")

    def log_message(self, message):
        # Add timestamp to message
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # Insert at the end of the log
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)  # Scroll to the end

if __name__ == "__main__":
    root = tk.Tk()
    app = ChannelContentRepurposer(root)
    root.mainloop()
