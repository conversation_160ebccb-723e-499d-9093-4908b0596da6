"""
VBee TTS API Integration
Handles Vietnamese text-to-speech generation using VBee API
"""

import requests
import json
import os
import time
import base64
from typing import Optional, Dict, Any, List


class VBeeTTS:
    """VBee Text-to-Speech API client for Vietnamese"""
    
    def __init__(self, api_key: str, base_url: str = "https://vbee.vn/api/v1"):
        """
        Initialize VBee TTS client
        
        Args:
            api_key (str): VBee API key
            base_url (str): Base URL for VBee API
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        })
    
    def get_available_voices(self) -> Optional[List[Dict[str, Any]]]:
        """
        Get available Vietnamese voices from VBee
        
        Returns:
            List of available voices or None if failed
        """
        try:
            url = f"{self.base_url}/voices"
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            if data.get('success'):
                return data.get('data', [])
            else:
                print(f"VBee API error: {data.get('message', 'Unknown error')}")
                return None
                
        except Exception as e:
            print(f"Error getting VBee voices: {str(e)}")
            return None
    
    def get_voice_info(self, voice_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific voice
        
        Args:
            voice_id (str): Voice ID to get info for
            
        Returns:
            Dict containing voice info or None if failed
        """
        try:
            voices = self.get_available_voices()
            if voices:
                for voice in voices:
                    if voice.get('voice_id') == voice_id:
                        return voice
            return None
            
        except Exception as e:
            print(f"Error getting voice info: {str(e)}")
            return None
    
    def generate_speech(self, text: str, output_file: str, voice_id: str = "hn_female_xuanmai_news_48k",
                       speed: float = 1.0, pitch: float = 1.0, volume: float = 1.0,
                       audio_format: str = "mp3") -> bool:
        """
        Generate Vietnamese speech from text using VBee TTS
        
        Args:
            text (str): Vietnamese text to convert to speech
            output_file (str): Path to save the audio file
            voice_id (str): VBee voice ID (default: hn_female_xuanmai_news_48k)
            speed (float): Speech speed (0.5-2.0)
            pitch (float): Pitch adjustment (0.5-2.0)
            volume (float): Volume level (0.1-2.0)
            audio_format (str): Output format (mp3, wav)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            url = f"{self.base_url}/tts"
            
            payload = {
                "text": text,
                "voice_id": voice_id,
                "speed": speed,
                "pitch": pitch,
                "volume": volume,
                "audio_format": audio_format,
                "sample_rate": 48000
            }
            
            print(f"Generating Vietnamese speech with VBee TTS...")
            print(f"Text length: {len(text)} characters")
            print(f"Voice: {voice_id}")
            
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('success'):
                # Check if response contains audio data or URL
                if 'audio_url' in result.get('data', {}):
                    # Download audio from URL
                    audio_url = result['data']['audio_url']
                    audio_response = requests.get(audio_url)
                    audio_response.raise_for_status()
                    
                    with open(output_file, 'wb') as f:
                        f.write(audio_response.content)
                        
                elif 'audio_data' in result.get('data', {}):
                    # Direct audio data (base64 encoded)
                    audio_data = base64.b64decode(result['data']['audio_data'])
                    
                    with open(output_file, 'wb') as f:
                        f.write(audio_data)
                        
                else:
                    print("No audio data found in response")
                    return False
                
                if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                    print(f"Vietnamese speech generated successfully: {output_file}")
                    return True
                else:
                    print("Generated file is empty or doesn't exist")
                    return False
            else:
                print(f"VBee TTS error: {result.get('message', 'Unknown error')}")
                return False
                
        except Exception as e:
            print(f"Error generating Vietnamese speech: {str(e)}")
            return False
    
    def split_vietnamese_text(self, text: str, max_length: int = 3000) -> List[str]:
        """
        Split long Vietnamese text into chunks suitable for TTS
        
        Args:
            text (str): Vietnamese text to split
            max_length (int): Maximum length per chunk
            
        Returns:
            list: List of text chunks
        """
        if len(text) <= max_length:
            return [text]
        
        chunks = []
        # Split by Vietnamese sentence endings
        sentences = []
        current_sentence = ""
        
        for char in text:
            current_sentence += char
            if char in '.!?。！？':
                sentences.append(current_sentence.strip())
                current_sentence = ""
        
        if current_sentence.strip():
            sentences.append(current_sentence.strip())
        
        current_chunk = ""
        for sentence in sentences:
            if len(current_chunk + sentence + ' ') <= max_length:
                current_chunk += sentence + ' '
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ' '
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def generate_speech_long_text(self, text: str, output_file: str, voice_id: str = "hn_female_xuanmai_news_48k",
                                 speed: float = 1.0, pitch: float = 1.0, volume: float = 1.0) -> bool:
        """
        Generate speech for long Vietnamese text by splitting into chunks
        
        Args:
            text (str): Long Vietnamese text to convert to speech
            output_file (str): Path to save the final audio file
            voice_id (str): VBee voice ID
            speed (float): Speech speed (0.5-2.0)
            pitch (float): Pitch adjustment (0.5-2.0)
            volume (float): Volume level (0.1-2.0)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            chunks = self.split_vietnamese_text(text)
            temp_files = []
            
            print(f"Splitting Vietnamese text into {len(chunks)} chunks for TTS generation...")
            
            # Generate audio for each chunk
            for i, chunk in enumerate(chunks):
                temp_file = f"{output_file}_chunk_{i}.mp3"
                temp_files.append(temp_file)
                
                print(f"Generating Vietnamese chunk {i+1}/{len(chunks)}...")
                if not self.generate_speech(chunk, temp_file, voice_id, speed, pitch, volume):
                    print(f"Failed to generate Vietnamese chunk {i+1}")
                    # Clean up temp files
                    for temp in temp_files:
                        if os.path.exists(temp):
                            os.remove(temp)
                    return False
                
                time.sleep(2)  # Delay between requests to avoid rate limiting
            
            # Combine all chunks into final file
            print("Combining Vietnamese audio chunks...")
            self._combine_audio_files(temp_files, output_file)
            
            # Clean up temp files
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            
            return os.path.exists(output_file) and os.path.getsize(output_file) > 0
            
        except Exception as e:
            print(f"Error generating speech for long Vietnamese text: {str(e)}")
            return False
    
    def _combine_audio_files(self, audio_files: List[str], output_file: str):
        """
        Combine multiple audio files into one
        
        Args:
            audio_files (list): List of audio file paths
            output_file (str): Output file path
        """
        try:
            from pydub import AudioSegment
            
            combined = AudioSegment.empty()
            for audio_file in audio_files:
                if os.path.exists(audio_file):
                    audio = AudioSegment.from_mp3(audio_file)
                    combined += audio
            
            combined.export(output_file, format="mp3")
            print(f"Combined Vietnamese audio saved: {output_file}")
            
        except ImportError:
            print("pydub not available, using simple concatenation")
            # Fallback: simple binary concatenation (not ideal for MP3)
            with open(output_file, 'wb') as outfile:
                for audio_file in audio_files:
                    if os.path.exists(audio_file):
                        with open(audio_file, 'rb') as infile:
                            outfile.write(infile.read())
    
    def get_voice_samples(self) -> Dict[str, str]:
        """
        Get sample voice IDs for different types
        
        Returns:
            Dict mapping voice types to voice IDs
        """
        return {
            "female_news": "hn_female_xuanmai_news_48k",
            "male_news": "hn_male_xuankien_news_48k", 
            "female_storytelling": "hn_female_thuminh_storytelling_48k",
            "male_storytelling": "hn_male_manhdung_storytelling_48k",
            "female_gentle": "hn_female_thuydung_gentle_48k",
            "male_gentle": "hn_male_anhduc_gentle_48k"
        }
    
    def test_connection(self) -> bool:
        """
        Test connection to VBee API
        
        Returns:
            bool: True if connection successful
        """
        try:
            voices = self.get_available_voices()
            return voices is not None and len(voices) > 0
        except Exception as e:
            print(f"VBee connection test failed: {e}")
            return False
