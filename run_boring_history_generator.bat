@echo off
echo Starting Boring History Generator...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "boring_history_generator.py" (
    echo Error: boring_history_generator.py not found
    echo Please make sure you're in the correct directory
    pause
    exit /b 1
)

REM Install requirements if needed
if exist "requirements.txt" (
    echo Installing/updating requirements...
    pip install -r requirements.txt
    echo.
)

REM Run the application
echo Launching Boring History Generator...
python boring_history_generator.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo Application exited with an error
    pause
)
