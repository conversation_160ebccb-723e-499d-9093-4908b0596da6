"""
Minimax TTS API Integration
Handles text-to-speech generation using Minimax API
"""

import requests
import json
import os
import time
from typing import Optional, Dict, Any


class MinimaxTTS:
    """Minimax Text-to-Speech API client"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.minimax.chat"):
        """
        Initialize Minimax TTS client
        
        Args:
            api_key (str): Minimax API key
            base_url (str): Base URL for Minimax API
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        })
    
    def get_available_voices(self) -> Optional[Dict[str, Any]]:
        """
        Get available voices from Minimax
        
        Returns:
            Dict containing available voices or None if failed
        """
        try:
            url = f"{self.base_url}/v1/text_to_speech/voices"
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error getting voices: {str(e)}")
            return None
    
    def generate_speech(self, text: str, output_file: str, voice_id: str = "male-qn-qingse", 
                       speed: float = 1.0, volume: float = 1.0, pitch: float = 1.0) -> bool:
        """
        Generate speech from text using Minimax TTS
        
        Args:
            text (str): Text to convert to speech
            output_file (str): Path to save the audio file
            voice_id (str): Voice ID to use
            speed (float): Speech speed (0.5-2.0)
            volume (float): Volume level (0.1-2.0)
            pitch (float): Pitch adjustment (0.5-2.0)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            url = f"{self.base_url}/v1/text_to_speech"
            
            payload = {
                "text": text,
                "voice_id": voice_id,
                "speed": speed,
                "volume": volume,
                "pitch": pitch,
                "output_format": "mp3"
            }
            
            print(f"Generating speech with Minimax TTS...")
            print(f"Text length: {len(text)} characters")
            print(f"Voice: {voice_id}")
            
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            # Check if response contains audio data or a URL
            response_data = response.json()
            
            if 'audio_url' in response_data:
                # Download audio from URL
                audio_url = response_data['audio_url']
                audio_response = requests.get(audio_url)
                audio_response.raise_for_status()
                
                with open(output_file, 'wb') as f:
                    f.write(audio_response.content)
                    
            elif 'audio_data' in response_data:
                # Direct audio data (base64 encoded)
                import base64
                audio_data = base64.b64decode(response_data['audio_data'])
                
                with open(output_file, 'wb') as f:
                    f.write(audio_data)
                    
            else:
                # Response might be direct audio content
                with open(output_file, 'wb') as f:
                    f.write(response.content)
            
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                print(f"Speech generated successfully: {output_file}")
                return True
            else:
                print("Generated file is empty or doesn't exist")
                return False
                
        except Exception as e:
            print(f"Error generating speech: {str(e)}")
            return False
    
    def generate_speech_streaming(self, text: str, output_file: str, voice_id: str = "male-qn-qingse",
                                speed: float = 1.0, volume: float = 1.0, pitch: float = 1.0) -> bool:
        """
        Generate speech with streaming (for long texts)
        
        Args:
            text (str): Text to convert to speech
            output_file (str): Path to save the audio file
            voice_id (str): Voice ID to use
            speed (float): Speech speed (0.5-2.0)
            volume (float): Volume level (0.1-2.0)
            pitch (float): Pitch adjustment (0.5-2.0)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            url = f"{self.base_url}/v1/text_to_speech/stream"
            
            payload = {
                "text": text,
                "voice_id": voice_id,
                "speed": speed,
                "volume": volume,
                "pitch": pitch,
                "output_format": "mp3"
            }
            
            print(f"Generating speech with streaming...")
            
            response = self.session.post(url, json=payload, stream=True)
            response.raise_for_status()
            
            with open(output_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                print(f"Streaming speech generated successfully: {output_file}")
                return True
            else:
                print("Generated file is empty or doesn't exist")
                return False
                
        except Exception as e:
            print(f"Error generating streaming speech: {str(e)}")
            return False
    
    def split_text_for_tts(self, text: str, max_length: int = 4000) -> list:
        """
        Split long text into chunks suitable for TTS
        
        Args:
            text (str): Text to split
            max_length (int): Maximum length per chunk
            
        Returns:
            list: List of text chunks
        """
        if len(text) <= max_length:
            return [text]
        
        chunks = []
        sentences = text.split('. ')
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk + sentence + '. ') <= max_length:
                current_chunk += sentence + '. '
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + '. '
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def generate_speech_long_text(self, text: str, output_file: str, voice_id: str = "male-qn-qingse",
                                 speed: float = 1.0, volume: float = 1.0, pitch: float = 1.0) -> bool:
        """
        Generate speech for long text by splitting into chunks
        
        Args:
            text (str): Long text to convert to speech
            output_file (str): Path to save the final audio file
            voice_id (str): Voice ID to use
            speed (float): Speech speed (0.5-2.0)
            volume (float): Volume level (0.1-2.0)
            pitch (float): Pitch adjustment (0.5-2.0)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            chunks = self.split_text_for_tts(text)
            temp_files = []
            
            print(f"Splitting text into {len(chunks)} chunks for TTS generation...")
            
            # Generate audio for each chunk
            for i, chunk in enumerate(chunks):
                temp_file = f"{output_file}_chunk_{i}.mp3"
                temp_files.append(temp_file)
                
                print(f"Generating chunk {i+1}/{len(chunks)}...")
                if not self.generate_speech(chunk, temp_file, voice_id, speed, volume, pitch):
                    print(f"Failed to generate chunk {i+1}")
                    # Clean up temp files
                    for temp in temp_files:
                        if os.path.exists(temp):
                            os.remove(temp)
                    return False
                
                time.sleep(1)  # Small delay between requests
            
            # Combine all chunks into final file
            print("Combining audio chunks...")
            self._combine_audio_files(temp_files, output_file)
            
            # Clean up temp files
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            
            return os.path.exists(output_file) and os.path.getsize(output_file) > 0
            
        except Exception as e:
            print(f"Error generating speech for long text: {str(e)}")
            return False
    
    def _combine_audio_files(self, audio_files: list, output_file: str):
        """
        Combine multiple audio files into one
        
        Args:
            audio_files (list): List of audio file paths
            output_file (str): Output file path
        """
        try:
            from pydub import AudioSegment
            
            combined = AudioSegment.empty()
            for audio_file in audio_files:
                if os.path.exists(audio_file):
                    audio = AudioSegment.from_mp3(audio_file)
                    combined += audio
            
            combined.export(output_file, format="mp3")
            
        except ImportError:
            print("pydub not available, using simple concatenation")
            # Fallback: simple binary concatenation (not ideal for MP3)
            with open(output_file, 'wb') as outfile:
                for audio_file in audio_files:
                    if os.path.exists(audio_file):
                        with open(audio_file, 'rb') as infile:
                            outfile.write(infile.read())
