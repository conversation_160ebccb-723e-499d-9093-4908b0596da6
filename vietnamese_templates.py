"""
Vietnamese Templates for Boring History Generator
Templates for generating Vietnamese content
"""

def get_vietnamese_title_generation_prompt(topic, num_titles=5, max_hook_words=14, include_runtime=True, 
                                          tone="hài hước nhẹ nhàng", focus_regions=None, custom_prefixes=None):
    """
    Generate Vietnamese title generation prompt
    
    Args:
        topic (str): Main topic for the video
        num_titles (int): Number of titles to generate
        max_hook_words (int): Maximum words in hook
        include_runtime (bool): Whether to include runtime tags
        tone (str): Tone style
        focus_regions (list): Specific regions to focus on
        custom_prefixes (list): Custom title prefixes
    """
    
    if custom_prefixes:
        prefixes = custom_prefixes
    else:
        prefixes = [
            '"Lịch Sử Buồn Ngủ | "',
            '"Câu Chuyện Lịch Sử Để Ngủ | "',
            '"Lịch Sử Cổ Đại Thư Giãn | "',
            '"Kể Chuyện Lịch Sử Ru Ngủ | "'
        ]
    
    runtime_instruction = ""
    if include_runtime:
        runtime_instruction = '<PERSON><PERSON> phù hợ<PERSON>, thê<PERSON> thẻ thời lượng nh<PERSON> "(2 GIỜ)", "(4 GIỜ)", hoặc "Kể Chuyện Nhẹ Nhàng & Âm Thanh Th<PERSON>ãn (2 GIỜ)".'
    
    regions_instruction = ""
    if focus_regions:
        regions_instruction = f"Tập trung đặc biệt vào các khu vực: {', '.join(focus_regions)}."
    
    return f"""
Vai Trò & Mục Tiêu
Hãy đóng vai một chuyên gia chiến lược nội dung YouTube có kinh nghiệm. Nhiệm vụ duy nhất của bạn là nghĩ ra những ý tưởng tiêu đề video mới mẻ, hấp dẫn phù hợp với phong cách: kể chuyện lịch sử chậm rãi, hài hước nhẹ nhàng được thiết kế để giúp người xem thư giãn hoặc ngủ.

Đầu Ra
Cung cấp {num_titles} tiêu đề video độc đáo trong danh sách đánh số.
Mỗi tiêu đề phải bắt đầu bằng một trong những tiền tố này (luân phiên tự nhiên):
{chr(10).join(prefixes)}

Sau dấu gạch đứng (|) đưa ra một hook ngắn gọn, thúc đẩy tò mò (≤ {max_hook_words} từ) để khơi gợi một sự kiện kỳ lạ, tình huống sống động, hoặc câu hỏi bất ngờ.
{runtime_instruction}

Tiêu Chí Nội Dung
Đa Dạng – Bao gồm nhiều thời đại (Cổ Đại, Trung Cổ, Cận Đại, Thế kỷ 19-20), khu vực (Châu Á, Châu Âu, Châu Mỹ, Châu Phi, Châu Đại Dương), và chủ đề (y học, ẩm thực, sinh hoạt hàng ngày, hình phạt, vai trò giới tính, tiểu sử nổi tiếng, luật lệ kỳ lạ, phát minh bị lãng quên, thành phố mất tích, sinh vật thần thoại, v.v.).
{regions_instruction}
Phong Cách – Tiêu đề nên có vẻ {tone} nhưng không giật gân; chúng phải hứa hẹn những sự kiện kỳ lạ nhưng thư giãn, hài hước nhẹ nhàng, và chi tiết sống động—không bao giờ phẫn nộ đạo đức hiện đại hoặc gây sốc để câu view.
Bao Gồm & Mới Mẻ – Ít nhất một phần ba danh sách nên làm nổi bật các nền văn hóa ít được biết đến hoặc các nhân vật không được đại diện đầy đủ.
Thân Thiện Với Giấc Ngủ – Tránh những từ ngữ khắc nghiệt trong tiêu đề ("tra tấn kinh hoàng" → "hình phạt kỳ lạ").
Logic Từ Khóa – Đưa một hoặc hai danh từ thân thiện với SEO vào mỗi tiêu đề ("con đường tơ lụa," "vườn Aztec," "thuốc chữa mất ngủ thời Victoria") trong khi giữ cách diễn đạt tự nhiên.

Chủ Đề: {topic}

Tạo danh sách ngay bây giờ.
"""


def get_vietnamese_script_generation_prompt(topic, total_words=15000, num_sections=15, words_per_section=1000,
                                           style="người kể chuyện YouTube thư giãn + châm biếm nhẹ nhàng + nhịp điệu ASMR buồn ngủ",
                                           include_intro_template=True, include_wind_down=True):
    """
    Generate Vietnamese script generation prompt
    
    Args:
        topic (str): Main topic for the script
        total_words (int): Total word count
        num_sections (int): Number of sections
        words_per_section (int): Words per section
        style (str): Narration style description
        include_intro_template (bool): Include intro template
        include_wind_down (bool): Include wind-down section
    """
    
    intro_section = ""
    if include_intro_template:
        intro_section = """
Mẫu Phần Mở Đầu (150‑200 từ, tính trong Phần 1)  
Mở đầu chính xác với:
Chào các bạn, tối nay chúng ta sẽ…  
Thu hút người nghe bằng hình ảnh sống động thì hiện tại liên quan đến chủ đề.  
Thả một câu thực tế thú vị kiểu "có lẽ bạn sẽ không sống sót được điều này".  
Bao gồm CTA này từng từ:  
Vậy nên, trước khi bạn thoải mái, hãy dành chút thời gian để like video và đăng ký—nhưng chỉ khi bạn thực sự thích những gì tôi làm ở đây.  
Mời người xem đăng vị trí & thời gian địa phương của họ.  
Kết thúc bằng lời ký kết này từng từ:  
Bây giờ, hãy tắt đèn, có thể bật quạt để có tiếng ồn nền nhẹ nhàng đó, và cùng nhau bước vào hành trình tối nay.  
Chuyển thẳng vào câu chuyện—không có tiêu đề phụ.
"""
    
    wind_down_section = ""
    if include_wind_down:
        wind_down_section = f"""
Phần Kết Thúc {words_per_section//5} Từ (cuối Phần {num_sections})  
• Làm chậm nhịp độ, làm mềm từ vựng, kéo dài câu.  
• Trấn an người nghe, làm mờ hình ảnh cuối cùng, và kết thúc bằng tiếng thì thầm êm dịu.
"""
    
    return f"""
Vai Trò
Bạn là người viết kịch bản "Lịch Sử Ru Ngủ" một người kể cho tôi.

Thông Số Toàn Cục  
Một câu chuyện liên tục, ngôi thứ hai về {topic}.  
Tổng độ dài {total_words:,}–{total_words + 1500:,} từ. 
Chia thành {num_sections} phần đánh số, {words_per_section}–{words_per_section + 100} từ MỖI PHẦN.  
Không có phần mở đầu mới giữa các phần—câu chuyện phải trôi chảy tiếp tục.

Quy Trình Hai Bước  
Bước 1 – Dàn Ý {num_sections} Điểm  
Trả lời đầu tiên với CHÍNH XÁC {num_sections} điểm.  
Điểm n = Phần n.  
≤ 8 từ tiêu đề mini + một câu giới thiệu về những gì phần đó sẽ bao gồm.  

DỪNG LẠI. Đợi "TIẾP TỤC" của tôi.

Bước 2 – Giao Phần  
Với mỗi lệnh "TIẾP TỤC":  
Nhìn lại điểm dàn ý tương ứng.  
Mở rộng chính xác nội dung đó—không trôi đến các điểm sau.  
Viết {words_per_section}–{words_per_section + 100} từ câu chuyện liền mạch.  
Bắt đầu với tiêu đề "Phần n", KHÔNG có phụ đề.  
Kết thúc với:  

[Số từ: ####]  
>>> Đang chờ "TIẾP TỤC"  
Sau Phần {num_sections}, thêm phần kết thúc {words_per_section//5} từ (xem bên dưới) và kết thúc với  
>>> Hết kịch bản. Chúc ngủ ngon.

Nếu bạn bao giờ lệch khỏi kế hoạch điểm—tự sửa trước khi gửi.
{intro_section}
Quy Tắc Phong Cách Kể Chuyện  
✓ Ngôi thứ hai hiện tại ("bạn bước đi…").  
✓ Giọng điệu = {style}.  
✓ Pha trộn chi tiết giác quan, bình luận hiện đại, và 3 câu đùa nhẹ mỗi ±{words_per_section} từ.  
✓ Mỗi phần phải chứa:  
   – 1 sự kiện lịch sử chính thống  
   – 1 chi tiết kỳ quặc hoặc ít biết  
   – 1 cụm từ tranh luận học thuật mở ("Các nhà sử học vẫn tranh luận về việc liệu…").  
✓ Giữ mức PG‑13; tránh bạo lực/tục tĩu rõ ràng.  
✓ Không trích dẫn hoặc URL; đan xen sự kiện một cách tự nhiên.  
✓ Tuyệt đối **không** có phần mở đầu mới như "Trong chương này…"—chỉ tiếp tục.

Cấu Trúc & Tính Liên Tục 
• Tiêu đề phần = "Phần 1", "Phần 2", v.v.  
• Sử dụng callback ("nhớ kem chống nắng phân cá sấu đó không?") để gắn kết.  
• KHÔNG giới thiệu lại chủ đề ở mỗi điểm ngắt phần.  
• Coi dàn ý như một hợp đồng—mỗi lời hứa của điểm phải được thực hiện trong phần tương ứng.
{wind_down_section}
BẮT ĐẦU NGAY BÂY GIỜ với BƯỚC 1—chỉ dàn ý {num_sections} điểm.
"""


def get_vietnamese_image_generation_prompt(script_section, num_images=1, 
                                          art_style="tranh minh họa sách cổ thế kỷ 15",
                                          medium="tempera & vàng ốc trên da cừu",
                                          perspective="phối cảnh phẳng thời trung cổ",
                                          aspect_ratio="16:9", 
                                          resolution="độ phân giải siêu cao (4K)",
                                          color_palette="màu sắc cổ điển (chàm, nâu đỏ, vàng nghệ, xanh mòng két)"):
    """
    Generate Vietnamese image generation prompt
    """
    
    return f"""
Bạn là người chọn cảnh và viết prompt nghệ thuật.

Mục Tiêu
Tạo chính xác {num_images} prompt tạo ảnh AI có độ chi tiết cao.
Mỗi prompt phải mô tả một khoảnh khắc hoặc bối cảnh khác nhau, khác biệt về mặt thị giác từ đoạn kịch bản được cung cấp bên dưới.
Tất cả các prompt phải chia sẻ cùng một hướng nghệ thuật: {art_style} ({medium}), {perspective}, tín hiệu độ sâu hạn chế, kết cấu giấy da nhẹ nhàng, đường viền mực nâu tinh tế, những nét trang trí vàng nhỏ, không có vật thể hoặc kỹ thuật hiện đại.

Phương Pháp
Đọc phần kịch bản.
Xác định những nhịp có thể hình dung nhất (địa điểm, hành động, hoặc tâm trạng) xảy ra theo thứ tự thời gian.
Viết một prompt cho mỗi nhịp. Giữ chúng đa dạng—nội thất vs ngoại thất, ngày vs đêm, các nhân vật khác nhau, mùa, hoặc hoạt động—để chuỗi cuối cùng cảm thấy như một cuốn sách tranh hình ảnh của văn bản.

Cấu Trúc mỗi prompt như thế này:
Prompt X: "Mô tả cảnh cốt lõi, nhân vật chính & hành động, bối cảnh, tâm trạng, điểm nhấn màu sắc — {art_style}, {medium}, {perspective}, đường viền mực nâu tinh tế, kết cấu giấy da nhẹ nhàng, tỷ lệ {aspect_ratio}, {resolution}"

Mẹo:
Sử dụng {color_palette}.
Đề cập đến những đạo cụ thời trung cổ nhỏ (giỏ mây, xô gỗ, kìm lửa sắt) khi có liên quan.
Nếu thời tiết quan trọng (tuyết, mưa, nắng mùa gặt), đan xen vào.
Giữ một câu cho mỗi prompt; tránh thuật ngữ máy ảnh ngoài "thẳng" hoặc "nhìn từ trên xuống."

Định Dạng Đầu Ra
Liệt kê mỗi prompt trên dòng riêng bắt đầu với "Prompt 1: …", "Prompt 2: …", lên đến "{num_images}".
Không thêm bình luận, giải thích, tiêu đề cảnh, hoặc dòng trống—chỉ các prompt.

Phần Kịch Bản:
{script_section}
"""


# Vietnamese voice settings for VBee
VIETNAMESE_VOICE_OPTIONS = {
    "female_news": {
        "id": "hn_female_xuanmai_news_48k",
        "name": "Xuân Mai (Nữ - Tin tức)",
        "description": "Giọng nữ chuyên nghiệp, phù hợp đọc tin tức"
    },
    "male_news": {
        "id": "hn_male_xuankien_news_48k", 
        "name": "Xuân Kiên (Nam - Tin tức)",
        "description": "Giọng nam chuyên nghiệp, phù hợp đọc tin tức"
    },
    "female_storytelling": {
        "id": "hn_female_thuminh_storytelling_48k",
        "name": "Thu Minh (Nữ - Kể chuyện)",
        "description": "Giọng nữ ấm áp, phù hợp kể chuyện"
    },
    "male_storytelling": {
        "id": "hn_male_manhdung_storytelling_48k",
        "name": "Mạnh Dũng (Nam - Kể chuyện)", 
        "description": "Giọng nam ấm áp, phù hợp kể chuyện"
    },
    "female_gentle": {
        "id": "hn_female_thuydung_gentle_48k",
        "name": "Thùy Dung (Nữ - Nhẹ nhàng)",
        "description": "Giọng nữ nhẹ nhàng, thư giãn"
    },
    "male_gentle": {
        "id": "hn_male_anhduc_gentle_48k",
        "name": "Anh Đức (Nam - Nhẹ nhàng)",
        "description": "Giọng nam nhẹ nhàng, thư giãn"
    }
}


# Vietnamese script styles
VIETNAMESE_SCRIPT_STYLES = [
    "người kể chuyện YouTube thư giãn + châm biếm nhẹ nhàng + nhịp điệu ASMR buồn ngủ",
    "người dẫn chương trình tài liệu chuyên nghiệp + giọng điệu bình tĩnh",
    "ông bà kể chuyện + ấm áp và dễ chịu",
    "giảng viên học thuật + nhịp độ đo lường + tạm dừng suy nghĩ",
    "người đọc truyện trước giờ ngủ + thì thầm nhẹ nhàng + nhịp điệu dịu dàng"
]


# Vietnamese tone options
VIETNAMESE_TONE_OPTIONS = [
    "hài hước nhẹ nhàng",
    "nghiêm túc và học thuật",
    "vui tươi và hài hước", 
    "bí ẩn và hấp dẫn",
    "ấm áp và thân thiện"
]
