"""
YouTube Channel Content Repurposer Application

This is the main application that integrates all components:
- YouTube API for channel scraping and uploads
- AI content generation for rewriting
- TTS for voice generation
- Subtitle generation
- Video composition
- Project management

This application provides a GUI for the user to interact with all these components.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
import re
import time
import subprocess
import requests
from urllib.parse import urlparse, parse_qs
import datetime
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

# Import custom modules
from youtube_api_handler import YouTube<PERSON><PERSON>Handler
from ai_content_generator import AIContentGenerator
from tts_engine import TTSEngine, TTSProvider
from subtitle_generator import SubtitleGenerator, SubtitleFormat
from video_composer import VideoComposer
from project_manager import ProjectManager, ProjectStatus, VideoStatus

# Import the existing GUI class
from channel_content_repurposer import ChannelContentRepurposer


class EnhancedChannelContentRepurposer(ChannelContentRepurposer):
    """Enhanced version of the Channel Content Repurposer with additional features"""

    def __init__(self, root):
        """
        Initialize the Enhanced Channel Content Repurposer

        Args:
            root: Tkinter root window
        """
        # Call the parent class constructor
        super().__init__(root)

        # Update window title
        self.root.title("Enhanced YouTube Channel Content Repurposer")

        # Initialize components
        self.youtube_api = None
        self.ai_generator = None
        self.tts_engine = None
        self.subtitle_generator = None
        self.video_composer = None
        self.project_manager = None

        # Add new tabs
        self.upload_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.upload_tab, text="Upload")

        self.batch_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.batch_tab, text="Batch")

        # Setup new tabs
        self.setup_upload_tab()
        self.setup_batch_tab()

        # Initialize components when settings are loaded
        self.initialize_components()

    def initialize_components(self):
        """Initialize all components with current settings"""
        # Get API keys from settings or environment variables
        openai_api_key = self.openai_api_key_var.get().strip() or os.getenv("OPENAI_API_KEY", "")
        tts_api_key = self.tts_api_key_var.get().strip() or os.getenv("ELEVENLABS_API_KEY", "")
        youtube_api_key = os.getenv("YOUTUBE_API_KEY", "")

        # Initialize YouTube API handler
        self.youtube_api = YouTubeAPIHandler(api_key=youtube_api_key)

        # Initialize AI content generator
        self.ai_generator = AIContentGenerator(api_key=openai_api_key)

        # Initialize TTS engine
        self.tts_engine = TTSEngine(provider=TTSProvider.ELEVENLABS, api_key=tts_api_key)

        # Initialize subtitle generator
        self.subtitle_generator = SubtitleGenerator(format=SubtitleFormat.SRT)

        # Initialize video composer
        self.video_composer = VideoComposer()

        # Initialize project manager with current project directory
        project_dir = self.project_dir_var.get().strip()
        if project_dir:
            self.project_manager = ProjectManager(project_dir)

    def save_settings(self):
        """Override save_settings to initialize components after saving"""
        # Call the parent method
        super().save_settings()

        # Initialize components with new settings
        self.initialize_components()

    def setup_upload_tab(self):
        """Setup the upload tab for YouTube uploads"""
        # YouTube credentials frame
        creds_frame = ttk.LabelFrame(self.upload_tab, text="YouTube Credentials")
        creds_frame.pack(fill=tk.X, padx=5, pady=5)

        # Client secrets file
        secrets_frame = ttk.Frame(creds_frame)
        secrets_frame.pack(fill=tk.X, padx=5, pady=5)

        secrets_label = ttk.Label(secrets_frame, text="Client Secrets File:")
        secrets_label.pack(side=tk.LEFT, padx=5)

        self.client_secrets_var = tk.StringVar()
        secrets_entry = ttk.Entry(secrets_frame, textvariable=self.client_secrets_var, width=40, show="*")
        secrets_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        secrets_button = ttk.Button(secrets_frame, text="Browse", command=self.browse_client_secrets)
        secrets_button.pack(side=tk.LEFT, padx=5)

        # Authenticate button
        auth_button = ttk.Button(creds_frame, text="Authenticate with YouTube", command=self.authenticate_youtube)
        auth_button.pack(padx=5, pady=5)

        # Video selection frame
        video_frame = ttk.LabelFrame(self.upload_tab, text="Video Selection")
        video_frame.pack(fill=tk.X, padx=5, pady=5)

        # Video selection
        select_frame = ttk.Frame(video_frame)
        select_frame.pack(fill=tk.X, padx=5, pady=5)

        select_label = ttk.Label(select_frame, text="Select Video:")
        select_label.pack(side=tk.LEFT, padx=5)

        self.upload_video_var = tk.StringVar()
        self.upload_video_combo = ttk.Combobox(select_frame, textvariable=self.upload_video_var, width=50)
        self.upload_video_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Video metadata frame
        metadata_frame = ttk.LabelFrame(self.upload_tab, text="Video Metadata")
        metadata_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Title
        title_frame = ttk.Frame(metadata_frame)
        title_frame.pack(fill=tk.X, padx=5, pady=5)

        title_label = ttk.Label(title_frame, text="Title:")
        title_label.pack(side=tk.LEFT, padx=5)

        self.upload_title_var = tk.StringVar()
        title_entry = ttk.Entry(title_frame, textvariable=self.upload_title_var, width=50)
        title_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Description
        desc_frame = ttk.LabelFrame(metadata_frame, text="Description")
        desc_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.upload_desc_text = scrolledtext.ScrolledText(desc_frame, wrap=tk.WORD, height=10)
        self.upload_desc_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Tags
        tags_frame = ttk.Frame(metadata_frame)
        tags_frame.pack(fill=tk.X, padx=5, pady=5)

        tags_label = ttk.Label(tags_frame, text="Tags (comma separated):")
        tags_label.pack(side=tk.LEFT, padx=5)

        self.upload_tags_var = tk.StringVar()
        tags_entry = ttk.Entry(tags_frame, textvariable=self.upload_tags_var, width=50)
        tags_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Category
        category_frame = ttk.Frame(metadata_frame)
        category_frame.pack(fill=tk.X, padx=5, pady=5)

        category_label = ttk.Label(category_frame, text="Category:")
        category_label.pack(side=tk.LEFT, padx=5)

        self.upload_category_var = tk.StringVar()
        category_combo = ttk.Combobox(category_frame, textvariable=self.upload_category_var)
        category_combo['values'] = ('Entertainment', 'Education', 'Science & Technology', 'Howto & Style', 'Music')
        category_combo.current(0)
        category_combo.pack(side=tk.LEFT, padx=5)

        # Privacy
        privacy_frame = ttk.Frame(metadata_frame)
        privacy_frame.pack(fill=tk.X, padx=5, pady=5)

        privacy_label = ttk.Label(privacy_frame, text="Privacy:")
        privacy_label.pack(side=tk.LEFT, padx=5)

        self.upload_privacy_var = tk.StringVar()
        privacy_combo = ttk.Combobox(privacy_frame, textvariable=self.upload_privacy_var)
        privacy_combo['values'] = ('private', 'unlisted', 'public')
        privacy_combo.current(0)
        privacy_combo.pack(side=tk.LEFT, padx=5)

        # Generate metadata button
        generate_button = ttk.Button(self.upload_tab, text="Generate Metadata from Content",
                                    command=self.generate_metadata)
        generate_button.pack(padx=5, pady=5)

        # Upload button
        upload_button = ttk.Button(self.upload_tab, text="Upload to YouTube",
                                  command=self.upload_to_youtube)
        upload_button.pack(padx=5, pady=10)

    def setup_batch_tab(self):
        """Setup the batch tab for batch processing"""
        # Channel URL frame
        channel_frame = ttk.Frame(self.batch_tab)
        channel_frame.pack(fill=tk.X, padx=5, pady=5)

        channel_label = ttk.Label(channel_frame, text="YouTube Channel URL:")
        channel_label.pack(side=tk.LEFT, padx=5)

        self.batch_channel_var = tk.StringVar()
        channel_entry = ttk.Entry(channel_frame, textvariable=self.batch_channel_var, width=50)
        channel_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        # Number of videos
        num_frame = ttk.Frame(self.batch_tab)
        num_frame.pack(fill=tk.X, padx=5, pady=5)

        num_label = ttk.Label(num_frame, text="Number of Videos:")
        num_label.pack(side=tk.LEFT, padx=5)

        self.batch_num_var = tk.StringVar(value="10")
        num_entry = ttk.Entry(num_frame, textvariable=self.batch_num_var, width=10)
        num_entry.pack(side=tk.LEFT, padx=5)

        # Fetch videos button
        fetch_button = ttk.Button(self.batch_tab, text="Fetch Channel Videos",
                                 command=self.batch_fetch_videos)
        fetch_button.pack(padx=5, pady=5)

        # Videos listbox with scrollbar
        videos_frame = ttk.LabelFrame(self.batch_tab, text="Channel Videos")
        videos_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.batch_videos_listbox = tk.Listbox(videos_frame, selectmode=tk.EXTENDED)
        self.batch_videos_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        videos_scrollbar = ttk.Scrollbar(videos_frame, command=self.batch_videos_listbox.yview)
        videos_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.batch_videos_listbox.config(yscrollcommand=videos_scrollbar.set)

        # Selection buttons
        selection_frame = ttk.Frame(self.batch_tab)
        selection_frame.pack(fill=tk.X, padx=5, pady=5)

        select_all_button = ttk.Button(selection_frame, text="Select All",
                                      command=self.batch_select_all)
        select_all_button.pack(side=tk.LEFT, padx=5)

        deselect_all_button = ttk.Button(selection_frame, text="Deselect All",
                                        command=self.batch_deselect_all)
        deselect_all_button.pack(side=tk.LEFT, padx=5)

        # Process options frame
        options_frame = ttk.LabelFrame(self.batch_tab, text="Processing Options")
        options_frame.pack(fill=tk.X, padx=5, pady=5)

        # Checkboxes for each step
        self.batch_download_var = tk.BooleanVar(value=True)
        download_check = ttk.Checkbutton(options_frame, text="Download Transcripts",
                                        variable=self.batch_download_var)
        download_check.pack(anchor=tk.W, padx=5, pady=2)

        self.batch_rewrite_var = tk.BooleanVar(value=True)
        rewrite_check = ttk.Checkbutton(options_frame, text="Rewrite Content",
                                       variable=self.batch_rewrite_var)
        rewrite_check.pack(anchor=tk.W, padx=5, pady=2)

        self.batch_voice_var = tk.BooleanVar(value=True)
        voice_check = ttk.Checkbutton(options_frame, text="Generate Voice",
                                     variable=self.batch_voice_var)
        voice_check.pack(anchor=tk.W, padx=5, pady=2)

        self.batch_subtitle_var = tk.BooleanVar(value=True)
        subtitle_check = ttk.Checkbutton(options_frame, text="Generate Subtitles",
                                        variable=self.batch_subtitle_var)
        subtitle_check.pack(anchor=tk.W, padx=5, pady=2)

        self.batch_video_var = tk.BooleanVar(value=True)
        video_check = ttk.Checkbutton(options_frame, text="Create Videos",
                                     variable=self.batch_video_var)
        video_check.pack(anchor=tk.W, padx=5, pady=2)

        self.batch_upload_var = tk.BooleanVar(value=False)
        upload_check = ttk.Checkbutton(options_frame, text="Upload to YouTube",
                                      variable=self.batch_upload_var)
        upload_check.pack(anchor=tk.W, padx=5, pady=2)

        # Start batch processing button
        batch_button = ttk.Button(self.batch_tab, text="Start Batch Processing",
                                 command=self.start_batch_processing)
        batch_button.pack(padx=5, pady=10)

        # Progress bar
        progress_frame = ttk.Frame(self.batch_tab)
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.batch_progress_var = tk.DoubleVar()
        self.batch_progress = ttk.Progressbar(progress_frame, variable=self.batch_progress_var, maximum=100)
        self.batch_progress.pack(fill=tk.X, padx=5, pady=5)

    # Upload tab methods
    def browse_client_secrets(self):
        """Browse for client secrets file"""
        file_path = filedialog.askopenfilename(filetypes=[("JSON Files", "*.json")])
        if file_path:
            self.client_secrets_var.set(file_path)
            self.log_message(f"Client secrets file set to: {file_path}")

    def authenticate_youtube(self):
        """Authenticate with YouTube API"""
        client_secrets_file = self.client_secrets_var.get().strip()
        if not client_secrets_file:
            messagebox.showerror("Error", "Please select a client secrets file")
            return

        if not os.path.exists(client_secrets_file):
            messagebox.showerror("Error", "Client secrets file not found")
            return

        self.status_var.set("Authenticating with YouTube...")
        self.log_message("Starting YouTube authentication")

        # Initialize YouTube API handler with client secrets
        if not self.youtube_api:
            self.youtube_api = YouTubeAPIHandler(client_secrets_file=client_secrets_file)
        else:
            self.youtube_api.client_secrets_file = client_secrets_file

        # Start authentication in a separate thread
        threading.Thread(target=self._authenticate_youtube_thread,
                        daemon=True).start()

    def _authenticate_youtube_thread(self):
        """Thread for YouTube authentication"""
        try:
            # Authenticate with YouTube
            if self.youtube_api.authenticate():
                self.root.after(0, self._authentication_success)
            else:
                self.root.after(0, self._authentication_failed)

        except Exception as e:
            self.log_message(f"Authentication error: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("Error", f"Authentication failed: {str(e)}"))
            self.status_var.set("Authentication failed")

    def _authentication_success(self):
        """Handle successful authentication"""
        self.log_message("YouTube authentication successful")
        self.status_var.set("YouTube authentication successful")
        messagebox.showinfo("Success", "YouTube authentication successful")

        # Update video list in upload tab
        self._update_upload_video_list()

    def _authentication_failed(self):
        """Handle failed authentication"""
        self.log_message("YouTube authentication failed")
        self.status_var.set("YouTube authentication failed")
        messagebox.showerror("Error", "YouTube authentication failed")

    def _update_upload_video_list(self):
        """Update the video list in the upload tab"""
        # Get videos from project
        if not self.project_manager:
            return

        # Get videos with VIDEO_CREATED status
        videos = self.project_manager.get_videos_by_status(VideoStatus.VIDEO_CREATED)

        # Update the combobox
        video_items = [f"{video['title']} ({video['id']})" for video in videos]
        self.upload_video_combo['values'] = video_items

        if video_items:
            self.upload_video_combo.current(0)
            self.load_video_metadata()

    def load_video_metadata(self):
        """Load metadata for the selected video"""
        selected_video = self.upload_video_var.get()
        if not selected_video:
            return

        # Extract video ID from the selected item
        match = re.search(r'\(([^)]+)\)$', selected_video)
        if not match:
            return

        video_id = match.group(1)

        # Get video data
        video = self.project_manager.get_video(video_id)
        if not video:
            return

        # Set title
        self.upload_title_var.set(video['title'])

        # Try to load rewritten content for description
        rewritten_content = self.project_manager.load_file(video_id, "rewritten")
        if rewritten_content:
            # Create a simple description from the content
            description = f"{video['title']}\n\n"
            description += rewritten_content[:500] + "...\n\n"
            description += "Thanks for watching! Please like and subscribe!"

            self.upload_desc_text.delete(1.0, tk.END)
            self.upload_desc_text.insert(tk.END, description)

        # Set default tags
        self.upload_tags_var.set(video['title'].replace(" ", ","))

    def generate_metadata(self):
        """Generate metadata from content using AI"""
        selected_video = self.upload_video_var.get()
        if not selected_video:
            messagebox.showerror("Error", "Please select a video")
            return

        # Extract video ID from the selected item
        match = re.search(r'\(([^)]+)\)$', selected_video)
        if not match:
            messagebox.showerror("Error", "Invalid video selection")
            return

        video_id = match.group(1)

        # Check if AI generator is initialized
        if not self.ai_generator:
            openai_api_key = self.openai_api_key_var.get().strip()
            if not openai_api_key:
                messagebox.showerror("Error", "Please enter your OpenAI API key in the Setup tab")
                return

            self.ai_generator = AIContentGenerator(api_key=openai_api_key)

        # Get rewritten content
        rewritten_content = self.project_manager.load_file(video_id, "rewritten")
        if not rewritten_content:
            messagebox.showerror("Error", "No rewritten content found for this video")
            return

        # Get video data
        video = self.project_manager.get_video(video_id)
        if not video:
            messagebox.showerror("Error", "Video data not found")
            return

        self.status_var.set("Generating metadata...")
        self.log_message(f"Generating metadata for video: {video['title']}")

        # Start generation in a separate thread
        threading.Thread(target=self._generate_metadata_thread,
                        args=(video_id, video['title'], rewritten_content),
                        daemon=True).start()

    def _generate_metadata_thread(self, video_id, title, content):
        """Thread for generating metadata"""
        try:
            # Generate title
            new_title = self.ai_generator.generate_title(content, original_title=title)

            # Generate description
            description = self.ai_generator.generate_description(content, title=new_title)

            # Generate tags
            tags = self.ai_generator.generate_tags(content, title=new_title)

            # Update UI
            self.root.after(0, lambda: self._update_metadata_ui(new_title, description, tags))

        except Exception as e:
            self.log_message(f"Error generating metadata: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to generate metadata: {str(e)}"))
            self.status_var.set("Error generating metadata")

    def _update_metadata_ui(self, title, description, tags):
        """Update UI with generated metadata"""
        if title:
            self.upload_title_var.set(title)

        if description:
            self.upload_desc_text.delete(1.0, tk.END)
            self.upload_desc_text.insert(tk.END, description)

        if tags:
            self.upload_tags_var.set(",".join(tags))

        self.log_message("Metadata generation complete")
        self.status_var.set("Metadata generation complete")

    def upload_to_youtube(self):
        """Upload the selected video to YouTube"""
        selected_video = self.upload_video_var.get()
        if not selected_video:
            messagebox.showerror("Error", "Please select a video")
            return

        # Extract video ID from the selected item
        match = re.search(r'\(([^)]+)\)$', selected_video)
        if not match:
            messagebox.showerror("Error", "Invalid video selection")
            return

        video_id = match.group(1)

        # Check if YouTube API is authenticated
        if not self.youtube_api or not self.youtube_api.authenticated:
            messagebox.showerror("Error", "Please authenticate with YouTube first")
            return

        # Get video file path
        video_path = self.project_manager.get_file_path(video_id, "video")
        if not video_path or not os.path.exists(video_path):
            messagebox.showerror("Error", "Video file not found")
            return

        # Get metadata
        title = self.upload_title_var.get().strip()
        if not title:
            messagebox.showerror("Error", "Please enter a title")
            return

        description = self.upload_desc_text.get(1.0, tk.END).strip()
        tags = [tag.strip() for tag in self.upload_tags_var.get().split(",") if tag.strip()]
        category = self.upload_category_var.get()
        privacy = self.upload_privacy_var.get()

        self.status_var.set("Uploading video to YouTube...")
        self.log_message(f"Starting YouTube upload for: {title}")

        # Start upload in a separate thread
        threading.Thread(target=self._upload_to_youtube_thread,
                        args=(video_id, video_path, title, description, tags, category, privacy),
                        daemon=True).start()

    def _upload_to_youtube_thread(self, video_id, video_path, title, description, tags, category, privacy):
        """Thread for uploading to YouTube"""
        try:
            # Upload video
            # This is a placeholder - the actual upload functionality would be implemented in the YouTubeAPIHandler
            self.log_message(f"Uploading video: {title}")
            self.log_message(f"File: {video_path}")
            self.log_message(f"Privacy: {privacy}")

            # Update status
            self.root.after(0, lambda: self.status_var.set("Video uploaded successfully"))
            self.root.after(0, lambda: messagebox.showinfo("Success", "Video uploaded successfully"))

            # Update video status
            if self.project_manager:
                self.project_manager.update_video_status(video_id, VideoStatus.UPLOADED)

        except Exception as e:
            self.log_message(f"Error uploading video: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to upload video: {str(e)}"))
            self.status_var.set("Error uploading video")

    # Batch tab methods
    def batch_fetch_videos(self):
        """Fetch videos from a channel for batch processing"""
        channel_url = self.batch_channel_var.get().strip()
        if not channel_url:
            messagebox.showerror("Error", "Please enter a YouTube channel URL")
            return

        try:
            max_results = int(self.batch_num_var.get().strip())
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number of videos")
            return

        # Check if YouTube API is initialized
        if not self.youtube_api:
            youtube_api_key = os.getenv("YOUTUBE_API_KEY", "")
            if not youtube_api_key:
                messagebox.showerror("Error", "YouTube API key not found. Please set it in the environment variables.")
                return

            self.youtube_api = YouTubeAPIHandler(api_key=youtube_api_key)

        self.status_var.set("Fetching channel videos...")
        self.log_message(f"Fetching videos from channel: {channel_url}")

        # Clear existing videos
        self.batch_videos_listbox.delete(0, tk.END)

        # Start fetching in a separate thread
        threading.Thread(target=self._batch_fetch_videos_thread,
                        args=(channel_url, max_results),
                        daemon=True).start()

    def _batch_fetch_videos_thread(self, channel_url, max_results):
        """Thread for fetching channel videos"""
        try:
            # Get channel ID from URL
            channel_id = self.youtube_api.get_channel_id_from_url(channel_url)
            if not channel_id:
                self.log_message(f"Could not extract channel ID from URL: {channel_url}")
                self.root.after(0, lambda: messagebox.showerror("Error", "Could not extract channel ID from URL"))
                self.status_var.set("Error fetching channel videos")
                return

            # Get channel videos
            videos = self.youtube_api.get_channel_videos(channel_id, max_results=max_results)

            # Update UI
            self.root.after(0, lambda: self._update_batch_videos_list(videos))

        except Exception as e:
            self.log_message(f"Error fetching channel videos: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to fetch channel videos: {str(e)}"))
            self.status_var.set("Error fetching channel videos")

    def _update_batch_videos_list(self, videos):
        """Update the batch videos list"""
        # Store videos
        self.batch_videos = videos

        # Update the listbox
        for video in videos:
            self.batch_videos_listbox.insert(tk.END, f"{video['title']} ({video['id']})")

        self.log_message(f"Found {len(videos)} videos in the channel")
        self.status_var.set(f"Found {len(videos)} videos")

    def batch_select_all(self):
        """Select all videos in the batch list"""
        self.batch_videos_listbox.select_set(0, tk.END)

    def batch_deselect_all(self):
        """Deselect all videos in the batch list"""
        self.batch_videos_listbox.selection_clear(0, tk.END)

    def start_batch_processing(self):
        """Start batch processing of selected videos"""
        selected_indices = self.batch_videos_listbox.curselection()
        if not selected_indices:
            messagebox.showerror("Error", "Please select at least one video")
            return

        # Check if project manager is initialized
        if not self.project_manager:
            project_dir = self.project_dir_var.get().strip()
            if not project_dir:
                messagebox.showerror("Error", "Please set a project directory in the Setup tab")
                return

            self.project_manager = ProjectManager(project_dir)

        # Get selected videos
        selected_videos = [self.batch_videos[i] for i in selected_indices]

        # Check required components based on selected options
        if self.batch_rewrite_var.get() and not self.ai_generator:
            openai_api_key = self.openai_api_key_var.get().strip()
            if not openai_api_key:
                messagebox.showerror("Error", "Please enter your OpenAI API key in the Setup tab")
                return

            self.ai_generator = AIContentGenerator(api_key=openai_api_key)

        if self.batch_voice_var.get() and not self.tts_engine:
            tts_api_key = self.tts_api_key_var.get().strip()
            if not tts_api_key:
                messagebox.showerror("Error", "Please enter your TTS API key in the Setup tab")
                return

            self.tts_engine = TTSEngine(provider=TTSProvider.ELEVENLABS, api_key=tts_api_key)

        # Check background video if creating videos
        if self.batch_video_var.get():
            bg_video = self.bg_video_var.get().strip()
            if not bg_video or not os.path.exists(bg_video):
                messagebox.showerror("Error", "Please select a valid background video in the Setup tab")
                return

        # Check YouTube authentication if uploading
        if self.batch_upload_var.get() and (not self.youtube_api or not self.youtube_api.authenticated):
            messagebox.showerror("Error", "Please authenticate with YouTube in the Upload tab first")
            return

        self.status_var.set(f"Starting batch processing for {len(selected_videos)} videos...")
        self.log_message(f"Starting batch processing for {len(selected_videos)} videos")

        # Reset progress bar
        self.batch_progress_var.set(0)

        # Start processing in a separate thread
        threading.Thread(target=self._batch_processing_thread,
                        args=(selected_videos,),
                        daemon=True).start()

    def _convert_vtt_to_text(self, vtt_content):
        """
        Convert WebVTT content to plain text

        Args:
            vtt_content (str): WebVTT content

        Returns:
            str: Plain text content
        """
        # Split into lines
        lines = vtt_content.split('\n')

        # Remove header lines
        while lines and not lines[0].strip().isdigit() and '-->' not in lines[0]:
            lines.pop(0)

        # Extract text content
        text_lines = []
        for line in lines:
            # Skip timestamp lines and empty lines
            if '-->' in line or not line.strip() or line.strip().isdigit():
                continue

            # Skip lines with VTT formatting tags
            if '<' in line and '>' in line:
                # Remove tags
                while '<' in line and '>' in line:
                    start = line.find('<')
                    end = line.find('>', start)
                    if start != -1 and end != -1:
                        line = line[:start] + line[end+1:]

            # Add non-empty lines to result
            if line.strip():
                text_lines.append(line.strip())

        # Join lines into paragraphs
        text = ' '.join(text_lines)

        # Clean up multiple spaces
        text = ' '.join(text.split())

        return text

    def _batch_processing_thread(self, videos):
        """Thread for batch processing videos"""
        total_videos = len(videos)
        processed = 0

        for video in videos:
            try:
                video_id = video["id"]
                video_title = video["title"]
                video_url = video["url"]

                self.log_message(f"Processing video: {video_title}")

                # Add video to project
                if not self.project_manager.get_video(video_id):
                    self.project_manager.add_video(video_id, video_title, video_url)

                # Step 1: Download transcript
                if self.batch_download_var.get():
                    self.log_message(f"Downloading transcript for: {video_title}")
                    transcript_file = self.youtube_api.download_transcript(
                        video_id, os.path.join(self.project_manager.project_dir, "transcripts"))

                    if transcript_file and os.path.exists(transcript_file):
                        # Convert VTT to plain text
                        with open(transcript_file, 'r', encoding='utf-8') as f:
                            vtt_content = f.read()

                        # Simple VTT to text conversion
                        text_content = self._convert_vtt_to_text(vtt_content)

                        # Save transcript
                        self.project_manager.save_file(video_id, "transcript", text_content)
                        self.log_message(f"Transcript downloaded for: {video_title}")
                    else:
                        self.log_message(f"Failed to download transcript for: {video_title}")
                        continue

                # Step 2: Rewrite content
                if self.batch_rewrite_var.get():
                    self.log_message(f"Rewriting content for: {video_title}")

                    # Get transcript
                    transcript = self.project_manager.load_file(video_id, "transcript")
                    if not transcript:
                        self.log_message(f"No transcript available for: {video_title}")
                        continue

                    # Rewrite content
                    rewritten = self.ai_generator.rewrite_content(transcript, video_title=video_title)
                    if rewritten:
                        # Save rewritten content
                        self.project_manager.save_file(video_id, "rewritten", rewritten)
                        self.log_message(f"Content rewritten for: {video_title}")
                    else:
                        self.log_message(f"Failed to rewrite content for: {video_title}")
                        continue

                # Step 3: Generate voice
                if self.batch_voice_var.get():
                    self.log_message(f"Generating voice for: {video_title}")

                    # Get rewritten content
                    content = self.project_manager.load_file(video_id, "rewritten")
                    if not content:
                        self.log_message(f"No rewritten content available for: {video_title}")
                        continue

                    # Generate voice
                    audio_file = self.project_manager.get_file_path(video_id, "audio")
                    if self.tts_engine.generate_speech(content, audio_file):
                        self.project_manager.update_video_status(video_id, VideoStatus.VOICE_GENERATED,
                                                               {"audio": audio_file})
                        self.log_message(f"Voice generated for: {video_title}")
                    else:
                        self.log_message(f"Failed to generate voice for: {video_title}")
                        continue

                # Step 4: Generate subtitles
                if self.batch_subtitle_var.get():
                    self.log_message(f"Generating subtitles for: {video_title}")

                    # Get rewritten content
                    content = self.project_manager.load_file(video_id, "rewritten")
                    if not content:
                        self.log_message(f"No rewritten content available for: {video_title}")
                        continue

                    # Generate subtitles
                    subtitle_file = self.project_manager.get_file_path(video_id, "subtitles")
                    if self.subtitle_generator.generate_subtitles_from_text(content, subtitle_file):
                        # Get audio file
                        audio_file = self.project_manager.get_file_path(video_id, "audio")
                        if os.path.exists(audio_file):
                            # Align subtitles with audio
                            self.subtitle_generator.align_subtitles_with_audio(subtitle_file, audio_file)

                        self.project_manager.update_video_status(video_id, VideoStatus.SUBTITLES_GENERATED,
                                                               {"subtitles": subtitle_file})
                        self.log_message(f"Subtitles generated for: {video_title}")
                    else:
                        self.log_message(f"Failed to generate subtitles for: {video_title}")
                        continue

                # Step 5: Create video
                if self.batch_video_var.get():
                    self.log_message(f"Creating video for: {video_title}")

                    # Get audio file
                    audio_file = self.project_manager.get_file_path(video_id, "audio")
                    if not audio_file or not os.path.exists(audio_file):
                        self.log_message(f"No audio file available for: {video_title}")
                        continue

                    # Get background video
                    bg_video = self.bg_video_var.get().strip()

                    # Get subtitle file
                    subtitle_file = self.project_manager.get_file_path(video_id, "subtitles")
                    if not os.path.exists(subtitle_file):
                        subtitle_file = None

                    # Create video
                    video_file = self.project_manager.get_file_path(video_id, "video")
                    if self.video_composer.create_complete_video(
                        audio_file, bg_video, subtitle_file, video_file, title=video_title):
                        self.project_manager.update_video_status(video_id, VideoStatus.VIDEO_CREATED,
                                                               {"video": video_file})
                        self.log_message(f"Video created for: {video_title}")
                    else:
                        self.log_message(f"Failed to create video for: {video_title}")
                        continue

                # Step 6: Upload to YouTube
                if self.batch_upload_var.get():
                    self.log_message(f"Uploading video to YouTube: {video_title}")

                    # Get video file
                    video_file = self.project_manager.get_file_path(video_id, "video")
                    if not video_file or not os.path.exists(video_file):
                        self.log_message(f"No video file available for: {video_title}")
                        continue

                    # Get content for description
                    content = self.project_manager.load_file(video_id, "rewritten")
                    description = f"{video_title}\n\n"
                    if content:
                        description += content[:500] + "...\n\n"
                    description += "Thanks for watching! Please like and subscribe!"

                    # Generate tags
                    tags = video_title.split()

                    # Upload video (placeholder)
                    self.log_message(f"Video would be uploaded: {video_title}")
                    self.project_manager.update_video_status(video_id, VideoStatus.UPLOADED)

                # Update progress
                processed += 1
                progress = (processed / total_videos) * 100
                self.root.after(0, lambda p=progress: self.batch_progress_var.set(p))

            except Exception as e:
                self.log_message(f"Error processing video {video_title}: {str(e)}")

        self.log_message(f"Batch processing complete. Processed {processed} of {total_videos} videos.")
        self.root.after(0, lambda: self.status_var.set(f"Batch processing complete"))

        # Update upload tab video list
        self.root.after(0, self._update_upload_video_list)


if __name__ == "__main__":
    root = tk.Tk()
    app = EnhancedChannelContentRepurposer(root)
    root.mainloop()
