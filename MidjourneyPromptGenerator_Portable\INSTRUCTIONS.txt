# Midjourney Prompt Generator - Portable Version

## Quick Start:
1. Run MidjourneyPromptGenerator.exe
2. Go to Setup tab and enter your OpenAI API key
3. Set your prompts and images directories
4. Click "Save Settings"
5. Go to Script tab and load demo_script.txt or enter your own script
6. Click "Analyze Script" to start

## Settings:
- Settings are automatically saved to your user AppData folder
- On Windows: %APPDATA%\MidjourneyPromptGenerator\settings.json
- You can also use the sample_settings.json as a template

## Note:
- Midjourney doesn't have a public API
- Use DALL-E 3 integration for actual image generation
- Or copy prompts to use manually in Discord

## Files included:
- MidjourneyPromptGenerator.exe - Main application
- demo_script.txt - Sample script for testing
- README_midjourney_prompt_generator.md - Full documentation
- sample_settings.json - Settings template
- INSTRUCTIONS.txt - This file
