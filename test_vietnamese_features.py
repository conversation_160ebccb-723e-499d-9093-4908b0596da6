"""
Test Vietnamese Features
Test the new Vietnamese templates and VBee TTS integration
"""

import os
import sys

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_vietnamese_templates():
    """Test Vietnamese template generation"""
    print("=== Testing Vietnamese Templates ===\n")
    
    try:
        from vietnamese_templates import (
            get_vietnamese_title_generation_prompt,
            get_vietnamese_script_generation_prompt,
            get_vietnamese_image_generation_prompt,
            VIETNAMESE_VOICE_OPTIONS,
            VIETNAMESE_SCRIPT_STYLES,
            VIETNAMESE_TONE_OPTIONS
        )
        
        topic = "Những Người Hề Cung Đình Thời Trung Cổ"
        
        # Test title generation
        print("1. Vietnamese Title Generation:")
        print("-" * 50)
        title_prompt = get_vietnamese_title_generation_prompt(topic)
        print(title_prompt[:300] + "...")
        print()
        
        # Test script generation
        print("2. Vietnamese Script Generation:")
        print("-" * 50)
        script_prompt = get_vietnamese_script_generation_prompt(topic)
        print(script_prompt[:300] + "...")
        print()
        
        # Test image generation
        print("3. Vietnamese Image Generation:")
        print("-" * 50)
        sample_script = "Bạn đang đứng trong sân lâu đài thời trung cổ. Người hề cung đình tiến đến với bộ trang phục đầy màu sắc và những chiếc chuông leng keng..."
        image_prompt = get_vietnamese_image_generation_prompt(sample_script, 2)
        print(image_prompt[:300] + "...")
        print()
        
        # Test voice options
        print("4. Vietnamese Voice Options:")
        print("-" * 50)
        for key, voice in VIETNAMESE_VOICE_OPTIONS.items():
            print(f"   {key}: {voice['name']} - {voice['description']}")
        print()
        
        # Test script styles
        print("5. Vietnamese Script Styles:")
        print("-" * 50)
        for i, style in enumerate(VIETNAMESE_SCRIPT_STYLES, 1):
            print(f"   {i}. {style}")
        print()
        
        # Test tone options
        print("6. Vietnamese Tone Options:")
        print("-" * 50)
        for i, tone in enumerate(VIETNAMESE_TONE_OPTIONS, 1):
            print(f"   {i}. {tone}")
        print()
        
        print("✅ Vietnamese templates test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Vietnamese templates test failed: {e}")
        return False


def test_vbee_tts():
    """Test VBee TTS integration (requires API key)"""
    print("\n=== Testing VBee TTS ===\n")
    
    try:
        from vbee_tts import VBeeTTS
        
        # Check if API key is available
        api_key = None
        try:
            if os.path.exists("boring_history_settings.json"):
                import json
                with open("boring_history_settings.json", "r") as f:
                    settings = json.load(f)
                    api_key = settings.get("vbee_api_key")
        except:
            pass
        
        if not api_key:
            print("No VBee API key found. Skipping VBee TTS test.")
            print("Add your VBee API key to boring_history_settings.json to test.")
            return True
        
        # Test VBee TTS
        vbee = VBeeTTS(api_key=api_key)
        
        # Test connection
        print("Testing VBee connection...")
        if vbee.test_connection():
            print("✅ VBee connection successful")
        else:
            print("❌ VBee connection failed")
            return False
        
        # Test getting voices
        print("Getting available Vietnamese voices...")
        voices = vbee.get_available_voices()
        if voices:
            print(f"✅ Found {len(voices)} Vietnamese voices")
            for voice in voices[:3]:  # Show first 3
                print(f"   - {voice.get('voice_id', 'Unknown')}: {voice.get('name', 'Unknown')}")
        else:
            print("❌ No voices returned")
        
        # Test voice samples
        print("Testing voice sample IDs...")
        samples = vbee.get_voice_samples()
        for voice_type, voice_id in samples.items():
            print(f"   {voice_type}: {voice_id}")
        
        # Test short text generation
        test_text = "Xin chào, đây là bài kiểm tra giọng đọc tiếng Việt với VBee TTS."
        output_file = "test_vbee_voice.mp3"
        
        print(f"Generating test Vietnamese voice: '{test_text[:30]}...'")
        success = vbee.generate_speech(test_text, output_file)
        
        if success and os.path.exists(output_file):
            print(f"✅ Vietnamese voice generated successfully: {output_file}")
            print(f"File size: {os.path.getsize(output_file)} bytes")
        else:
            print("❌ Vietnamese voice generation failed")
        
        print("✅ VBee TTS test completed!")
        return True
        
    except Exception as e:
        print(f"❌ VBee TTS test failed: {e}")
        return False


def test_flexible_templates_vietnamese():
    """Test flexible templates with Vietnamese support"""
    print("\n=== Testing Flexible Templates with Vietnamese ===\n")
    
    try:
        from flexible_templates import TemplateConfig
        
        # Test default config
        config = TemplateConfig()
        print(f"Default language: {getattr(config, 'language', 'english')}")
        
        # Test English prompts
        topic = "Medieval Court Jesters"
        english_title = config.get_title_prompt(topic)
        print("English title prompt (first 100 chars):")
        print(english_title[:100] + "...")
        print()
        
        # Switch to Vietnamese
        config.language = "vietnamese"
        print(f"Switched to language: {config.language}")
        
        # Test Vietnamese prompts
        vietnamese_topic = "Những Người Hề Cung Đình Thời Trung Cổ"
        vietnamese_title = config.get_title_prompt(vietnamese_topic)
        print("Vietnamese title prompt (first 100 chars):")
        print(vietnamese_title[:100] + "...")
        print()
        
        vietnamese_script = config.get_script_prompt(vietnamese_topic)
        print("Vietnamese script prompt (first 100 chars):")
        print(vietnamese_script[:100] + "...")
        print()
        
        print("✅ Flexible templates Vietnamese test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Flexible templates Vietnamese test failed: {e}")
        return False


def test_template_configurator_vietnamese():
    """Test template configurator with Vietnamese interface"""
    print("\n=== Testing Template Configurator Vietnamese Interface ===\n")
    
    try:
        import tkinter as tk
        from template_configurator import TemplateConfigurator
        
        # Create test window
        root = tk.Tk()
        root.withdraw()  # Hide main window
        
        # Create configurator
        configurator = TemplateConfigurator(root)
        
        print("✅ Template configurator created with Vietnamese interface")
        print("📝 Check the following in the GUI:")
        print("   - Tab names are in Vietnamese")
        print("   - Labels and descriptions are in Vietnamese")
        print("   - Language selection dropdown")
        print("   - Vietnamese style options")
        print("   - Vietnamese tone options")
        
        # Test language switching
        if hasattr(configurator, 'language_var'):
            configurator.language_var.set("vietnamese")
            configurator.on_language_change()
            print("✅ Language switched to Vietnamese")
        
        print("Close the configurator window to continue...")
        
        # Show the configurator
        configurator.window.deiconify()
        root.wait_window(configurator.window)
        
        root.destroy()
        print("✅ Template configurator test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Template configurator test failed: {e}")
        return False


def test_main_app_vietnamese():
    """Test main application with Vietnamese features"""
    print("\n=== Testing Main Application Vietnamese Features ===\n")
    
    try:
        import tkinter as tk
        from boring_history_generator import BoringHistoryGenerator
        
        print("🚀 Starting main application with Vietnamese features...")
        print("📝 Test the following features:")
        print("   - Vietnamese interface")
        print("   - VBee TTS option in settings")
        print("   - Vietnamese voice selection")
        print("   - Template configurator with Vietnamese")
        print("   - Vietnamese prompts generation")
        
        # Create and run the main application
        root = tk.Tk()
        app = BoringHistoryGenerator(root)
        
        print("✅ Main application started successfully")
        print("🔍 Manually test Vietnamese features and close when done")
        
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Main application test failed: {e}")
        return False


def main():
    """Run all Vietnamese feature tests"""
    print("🧪 Vietnamese Features Test Suite")
    print("=" * 60)
    
    # Test 1: Vietnamese templates
    templates_ok = test_vietnamese_templates()
    
    # Test 2: VBee TTS
    vbee_ok = test_vbee_tts()
    
    # Test 3: Flexible templates with Vietnamese
    flexible_ok = test_flexible_templates_vietnamese()
    
    # Test 4: Template configurator (optional)
    response = input("\nDo you want to test the template configurator GUI? (y/n): ")
    if response.lower() == 'y':
        configurator_ok = test_template_configurator_vietnamese()
    else:
        print("⏭️ Skipping template configurator test")
        configurator_ok = True
    
    # Test 5: Main application (optional)
    response = input("\nDo you want to test the main application? (y/n): ")
    if response.lower() == 'y':
        app_ok = test_main_app_vietnamese()
    else:
        print("⏭️ Skipping main application test")
        app_ok = True
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Vietnamese Features Test Results:")
    print(f"   Vietnamese Templates: {'✅ PASS' if templates_ok else '❌ FAIL'}")
    print(f"   VBee TTS Integration: {'✅ PASS' if vbee_ok else '❌ FAIL'}")
    print(f"   Flexible Templates: {'✅ PASS' if flexible_ok else '❌ FAIL'}")
    print(f"   Template Configurator: {'✅ PASS' if configurator_ok else '⏭️ SKIPPED'}")
    print(f"   Main Application: {'✅ PASS' if app_ok else '⏭️ SKIPPED'}")
    
    if all([templates_ok, vbee_ok, flexible_ok]):
        print("\n🎉 All Vietnamese features are working correctly!")
        print("💡 You can now create Vietnamese content with:")
        print("   🎬 Vietnamese video titles")
        print("   📜 Vietnamese scripts")
        print("   🎤 Vietnamese TTS with VBee")
        print("   🎨 Vietnamese image prompts")
        print("   ⚙️ Vietnamese interface")
    else:
        print("\n⚠️ Some Vietnamese features have issues")
        print("💡 Check the error messages above for details")
    
    print("\n📚 New Vietnamese Features:")
    print("   🇻🇳 Full Vietnamese language support")
    print("   🎤 VBee TTS for natural Vietnamese voice")
    print("   📝 Vietnamese script templates")
    print("   🎨 Vietnamese image generation prompts")
    print("   ⚙️ Vietnamese GUI interface")
    print("   🔧 Flexible template configuration")


if __name__ == "__main__":
    main()
