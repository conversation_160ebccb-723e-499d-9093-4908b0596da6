import os
import subprocess
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import moviepy.editor as mp
import re
import time

class AutoYouTubeMerger:
    def __init__(self, root):
        self.root = root
        self.root.title("Auto YouTube Audio + Video Merger")
        self.root.geometry("700x600")
        self.root.resizable(True, True)

        # Set style
        self.style = ttk.Style()
        self.style.configure("TButton", padding=6, relief="flat", background="#ccc")
        self.style.configure("TLabel", padding=6)
        self.style.configure("TEntry", padding=6)

        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Setup folders section
        self.setup_folders_section(main_frame)

        # Setup batch URLs section
        self.setup_batch_section(main_frame)

        # Setup log section
        self.setup_log_section(main_frame)

        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Initialize variables
        self.processing_queue = []
        self.currently_processing = False

    def setup_folders_section(self, parent):
        folders_frame = ttk.LabelFrame(parent, text="Folders Setup")
        folders_frame.pack(fill=tk.X, padx=5, pady=5)

        # Output directory
        output_dir_frame = ttk.Frame(folders_frame)
        output_dir_frame.pack(fill=tk.X, padx=5, pady=5)

        output_dir_label = ttk.Label(output_dir_frame, text="Output Directory:")
        output_dir_label.pack(side=tk.LEFT, padx=5)

        self.output_dir_var = tk.StringVar()
        output_dir_entry = ttk.Entry(output_dir_frame, textvariable=self.output_dir_var, width=40)
        output_dir_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        output_dir_button = ttk.Button(output_dir_frame, text="Browse", command=self.browse_output_dir)
        output_dir_button.pack(side=tk.LEFT, padx=5)

        # Background video file (direct selection)
        bg_video_frame = ttk.Frame(folders_frame)
        bg_video_frame.pack(fill=tk.X, padx=5, pady=5)

        bg_video_label = ttk.Label(bg_video_frame, text="Background Video File:")
        bg_video_label.pack(side=tk.LEFT, padx=5)

        self.bg_video_var = tk.StringVar()
        bg_video_entry = ttk.Entry(bg_video_frame, textvariable=self.bg_video_var, width=40)
        bg_video_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

        bg_video_button = ttk.Button(bg_video_frame, text="Browse", command=self.browse_bg_video)
        bg_video_button.pack(side=tk.LEFT, padx=5)

        # Loop video option
        options_frame = ttk.Frame(folders_frame)
        options_frame.pack(fill=tk.X, padx=5, pady=5)

        self.loop_var = tk.BooleanVar(value=True)
        loop_check = ttk.Checkbutton(options_frame, text="Loop background video until audio ends", variable=self.loop_var)
        loop_check.pack(side=tk.LEFT, padx=5)

    def setup_batch_section(self, parent):
        # Create notebook for YouTube URLs and Local Audio Files
        batch_notebook = ttk.Notebook(parent)
        batch_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create tabs
        youtube_tab = ttk.Frame(batch_notebook)
        local_audio_tab = ttk.Frame(batch_notebook)

        batch_notebook.add(youtube_tab, text="YouTube URLs")
        batch_notebook.add(local_audio_tab, text="Local Audio Files")

        # Setup YouTube tab
        self.setup_youtube_tab(youtube_tab)

        # Setup Local Audio tab
        self.setup_local_audio_tab(local_audio_tab)

        # Progress
        progress_frame = ttk.Frame(parent)
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.progress_var = tk.DoubleVar()
        self.progress = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress.pack(fill=tk.X, padx=5, pady=5)

    def setup_youtube_tab(self, parent):
        # Label
        label = ttk.Label(parent, text="Enter YouTube URLs (one per line):")
        label.pack(anchor=tk.W, padx=5, pady=5)

        # URLs text area
        urls_frame = ttk.Frame(parent)
        urls_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.batch_urls_text = tk.Text(urls_frame, wrap=tk.WORD, height=10)
        self.batch_urls_text.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

        urls_scrollbar = ttk.Scrollbar(urls_frame, command=self.batch_urls_text.yview)
        urls_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.batch_urls_text.config(yscrollcommand=urls_scrollbar.set)

        # Process button
        process_button = ttk.Button(parent, text="Process YouTube URLs", command=self.process_youtube_urls)
        process_button.pack(padx=5, pady=10)

    def setup_local_audio_tab(self, parent):
        # Label
        label = ttk.Label(parent, text="Select local audio files to merge with background video:")
        label.pack(anchor=tk.W, padx=5, pady=5)

        # Audio files listbox
        listbox_frame = ttk.Frame(parent)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.audio_files_listbox = tk.Listbox(listbox_frame, selectmode=tk.EXTENDED)
        self.audio_files_listbox.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

        listbox_scrollbar = ttk.Scrollbar(listbox_frame, command=self.audio_files_listbox.yview)
        listbox_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.audio_files_listbox.config(yscrollcommand=listbox_scrollbar.set)

        # Buttons frame
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        add_button = ttk.Button(buttons_frame, text="Add Files", command=self.add_audio_files)
        add_button.pack(side=tk.LEFT, padx=5)

        remove_button = ttk.Button(buttons_frame, text="Remove Selected", command=self.remove_audio_files)
        remove_button.pack(side=tk.LEFT, padx=5)

        clear_button = ttk.Button(buttons_frame, text="Clear All", command=self.clear_audio_files)
        clear_button.pack(side=tk.LEFT, padx=5)

        # Process button
        process_button = ttk.Button(parent, text="Process Local Audio Files", command=self.process_local_audio)
        process_button.pack(padx=5, pady=10)

    def setup_log_section(self, parent):
        log_frame = ttk.LabelFrame(parent, text="Processing Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        scrollbar = ttk.Scrollbar(self.log_text, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)

    def browse_output_dir(self):
        dir_path = filedialog.askdirectory()
        if dir_path:
            self.output_dir_var.set(dir_path)

    def browse_bg_video(self):
        file_path = filedialog.askopenfilename(filetypes=[("Video Files", "*.mp4;*.avi;*.mkv")])
        if file_path:
            self.bg_video_var.set(file_path)
            self.log_message(f"Selected background video: {os.path.basename(file_path)}")

    def add_audio_files(self):
        file_paths = filedialog.askopenfilenames(filetypes=[("Audio Files", "*.mp3;*.wav;*.m4a;*.webm;*.opus")])
        if file_paths:
            for path in file_paths:
                # Check if file is already in the list
                if path not in self.get_audio_files_list():
                    self.audio_files_listbox.insert(tk.END, path)
            self.log_message(f"Added {len(file_paths)} audio file(s) to the list")

    def remove_audio_files(self):
        selected_indices = self.audio_files_listbox.curselection()
        if selected_indices:
            # Remove in reverse order to avoid index shifting
            for index in sorted(selected_indices, reverse=True):
                self.audio_files_listbox.delete(index)
            self.log_message(f"Removed {len(selected_indices)} audio file(s) from the list")

    def clear_audio_files(self):
        self.audio_files_listbox.delete(0, tk.END)
        self.log_message("Cleared all audio files from the list")

    def get_audio_files_list(self):
        """Get all audio files from the listbox"""
        return [self.audio_files_listbox.get(i) for i in range(self.audio_files_listbox.size())]

    def process_youtube_urls(self):
        # Get URLs from text area
        urls_text = self.batch_urls_text.get(1.0, tk.END).strip()
        if not urls_text:
            messagebox.showerror("Error", "Please enter at least one YouTube URL")
            return

        # Split by newlines and filter out empty lines
        urls = [url.strip() for url in urls_text.split('\n') if url.strip()]

        # Get output directory
        output_dir = self.output_dir_var.get().strip()
        if not output_dir:
            messagebox.showerror("Error", "Please select an output directory")
            return

        # Get background video file
        bg_video = self.bg_video_var.get().strip()
        if not bg_video:
            messagebox.showerror("Error", "Please select a background video file")
            return

        if not os.path.exists(bg_video):
            messagebox.showerror("Error", "The selected background video file does not exist")
            return

        # Create output directories
        audio_dir = os.path.join(output_dir, "audio")
        video_dir = os.path.join(output_dir, "videos")

        try:
            os.makedirs(audio_dir, exist_ok=True)
            os.makedirs(video_dir, exist_ok=True)
        except Exception as e:
            messagebox.showerror("Error", f"Could not create output directories: {str(e)}")
            return

        # Clear the processing queue and add new URLs
        self.processing_queue = []
        for url in urls:
            self.processing_queue.append({
                "type": "youtube",
                "url": url,
                "audio_dir": audio_dir,
                "video_dir": video_dir,
                "bg_video": bg_video,
                "loop_video": self.loop_var.get()
            })

        # Start processing
        self.progress_var.set(0)
        self.status_var.set(f"Processing {len(urls)} YouTube URLs...")
        self.log_message(f"Starting to process {len(urls)} YouTube URLs")
        self.log_message(f"Using background video: {os.path.basename(bg_video)}")

        # Start the processing thread if not already running
        if not self.currently_processing:
            self.currently_processing = True
            threading.Thread(target=self.process_queue, daemon=True).start()

    def process_local_audio(self):
        # Get audio files from listbox
        audio_files = self.get_audio_files_list()
        if not audio_files:
            messagebox.showerror("Error", "Please add at least one audio file")
            return

        # Get output directory
        output_dir = self.output_dir_var.get().strip()
        if not output_dir:
            messagebox.showerror("Error", "Please select an output directory")
            return

        # Get background video file
        bg_video = self.bg_video_var.get().strip()
        if not bg_video:
            messagebox.showerror("Error", "Please select a background video file")
            return

        if not os.path.exists(bg_video):
            messagebox.showerror("Error", "The selected background video file does not exist")
            return

        # Check if all audio files exist
        missing_files = [f for f in audio_files if not os.path.exists(f)]
        if missing_files:
            messagebox.showerror("Error", f"The following audio files do not exist:\n{chr(10).join(missing_files)}")
            return

        # Create output directory
        video_dir = os.path.join(output_dir, "videos")

        try:
            os.makedirs(video_dir, exist_ok=True)
        except Exception as e:
            messagebox.showerror("Error", f"Could not create output directory: {str(e)}")
            return

        # Clear the processing queue and add new audio files
        self.processing_queue = []
        for audio_file in audio_files:
            self.processing_queue.append({
                "type": "local",
                "audio_file": audio_file,
                "video_dir": video_dir,
                "bg_video": bg_video,
                "loop_video": self.loop_var.get()
            })

        # Start processing
        self.progress_var.set(0)
        self.status_var.set(f"Processing {len(audio_files)} local audio files...")
        self.log_message(f"Starting to process {len(audio_files)} local audio files")
        self.log_message(f"Using background video: {os.path.basename(bg_video)}")

        # Start the processing thread if not already running
        if not self.currently_processing:
            self.currently_processing = True
            threading.Thread(target=self.process_queue, daemon=True).start()

    def process_queue(self):
        total_items = len(self.processing_queue)
        processed = 0

        while self.processing_queue:
            item = self.processing_queue.pop(0)
            item_type = item.get("type", "youtube")  # Default to youtube for backward compatibility
            bg_video = item["bg_video"]
            loop_video = item["loop_video"]
            video_dir = item["video_dir"]

            try:
                # Update progress
                self.progress_var.set((processed / total_items) * 100)

                if item_type == "youtube":
                    # Process YouTube URL
                    url = item["url"]
                    audio_dir = item["audio_dir"]

                    # Update status
                    self.status_var.set(f"Processing {processed+1}/{total_items}: {url}")

                    # 1. Download audio
                    self.log_message(f"Downloading audio from: {url}")
                    audio_file = self.download_audio(url, audio_dir)

                    if audio_file:
                        # 2. Merge audio with the selected background video
                        self.log_message(f"Merging audio with background video...")
                        output_file = self.merge_audio_video(audio_file, bg_video, video_dir, loop_video)

                        if output_file:
                            self.log_message(f"Successfully created: {os.path.basename(output_file)}")
                        else:
                            self.log_message(f"Failed to merge audio and video")
                    else:
                        self.log_message(f"Failed to download audio from: {url}")

                elif item_type == "local":
                    # Process local audio file
                    audio_file = item["audio_file"]

                    # Update status
                    self.status_var.set(f"Processing {processed+1}/{total_items}: {os.path.basename(audio_file)}")

                    # Check if audio file exists
                    if os.path.exists(audio_file):
                        # Merge audio with the selected background video
                        self.log_message(f"Merging audio file: {os.path.basename(audio_file)}")
                        output_file = self.merge_audio_video(audio_file, bg_video, video_dir, loop_video)

                        if output_file:
                            self.log_message(f"Successfully created: {os.path.basename(output_file)}")
                        else:
                            self.log_message(f"Failed to merge audio and video")
                    else:
                        self.log_message(f"Audio file not found: {audio_file}")

            except Exception as e:
                self.log_message(f"Error processing item {processed+1}: {str(e)}")

            processed += 1

        # Update final status
        self.progress_var.set(100)
        self.status_var.set(f"Processing complete. Processed {processed} items")
        self.log_message(f"All processing complete!")
        self.currently_processing = False

    def download_audio(self, url, output_dir):
        try:
            # Get a clean filename from the YouTube title
            title_command = [
                "yt-dlp",
                "--no-check-certificate",
                "--skip-download",
                "--print", "%(title)s",
                url
            ]

            result = subprocess.run(title_command, capture_output=True, text=True)

            if result.returncode == 0 and result.stdout.strip():
                # Clean the title to use as filename
                title = result.stdout.strip()
                clean_title = self.clean_filename(title)
                output_filename = f"{clean_title}.mp3"
                output_path = os.path.join(output_dir, output_filename)

                # Try different download approaches

                # Approach 1: Direct download without format conversion
                self.log_message("Trying direct download approach...")
                command1 = [
                    "yt-dlp",
                    "--no-check-certificate",
                    "--no-playlist",
                    "--ignore-errors",
                    "--format", "bestaudio",
                    "--output", output_path,
                    url
                ]

                download_result = subprocess.run(command1, capture_output=True, text=True)

                if download_result.returncode == 0 and os.path.exists(output_path):
                    self.log_message("Direct download successful")
                    return output_path

                # Approach 2: Try with simpler options
                self.log_message("Trying alternative download approach...")
                command2 = [
                    "yt-dlp",
                    "-x",  # Extract audio
                    "--audio-format", "mp3",
                    "--no-check-certificate",
                    "--output", output_path,
                    url
                ]

                download_result = subprocess.run(command2, capture_output=True, text=True)

                if download_result.returncode == 0 and os.path.exists(output_path):
                    self.log_message("Alternative download successful")
                    return output_path

                # Approach 3: Most basic approach
                self.log_message("Trying basic download approach...")
                command3 = [
                    "yt-dlp",
                    "-f", "bestaudio",
                    "--no-check-certificate",
                    "--output", output_path,
                    url
                ]

                download_result = subprocess.run(command3, capture_output=True, text=True)

                if download_result.returncode == 0:
                    # Check for any audio file that might have been created
                    directory = os.path.dirname(output_path)
                    basename = os.path.splitext(os.path.basename(output_path))[0]

                    for file in os.listdir(directory):
                        if file.startswith(basename) and file.endswith(('.mp3', '.m4a', '.webm', '.opus')):
                            file_path = os.path.join(directory, file)
                            self.log_message(f"Found audio file: {file}")

                            # If it's not already an mp3, rename it
                            if not file.endswith('.mp3'):
                                new_path = os.path.join(directory, f"{basename}.mp3")
                                os.rename(file_path, new_path)
                                self.log_message(f"Renamed to: {basename}.mp3")
                                return new_path
                            return file_path

            # If we get here, something went wrong
            self.log_message(f"All download approaches failed")
            if result.stderr:
                self.log_message(f"Error details: {result.stderr}")
            return None

        except Exception as e:
            self.log_message(f"Error in download_audio: {str(e)}")
            return None

    def merge_audio_video(self, audio_file, video_file, output_dir, loop_video):
        try:
            # Get base filename without extension
            audio_basename = os.path.basename(audio_file)
            audio_name = os.path.splitext(audio_basename)[0]

            # Create output filename
            output_filename = f"{audio_name}.mp4"
            output_path = os.path.join(output_dir, output_filename)

            self.log_message(f"Loading video file: {os.path.basename(video_file)}")

            # Try to load the video and audio with error handling
            try:
                video = mp.VideoFileClip(video_file)
            except Exception as e:
                self.log_message(f"Error loading video file: {str(e)}")
                return None

            self.log_message(f"Loading audio file: {os.path.basename(audio_file)}")

            try:
                audio = mp.AudioFileClip(audio_file)
            except Exception as e:
                self.log_message(f"Error loading audio file: {str(e)}")
                # Try a direct ffmpeg approach if available
                if self.try_ffmpeg_merge(video_file, audio_file, output_path):
                    return output_path
                return None

            # Calculate durations
            video_duration = video.duration
            audio_duration = audio.duration

            self.log_message(f"Video duration: {video_duration:.2f}s, Audio duration: {audio_duration:.2f}s")

            # Check if we need to loop the video
            if loop_video and audio_duration > video_duration:
                self.log_message(f"Looping video {int(audio_duration / video_duration) + 1} times")

                # Calculate how many times we need to loop the video
                loop_count = int(audio_duration / video_duration) + 1

                # Create a list of video clips to concatenate
                video_clips = []
                for i in range(loop_count):
                    video_clips.append(video)

                # Concatenate the video clips
                final_video = mp.concatenate_videoclips(video_clips)

                # Trim the final video to match the audio duration
                final_video = final_video.subclip(0, audio_duration)

                # Set the audio
                final_video = final_video.set_audio(audio)
            else:
                # If video is longer than audio or looping is disabled
                if audio_duration > video_duration and not loop_video:
                    self.log_message(f"Audio is longer than video, trimming audio")
                    # Trim the audio to match the video duration
                    audio = audio.subclip(0, video_duration)

                # Set the audio
                final_video = video.set_audio(audio)

                # Trim the final video if needed
                if audio_duration < video_duration:
                    self.log_message(f"Video is longer than audio, trimming video")
                    final_video = final_video.subclip(0, audio_duration)

            # Write the result to a file
            self.log_message(f"Writing output file: {output_filename}")
            final_video.write_videofile(output_path)

            return output_path

        except Exception as e:
            self.log_message(f"Error in merge_audio_video: {str(e)}")
            return None

    def try_ffmpeg_merge(self, video_file, audio_file, output_path):
        """Try to merge using direct ffmpeg command if available"""
        try:
            self.log_message("Attempting direct ffmpeg merge as fallback...")

            # Check if ffmpeg is available
            try:
                subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                self.log_message("ffmpeg not found, cannot use fallback method")
                return False

            # Use ffmpeg to merge the files
            command = [
                "ffmpeg",
                "-i", video_file,  # Video input
                "-i", audio_file,  # Audio input
                "-map", "0:v",     # Use video from first input
                "-map", "1:a",     # Use audio from second input
                "-c:v", "copy",    # Copy video codec
                "-shortest",       # End when shortest input ends
                output_path
            ]

            result = subprocess.run(command, capture_output=True, text=True)

            if result.returncode == 0 and os.path.exists(output_path):
                self.log_message("Direct ffmpeg merge successful")
                return True
            else:
                self.log_message(f"Direct ffmpeg merge failed: {result.stderr}")
                return False

        except Exception as e:
            self.log_message(f"Error in try_ffmpeg_merge: {str(e)}")
            return False

    def clean_filename(self, filename):
        # Remove invalid characters and replace spaces with underscores
        clean = re.sub(r'[\\/*?:"<>|]', "", filename)
        clean = clean.replace("-", "")  # Remove hyphens as requested
        clean = clean.replace(" ", "_")  # Replace spaces with underscores
        return clean

    def log_message(self, message):
        # Add timestamp to message
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # Insert at the end of the log
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)  # Scroll to the end

if __name__ == "__main__":
    root = tk.Tk()
    app = AutoYouTubeMerger(root)
    root.mainloop()
