"""
Leonardo AI API Integration
Handles image generation using Leonardo AI API
"""

import requests
import json
import os
import time
from typing import Optional, Dict, Any, List
import base64


class LeonardoAI:
    """Leonardo AI API client for image generation"""
    
    def __init__(self, api_key: str, base_url: str = "https://cloud.leonardo.ai/api/rest/v1"):
        """
        Initialize Leonardo AI client
        
        Args:
            api_key (str): Leonardo AI API key
            base_url (str): Base URL for Leonardo AI API
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        })
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        Get user information and credits
        
        Returns:
            Dict containing user info or None if failed
        """
        try:
            url = f"{self.base_url}/me"
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error getting user info: {str(e)}")
            return None
    
    def get_models(self) -> Optional[List[Dict[str, Any]]]:
        """
        Get available models
        
        Returns:
            List of available models or None if failed
        """
        try:
            url = f"{self.base_url}/platformModels"
            response = self.session.get(url)
            response.raise_for_status()
            data = response.json()
            return data.get('custom_models', [])
        except Exception as e:
            print(f"Error getting models: {str(e)}")
            return None
    
    def generate_image(self, prompt: str, model_id: str = None, width: int = 1024, height: int = 1024,
                      num_images: int = 1, guidance_scale: float = 7.0, num_inference_steps: int = 50,
                      seed: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        Generate images using Leonardo AI
        
        Args:
            prompt (str): Text prompt for image generation
            model_id (str): Model ID to use (if None, uses default)
            width (int): Image width
            height (int): Image height
            num_images (int): Number of images to generate
            guidance_scale (float): How closely to follow the prompt
            num_inference_steps (int): Number of denoising steps
            seed (int): Random seed for reproducibility
            
        Returns:
            Dict containing generation info or None if failed
        """
        try:
            url = f"{self.base_url}/generations"
            
            payload = {
                "prompt": prompt,
                "width": width,
                "height": height,
                "num_images": num_images,
                "guidance_scale": guidance_scale,
                "num_inference_steps": num_inference_steps,
                "presetStyle": "LEONARDO"
            }
            
            if model_id:
                payload["modelId"] = model_id
            
            if seed is not None:
                payload["seed"] = seed
            
            print(f"Generating image with Leonardo AI...")
            print(f"Prompt: {prompt[:100]}...")
            print(f"Size: {width}x{height}")
            
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            generation_id = result.get('sdGenerationJob', {}).get('generationId')
            
            if generation_id:
                print(f"Generation started with ID: {generation_id}")
                return {"generation_id": generation_id, "status": "pending"}
            else:
                print("Failed to start generation")
                return None
                
        except Exception as e:
            print(f"Error generating image: {str(e)}")
            return None
    
    def get_generation_status(self, generation_id: str) -> Optional[Dict[str, Any]]:
        """
        Check the status of an image generation
        
        Args:
            generation_id (str): Generation ID to check
            
        Returns:
            Dict containing generation status or None if failed
        """
        try:
            url = f"{self.base_url}/generations/{generation_id}"
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error getting generation status: {str(e)}")
            return None
    
    def wait_for_generation(self, generation_id: str, max_wait_time: int = 300, 
                           check_interval: int = 5) -> Optional[Dict[str, Any]]:
        """
        Wait for image generation to complete
        
        Args:
            generation_id (str): Generation ID to wait for
            max_wait_time (int): Maximum time to wait in seconds
            check_interval (int): How often to check status in seconds
            
        Returns:
            Dict containing completed generation or None if failed/timeout
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            status = self.get_generation_status(generation_id)
            
            if not status:
                print("Failed to get generation status")
                return None
            
            generation_status = status.get('generations_by_pk', {}).get('status')
            
            if generation_status == 'COMPLETE':
                print("Generation completed!")
                return status
            elif generation_status == 'FAILED':
                print("Generation failed!")
                return None
            else:
                print(f"Generation status: {generation_status}, waiting...")
                time.sleep(check_interval)
        
        print("Generation timed out")
        return None
    
    def download_images(self, generation_data: Dict[str, Any], output_dir: str, 
                       filename_prefix: str = "image") -> List[str]:
        """
        Download generated images
        
        Args:
            generation_data (dict): Generation data from completed generation
            output_dir (str): Directory to save images
            filename_prefix (str): Prefix for image filenames
            
        Returns:
            List of downloaded image file paths
        """
        downloaded_files = []
        
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            generations = generation_data.get('generations_by_pk', {})
            generated_images = generations.get('generated_images', [])
            
            for i, image_data in enumerate(generated_images):
                image_url = image_data.get('url')
                if not image_url:
                    continue
                
                # Download image
                response = requests.get(image_url)
                response.raise_for_status()
                
                # Save image
                filename = f"{filename_prefix}_{i+1}.jpg"
                filepath = os.path.join(output_dir, filename)
                
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
                downloaded_files.append(filepath)
                print(f"Downloaded: {filepath}")
            
            return downloaded_files
            
        except Exception as e:
            print(f"Error downloading images: {str(e)}")
            return downloaded_files
    
    def generate_and_download(self, prompt: str, output_dir: str, filename_prefix: str = "image",
                             model_id: str = None, width: int = 1024, height: int = 1024,
                             num_images: int = 1, max_wait_time: int = 300) -> List[str]:
        """
        Generate images and download them (convenience method)
        
        Args:
            prompt (str): Text prompt for image generation
            output_dir (str): Directory to save images
            filename_prefix (str): Prefix for image filenames
            model_id (str): Model ID to use
            width (int): Image width
            height (int): Image height
            num_images (int): Number of images to generate
            max_wait_time (int): Maximum time to wait for generation
            
        Returns:
            List of downloaded image file paths
        """
        # Start generation
        generation_result = self.generate_image(
            prompt=prompt,
            model_id=model_id,
            width=width,
            height=height,
            num_images=num_images
        )
        
        if not generation_result:
            return []
        
        generation_id = generation_result.get('generation_id')
        if not generation_id:
            return []
        
        # Wait for completion
        completed_generation = self.wait_for_generation(generation_id, max_wait_time)
        
        if not completed_generation:
            return []
        
        # Download images
        return self.download_images(completed_generation, output_dir, filename_prefix)
    
    def batch_generate_images(self, prompts: List[str], output_dir: str, 
                             model_id: str = None, width: int = 1024, height: int = 1024,
                             delay_between_requests: int = 2) -> List[str]:
        """
        Generate multiple images from a list of prompts
        
        Args:
            prompts (list): List of text prompts
            output_dir (str): Directory to save images
            model_id (str): Model ID to use
            width (int): Image width
            height (int): Image height
            delay_between_requests (int): Delay between requests in seconds
            
        Returns:
            List of all downloaded image file paths
        """
        all_downloaded_files = []
        
        for i, prompt in enumerate(prompts):
            print(f"\nGenerating image {i+1}/{len(prompts)}")
            
            filename_prefix = f"scene_{i+1:03d}"
            downloaded_files = self.generate_and_download(
                prompt=prompt,
                output_dir=output_dir,
                filename_prefix=filename_prefix,
                model_id=model_id,
                width=width,
                height=height,
                num_images=1
            )
            
            all_downloaded_files.extend(downloaded_files)
            
            # Delay between requests to avoid rate limiting
            if i < len(prompts) - 1:
                print(f"Waiting {delay_between_requests} seconds before next request...")
                time.sleep(delay_between_requests)
        
        return all_downloaded_files
