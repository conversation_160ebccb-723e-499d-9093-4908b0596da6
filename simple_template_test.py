"""
Simple Template Test
Test the flexible templates without GUI
"""

from flexible_templates import TemplateConfig, get_title_generation_prompt, get_script_generation_prompt, get_image_generation_prompt


def test_basic_templates():
    """Test basic template functionality"""
    print("=== Testing Basic Templates ===\n")
    
    topic = "Medieval Court Jesters"
    
    # Test title generation with default settings
    print("1. Default Title Generation:")
    print("-" * 40)
    title_prompt = get_title_generation_prompt(topic)
    print(title_prompt)
    print("\n")
    
    # Test title generation with custom settings
    print("2. Custom Title Generation (3 titles, 10 words max, serious tone):")
    print("-" * 40)
    custom_title_prompt = get_title_generation_prompt(
        topic=topic,
        num_titles=3,
        max_hook_words=10,
        tone="serious and scholarly",
        focus_regions=["Medieval Europe", "Byzantine Empire"]
    )
    print(custom_title_prompt)
    print("\n")
    
    # Test script generation with default settings
    print("3. Default Script Generation (first 500 chars):")
    print("-" * 40)
    script_prompt = get_script_generation_prompt(topic)
    print(script_prompt[:500] + "...")
    print("\n")
    
    # Test script generation with custom settings
    print("4. Custom Script Generation (8 sections, 800 words each):")
    print("-" * 40)
    custom_script_prompt = get_script_generation_prompt(
        topic=topic,
        total_words=6400,
        num_sections=8,
        words_per_section=800,
        style="academic lecturer + measured pace + thoughtful pauses",
        include_intro_template=False
    )
    print(custom_script_prompt[:500] + "...")
    print("\n")
    
    # Test image generation
    print("5. Default Image Generation:")
    print("-" * 40)
    sample_script = "You find yourself in a medieval castle courtyard. The court jester approaches with his colorful outfit, jingling bells, and a mischievous grin. The stone walls echo with laughter as nobles gather around."
    image_prompt = get_image_generation_prompt(sample_script, num_images=2)
    print(image_prompt)
    print("\n")
    
    # Test image generation with custom style
    print("6. Custom Image Generation (Renaissance style):")
    print("-" * 40)
    custom_image_prompt = get_image_generation_prompt(
        script_section=sample_script,
        num_images=1,
        art_style="Renaissance fresco painting",
        medium="oil on canvas",
        perspective="one-point perspective",
        aspect_ratio="4:3",
        color_palette="warm earth tones (ochre, sienna, umber, gold)"
    )
    print(custom_image_prompt)
    print("\n")


def test_template_config_class():
    """Test the TemplateConfig class"""
    print("=== Testing TemplateConfig Class ===\n")
    
    # Create default config
    config = TemplateConfig()
    
    print("1. Default Configuration:")
    print(f"   Title num_titles: {config.title_num_titles}")
    print(f"   Title max_hook_words: {config.title_max_hook_words}")
    print(f"   Title tone: {config.title_tone}")
    print(f"   Script total_words: {config.script_total_words}")
    print(f"   Script num_sections: {config.script_num_sections}")
    print(f"   Script style: {config.script_style}")
    print(f"   Image art_style: {config.image_art_style}")
    print(f"   Image aspect_ratio: {config.image_aspect_ratio}")
    print()
    
    # Test generating prompts with config
    topic = "Ancient Roman Engineering"
    
    print("2. Generated Prompts with Default Config:")
    print("-" * 40)
    
    title_prompt = config.get_title_prompt(topic)
    print("Title prompt (first 200 chars):")
    print(title_prompt[:200] + "...")
    print()
    
    script_prompt = config.get_script_prompt(topic)
    print("Script prompt (first 200 chars):")
    print(script_prompt[:200] + "...")
    print()
    
    # Modify config
    print("3. Modified Configuration:")
    print("-" * 40)
    config.title_num_titles = 7
    config.title_max_hook_words = 20
    config.title_tone = "mysterious and intriguing"
    config.title_focus_regions = ["Ancient Rome", "Roman Empire"]
    
    config.script_total_words = 12000
    config.script_num_sections = 12
    config.script_words_per_section = 1000
    config.script_style = "storytelling grandparent + warm and cozy"
    
    config.image_art_style = "Victorian book illustration"
    config.image_medium = "ink and wash"
    config.image_aspect_ratio = "3:4"
    
    print(f"   Modified title num_titles: {config.title_num_titles}")
    print(f"   Modified title tone: {config.title_tone}")
    print(f"   Modified script total_words: {config.script_total_words}")
    print(f"   Modified image art_style: {config.image_art_style}")
    print()
    
    # Test modified prompts
    modified_title_prompt = config.get_title_prompt(topic)
    print("Modified title prompt (first 300 chars):")
    print(modified_title_prompt[:300] + "...")
    print()
    
    # Test save/load functionality
    print("4. Save/Load Configuration:")
    print("-" * 40)
    
    # Save config to dict
    config_dict = config.save_to_dict()
    print(f"Config saved to dict with {len(config_dict)} main sections")
    print(f"Title section keys: {list(config_dict['title'].keys())}")
    print(f"Script section keys: {list(config_dict['script'].keys())}")
    print()
    
    # Create new config and load
    new_config = TemplateConfig()
    print(f"New config default title num_titles: {new_config.title_num_titles}")
    
    new_config.load_from_dict(config_dict)
    print(f"After loading - title num_titles: {new_config.title_num_titles}")
    print(f"After loading - title tone: {new_config.title_tone}")
    print(f"After loading - script total_words: {new_config.script_total_words}")
    print()


def test_edge_cases():
    """Test edge cases and error handling"""
    print("=== Testing Edge Cases ===\n")
    
    # Test with empty/None values
    print("1. Testing with None/empty values:")
    print("-" * 40)
    
    config = TemplateConfig()
    config.title_focus_regions = None
    config.title_custom_prefixes = None
    
    topic = "Test Topic"
    prompt = config.get_title_prompt(topic)
    print("Title prompt with None regions/prefixes works:", "✅" if prompt else "❌")
    
    # Test with very small/large values
    print("\n2. Testing with extreme values:")
    print("-" * 40)
    
    config.title_num_titles = 1
    config.title_max_hook_words = 5
    config.script_total_words = 1000
    config.script_num_sections = 2
    config.script_words_per_section = 500
    
    small_title_prompt = config.get_title_prompt(topic)
    small_script_prompt = config.get_script_prompt(topic)
    
    print("Small values work:", "✅" if small_title_prompt and small_script_prompt else "❌")
    
    config.title_num_titles = 20
    config.title_max_hook_words = 50
    config.script_total_words = 50000
    config.script_num_sections = 50
    
    large_title_prompt = config.get_title_prompt(topic)
    large_script_prompt = config.get_script_prompt(topic)
    
    print("Large values work:", "✅" if large_title_prompt and large_script_prompt else "❌")
    
    # Test with special characters in topic
    print("\n3. Testing with special characters:")
    print("-" * 40)
    
    special_topic = "Medieval Court Jesters & Their Fools: A Study of 13th-Century Entertainment (Part I)"
    special_prompt = config.get_title_prompt(special_topic)
    print("Special characters in topic work:", "✅" if special_prompt else "❌")
    
    print()


def main():
    """Run all tests"""
    print("🧪 Flexible Templates Test Suite\n")
    print("=" * 60)
    
    try:
        test_basic_templates()
        print("=" * 60)
        
        test_template_config_class()
        print("=" * 60)
        
        test_edge_cases()
        print("=" * 60)
        
        print("🎉 All tests completed successfully!")
        print("\nThe flexible templates are working correctly.")
        print("You can now use them in the main application:")
        print("1. Run: python boring_history_generator.py")
        print("2. Go to Settings tab")
        print("3. Click 'Configure Templates' to customize prompts")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
