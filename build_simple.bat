@echo off
echo Building Midjourney Prompt Generator...
echo.

REM Clean previous builds
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist __pycache__ rmdir /s /q __pycache__

echo Cleaning completed.
echo.

REM Build with PyInstaller
echo Building executable...
pyinstaller --onefile --windowed --name=MidjourneyPromptGenerator --add-data="demo_script.txt;." --add-data="README_midjourney_prompt_generator.md;." run_midjourney_prompt_generator.py

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo Executable created: dist\MidjourneyPromptGenerator.exe
echo.

REM Copy additional files
echo Copying additional files...
copy "demo_script.txt" "dist\" >nul 2>&1
copy "README_midjourney_prompt_generator.md" "dist\" >nul 2>&1

echo.
echo Files in dist folder:
dir /b dist\

echo.
echo Build completed! You can find the executable in the 'dist' folder.
pause
