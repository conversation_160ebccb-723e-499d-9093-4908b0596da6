"""
CapCut Project Creator
Creates CapCut project files for automatic import
"""

import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional


class CapCutProjectCreator:
    """Creates CapCut project files with timeline setup"""
    
    def __init__(self):
        self.project_template = {
            "version": "3.0.0",
            "id": "",
            "name": "",
            "create_time": 0,
            "update_time": 0,
            "duration": 0,
            "materials": {
                "videos": [],
                "audios": [],
                "images": [],
                "texts": [],
                "effects": [],
                "transitions": [],
                "stickers": []
            },
            "tracks": {
                "video_tracks": [],
                "audio_tracks": []
            },
            "canvas": {
                "width": 1920,
                "height": 1080,
                "color": "#000000"
            },
            "export_settings": {
                "resolution": "1080p",
                "fps": 30,
                "bitrate": 8000,
                "format": "mp4"
            }
        }
    
    def create_project(self, project_name: str, audio_file: str, image_files: List[str],
                      output_dir: str, background_video: Optional[str] = None) -> str:
        """
        Create a CapCut project file
        
        Args:
            project_name (str): Name of the project
            audio_file (str): Path to the main audio file
            image_files (list): List of image file paths
            output_dir (str): Directory to save the project file
            background_video (str): Optional background video file
            
        Returns:
            str: Path to the created project file
        """
        try:
            # Create project structure
            project = self.project_template.copy()
            project["id"] = str(uuid.uuid4())
            project["name"] = project_name
            
            current_time = int(datetime.now().timestamp() * 1000)
            project["create_time"] = current_time
            project["update_time"] = current_time
            
            # Calculate total duration from audio
            audio_duration = self._get_audio_duration(audio_file)
            project["duration"] = int(audio_duration * 1000)  # Convert to milliseconds
            
            # Add materials
            self._add_audio_material(project, audio_file)
            self._add_image_materials(project, image_files)
            
            if background_video:
                self._add_video_material(project, background_video)
            
            # Create timeline tracks
            self._create_timeline(project, audio_file, image_files, background_video, audio_duration)
            
            # Save project file
            project_file = os.path.join(output_dir, f"{project_name}.cep")
            os.makedirs(output_dir, exist_ok=True)
            
            with open(project_file, 'w', encoding='utf-8') as f:
                json.dump(project, f, indent=2, ensure_ascii=False)
            
            print(f"CapCut project created: {project_file}")
            return project_file
            
        except Exception as e:
            print(f"Error creating CapCut project: {str(e)}")
            return ""
    
    def _get_audio_duration(self, audio_file: str) -> float:
        """Get audio duration in seconds"""
        try:
            from pydub import AudioSegment
            audio = AudioSegment.from_file(audio_file)
            return len(audio) / 1000.0
        except ImportError:
            # Fallback: estimate based on file size (very rough)
            file_size = os.path.getsize(audio_file)
            # Rough estimate: 1MB ≈ 60 seconds for typical speech MP3
            return file_size / (1024 * 1024) * 60
        except Exception:
            # Default fallback
            return 300.0  # 5 minutes
    
    def _add_audio_material(self, project: Dict, audio_file: str):
        """Add audio file to project materials"""
        audio_material = {
            "id": str(uuid.uuid4()),
            "type": "audio",
            "path": os.path.abspath(audio_file),
            "name": os.path.basename(audio_file),
            "duration": self._get_audio_duration(audio_file) * 1000,
            "import_time": int(datetime.now().timestamp() * 1000)
        }
        project["materials"]["audios"].append(audio_material)
    
    def _add_video_material(self, project: Dict, video_file: str):
        """Add video file to project materials"""
        video_material = {
            "id": str(uuid.uuid4()),
            "type": "video",
            "path": os.path.abspath(video_file),
            "name": os.path.basename(video_file),
            "duration": 0,  # Will be set by CapCut
            "width": 1920,
            "height": 1080,
            "fps": 30,
            "import_time": int(datetime.now().timestamp() * 1000)
        }
        project["materials"]["videos"].append(video_material)
    
    def _add_image_materials(self, project: Dict, image_files: List[str]):
        """Add image files to project materials"""
        for image_file in image_files:
            if os.path.exists(image_file):
                image_material = {
                    "id": str(uuid.uuid4()),
                    "type": "image",
                    "path": os.path.abspath(image_file),
                    "name": os.path.basename(image_file),
                    "width": 1024,  # Default, will be detected by CapCut
                    "height": 1024,
                    "import_time": int(datetime.now().timestamp() * 1000)
                }
                project["materials"]["images"].append(image_material)
    
    def _create_timeline(self, project: Dict, audio_file: str, image_files: List[str],
                        background_video: Optional[str], total_duration: float):
        """Create timeline tracks with materials"""
        # Audio track
        audio_track = {
            "id": str(uuid.uuid4()),
            "type": "audio",
            "clips": []
        }
        
        # Add main audio clip
        if project["materials"]["audios"]:
            audio_material = project["materials"]["audios"][0]
            audio_clip = {
                "id": str(uuid.uuid4()),
                "material_id": audio_material["id"],
                "start_time": 0,
                "end_time": int(total_duration * 1000),
                "duration": int(total_duration * 1000),
                "volume": 1.0,
                "fade_in": 0,
                "fade_out": 0
            }
            audio_track["clips"].append(audio_clip)
        
        project["tracks"]["audio_tracks"].append(audio_track)
        
        # Video track for background video (if provided)
        if background_video and project["materials"]["videos"]:
            video_track = {
                "id": str(uuid.uuid4()),
                "type": "video",
                "clips": []
            }
            
            video_material = project["materials"]["videos"][0]
            video_clip = {
                "id": str(uuid.uuid4()),
                "material_id": video_material["id"],
                "start_time": 0,
                "end_time": int(total_duration * 1000),
                "duration": int(total_duration * 1000),
                "loop": True,  # Loop background video
                "opacity": 1.0,
                "transform": {
                    "x": 0,
                    "y": 0,
                    "scale_x": 1.0,
                    "scale_y": 1.0,
                    "rotation": 0
                }
            }
            video_track["clips"].append(video_clip)
            project["tracks"]["video_tracks"].append(video_track)
        
        # Video track for images
        if image_files and project["materials"]["images"]:
            image_track = {
                "id": str(uuid.uuid4()),
                "type": "video",
                "clips": []
            }
            
            # Calculate duration per image
            image_duration = (total_duration * 1000) / len(image_files)
            
            for i, image_material in enumerate(project["materials"]["images"]):
                start_time = int(i * image_duration)
                end_time = int((i + 1) * image_duration)
                
                image_clip = {
                    "id": str(uuid.uuid4()),
                    "material_id": image_material["id"],
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": int(image_duration),
                    "opacity": 1.0,
                    "transform": {
                        "x": 0,
                        "y": 0,
                        "scale_x": 1.0,
                        "scale_y": 1.0,
                        "rotation": 0
                    },
                    "crop": {
                        "left": 0,
                        "top": 0,
                        "right": 1,
                        "bottom": 1
                    }
                }
                image_track["clips"].append(image_clip)
            
            project["tracks"]["video_tracks"].append(image_track)
    
    def create_batch_projects(self, projects_data: List[Dict], output_dir: str) -> List[str]:
        """
        Create multiple CapCut projects
        
        Args:
            projects_data (list): List of project data dictionaries
            output_dir (str): Directory to save project files
            
        Returns:
            List of created project file paths
        """
        created_projects = []
        
        for project_data in projects_data:
            project_file = self.create_project(
                project_name=project_data.get("name", "Untitled"),
                audio_file=project_data.get("audio_file", ""),
                image_files=project_data.get("image_files", []),
                output_dir=output_dir,
                background_video=project_data.get("background_video")
            )
            
            if project_file:
                created_projects.append(project_file)
        
        return created_projects
    
    def create_project_folder_structure(self, base_dir: str, project_name: str) -> Dict[str, str]:
        """
        Create organized folder structure for a project
        
        Args:
            base_dir (str): Base directory for projects
            project_name (str): Name of the project
            
        Returns:
            Dict with folder paths
        """
        project_dir = os.path.join(base_dir, project_name)
        
        folders = {
            "project": project_dir,
            "audio": os.path.join(project_dir, "audio"),
            "images": os.path.join(project_dir, "images"),
            "video": os.path.join(project_dir, "video"),
            "capcut": os.path.join(project_dir, "capcut"),
            "scripts": os.path.join(project_dir, "scripts"),
            "prompts": os.path.join(project_dir, "prompts")
        }
        
        # Create all folders
        for folder_path in folders.values():
            os.makedirs(folder_path, exist_ok=True)
        
        return folders
