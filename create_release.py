"""
Create release package for Midjourney Prompt Generator
Creates a zip file ready for distribution
"""

import os
import zipfile
import shutil
from datetime import datetime
from pathlib import Path

def create_release_zip():
    """Create a release zip file"""
    
    # Get current date for version
    current_date = datetime.now().strftime("%Y%m%d")
    zip_filename = f"MidjourneyPromptGenerator_v{current_date}.zip"
    
    print(f"Creating release package: {zip_filename}")
    
    # Source directory
    source_dir = Path("MidjourneyPromptGenerator_Portable")
    
    if not source_dir.exists():
        print("Error: Portable package not found. Please run build_exe.py first.")
        return False
    
    # Create zip file
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in source_dir.rglob('*'):
            if file_path.is_file():
                # Add file to zip with relative path
                arcname = file_path.relative_to(source_dir.parent)
                zipf.write(file_path, arcname)
                print(f"Added: {arcname}")
    
    # Get file size
    file_size = os.path.getsize(zip_filename) / (1024 * 1024)  # MB
    
    print(f"\nRelease package created successfully!")
    print(f"File: {zip_filename}")
    print(f"Size: {file_size:.2f} MB")
    
    return True

def create_installer_info():
    """Create installation information file"""
    
    installer_info = """# Midjourney Prompt Generator - Installation Guide

## System Requirements:
- Windows 10/11 (64-bit)
- Internet connection for API calls
- OpenAI API key (required)

## Installation:
1. Extract all files from the zip to a folder of your choice
2. Run MidjourneyPromptGenerator.exe
3. No additional installation required - it's portable!

## First Time Setup:
1. Open the application
2. Go to "Setup" tab
3. Enter your OpenAI API key
4. Set directories for saving prompts and images
5. Click "Save Settings"
6. Test connection with "Test Connections" button

## Usage:
1. Go to "Script" tab
2. Load demo_script.txt or enter your own script
3. Click "Analyze Script"
4. Go to "Analysis" tab to see results
5. Click "Generate Midjourney Prompts"
6. Go to "Prompts" tab to view and edit prompts
7. Use "Generate with DALL-E 3" to create images

## Settings Location:
Settings are automatically saved to:
- Windows: %APPDATA%\\MidjourneyPromptGenerator\\settings.json

## Troubleshooting:
- If the app doesn't start, make sure you have Windows Defender/antivirus exceptions
- If API calls fail, check your internet connection and API keys
- For support, check the README file

## Files Included:
- MidjourneyPromptGenerator.exe - Main application (portable)
- demo_script.txt - Sample script for testing
- README_midjourney_prompt_generator.md - Complete documentation
- sample_settings.json - Settings template
- INSTRUCTIONS.txt - Quick start guide
- INSTALLATION.txt - This file

## Version Information:
- Build Date: {build_date}
- Features: Script Analysis, Prompt Generation, DALL-E 3 Integration
- Note: Midjourney API not available (no public API)

## License:
MIT License - Free for personal and commercial use
"""
    
    build_date = datetime.now().strftime("%Y-%m-%d")
    installer_info = installer_info.format(build_date=build_date)
    
    # Write to portable package
    portable_dir = Path("MidjourneyPromptGenerator_Portable")
    if portable_dir.exists():
        with open(portable_dir / "INSTALLATION.txt", "w", encoding="utf-8") as f:
            f.write(installer_info)
        print("Created INSTALLATION.txt")

def main():
    """Main function"""
    print("=" * 60)
    print("Midjourney Prompt Generator - Release Creator")
    print("=" * 60)
    
    # Create installation info
    create_installer_info()
    
    # Create release zip
    if create_release_zip():
        print("\n" + "=" * 60)
        print("Release package created successfully!")
        print("Ready for distribution!")
        print("=" * 60)
    else:
        print("Failed to create release package!")

if __name__ == "__main__":
    main()
