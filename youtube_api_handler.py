"""
YouTube API Handler for Channel Content Repurposer

This module handles all interactions with the YouTube API, including:
- Fetching channel videos and metadata
- Downloading video transcripts
- Uploading videos to YouTube
"""

import os
import re
import json
import time
import subprocess
import requests
from urllib.parse import urlparse, parse_qs
import google.oauth2.credentials
import google_auth_oauthlib.flow
import googleapiclient.discovery
import googleapiclient.errors
from googleapiclient.http import MediaFileUpload


class YouTubeAPIHandler:
    """Handles all interactions with the YouTube API"""
    
    def __init__(self, api_key=None, client_secrets_file=None):
        """
        Initialize the YouTube API Handler
        
        Args:
            api_key (str, optional): YouTube Data API key for public data access
            client_secrets_file (str, optional): Path to OAuth client secrets file for uploads
        """
        self.api_key = api_key
        self.client_secrets_file = client_secrets_file
        self.youtube = None
        self.authenticated = False
        
        # Initialize API for public data if API key is provided
        if api_key:
            self._init_public_api()
    
    def _init_public_api(self):
        """Initialize the YouTube API for public data access"""
        try:
            api_service_name = "youtube"
            api_version = "v3"
            self.youtube = googleapiclient.discovery.build(
                api_service_name, api_version, developerKey=self.api_key)
            return True
        except Exception as e:
            print(f"Error initializing YouTube API: {str(e)}")
            return False
    
    def authenticate(self, scopes=None):
        """
        Authenticate with OAuth for actions requiring authorization
        
        Args:
            scopes (list, optional): List of OAuth scopes to request
            
        Returns:
            bool: True if authentication was successful, False otherwise
        """
        if not self.client_secrets_file or not os.path.exists(self.client_secrets_file):
            print("Client secrets file not found")
            return False
            
        if scopes is None:
            scopes = ["https://www.googleapis.com/auth/youtube.upload",
                     "https://www.googleapis.com/auth/youtube.force-ssl"]
        
        try:
            # Create the flow using the client secrets file
            flow = google_auth_oauthlib.flow.InstalledAppFlow.from_client_secrets_file(
                self.client_secrets_file, scopes)
            
            # Run the OAuth flow
            credentials = flow.run_local_server(port=8080)
            
            # Build the YouTube API client
            self.youtube = googleapiclient.discovery.build(
                "youtube", "v3", credentials=credentials)
            
            self.authenticated = True
            return True
        except Exception as e:
            print(f"Authentication error: {str(e)}")
            return False
    
    def get_channel_id_from_url(self, channel_url):
        """
        Extract channel ID from various YouTube channel URL formats
        
        Args:
            channel_url (str): YouTube channel URL
            
        Returns:
            str: Channel ID or None if not found
        """
        # Try to parse the URL
        parsed_url = urlparse(channel_url)
        
        # Check if it's a valid YouTube URL
        if parsed_url.netloc not in ['www.youtube.com', 'youtube.com']:
            return None
        
        # Extract channel ID based on URL format
        path = parsed_url.path
        
        # Format: youtube.com/channel/CHANNEL_ID
        if path.startswith('/channel/'):
            return path.split('/channel/')[1].split('/')[0]
        
        # Format: youtube.com/c/CHANNEL_NAME or youtube.com/user/USERNAME
        # These require an API call to resolve to a channel ID
        if path.startswith('/c/') or path.startswith('/user/') or path.startswith('/@'):
            if not self.youtube:
                if not self._init_public_api():
                    return None
            
            # Extract the username or custom URL
            if path.startswith('/c/'):
                custom_name = path.split('/c/')[1].split('/')[0]
                search_term = custom_name
            elif path.startswith('/user/'):
                username = path.split('/user/')[1].split('/')[0]
                search_term = username
            elif path.startswith('/@'):
                handle = path.split('/@')[1].split('/')[0]
                search_term = handle
            
            # Search for the channel
            try:
                request = self.youtube.search().list(
                    part="snippet",
                    q=search_term,
                    type="channel",
                    maxResults=1
                )
                response = request.execute()
                
                if response['items']:
                    return response['items'][0]['id']['channelId']
            except Exception as e:
                print(f"Error resolving channel ID: {str(e)}")
                return None
        
        return None
    
    def get_channel_videos(self, channel_id, max_results=50):
        """
        Get videos from a YouTube channel
        
        Args:
            channel_id (str): YouTube channel ID
            max_results (int, optional): Maximum number of videos to retrieve
            
        Returns:
            list: List of video dictionaries with id, title, and url
        """
        if not self.youtube:
            if not self._init_public_api():
                return []
        
        videos = []
        next_page_token = None
        
        try:
            # First, get the channel's uploads playlist ID
            request = self.youtube.channels().list(
                part="contentDetails",
                id=channel_id
            )
            response = request.execute()
            
            if not response['items']:
                return []
            
            uploads_playlist_id = response['items'][0]['contentDetails']['relatedPlaylists']['uploads']
            
            # Now get the videos from the uploads playlist
            while len(videos) < max_results:
                request = self.youtube.playlistItems().list(
                    part="snippet",
                    playlistId=uploads_playlist_id,
                    maxResults=min(50, max_results - len(videos)),
                    pageToken=next_page_token
                )
                response = request.execute()
                
                for item in response['items']:
                    video_id = item['snippet']['resourceId']['videoId']
                    video_title = item['snippet']['title']
                    videos.append({
                        "id": video_id,
                        "title": video_title,
                        "url": f"https://www.youtube.com/watch?v={video_id}"
                    })
                
                next_page_token = response.get('nextPageToken')
                if not next_page_token:
                    break
            
            return videos
        except Exception as e:
            print(f"Error getting channel videos: {str(e)}")
            return []
    
    def download_transcript(self, video_id, output_dir):
        """
        Download transcript for a YouTube video using yt-dlp
        
        Args:
            video_id (str): YouTube video ID
            output_dir (str): Directory to save the transcript
            
        Returns:
            str: Path to the transcript file or None if failed
        """
        os.makedirs(output_dir, exist_ok=True)
        video_url = f"https://www.youtube.com/watch?v={video_id}"
        
        try:
            # Use yt-dlp to download subtitles/transcript
            command = [
                "yt-dlp",
                "--skip-download",
                "--write-auto-sub",
                "--sub-format", "vtt",
                "--sub-lang", "en",
                "--output", os.path.join(output_dir, f"{video_id}"),
                video_url
            ]
            
            result = subprocess.run(command, capture_output=True, text=True)
            
            if result.returncode == 0:
                # Check if the subtitle file was created
                subtitle_file = os.path.join(output_dir, f"{video_id}.en.vtt")
                if os.path.exists(subtitle_file):
                    return subtitle_file
            
            return None
        except Exception as e:
            print(f"Error downloading transcript: {str(e)}")
            return None
