import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import requests
import json
import csv
from datetime import datetime
import re
import threading
import time
from bs4 import BeautifulSoup
import yt_dlp
from urllib.parse import urlparse

class YouTubeCrawlerNoAPI:
    def __init__(self, root):
        self.root = root
        self.root.title("YouTube Channel Video Crawler (No API)")
        self.root.geometry("900x700")
        
        # Variables
        self.channel_url = tk.StringVar()
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="Sẵn sàng")
        self.videos_data = []
        self.max_videos = tk.IntVar(value=50)
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Channel URL input
        ttk.Label(main_frame, text="Channel URL hoặc Handle:").grid(row=0, column=0, sticky=tk.W, pady=5)
        url_entry = ttk.Entry(main_frame, textvariable=self.channel_url, width=60)
        url_entry.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Max videos input
        ttk.Label(main_frame, text="Số video tối đa:").grid(row=1, column=0, sticky=tk.W, pady=5)
        max_videos_spin = ttk.Spinbox(main_frame, from_=1, to=1000, textvariable=self.max_videos, width=10)
        max_videos_spin.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # Video type selection
        type_frame = ttk.LabelFrame(main_frame, text="Loại video", padding="5")
        type_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.video_type = tk.StringVar(value="all")
        ttk.Radiobutton(type_frame, text="Tất cả video", variable=self.video_type, value="all").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(type_frame, text="Chỉ Shorts", variable=self.video_type, value="shorts").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(type_frame, text="Chỉ video dài", variable=self.video_type, value="long").pack(side=tk.LEFT, padx=5)
        
        # Options frame
        options_frame = ttk.LabelFrame(main_frame, text="Tùy chọn crawl", padding="5")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.include_description = tk.BooleanVar(value=True)
        self.include_comments = tk.BooleanVar(value=False)
        self.include_subtitles = tk.BooleanVar(value=False)
        self.sort_by_date = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(options_frame, text="Bao gồm mô tả", variable=self.include_description).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(options_frame, text="Lấy bình luận top", variable=self.include_comments).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(options_frame, text="Lấy phụ đề", variable=self.include_subtitles).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(options_frame, text="Sắp xếp theo ngày", variable=self.sort_by_date).pack(side=tk.LEFT, padx=5)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=4, column=0, columnspan=3, pady=10)
        
        ttk.Button(buttons_frame, text="Bắt đầu Crawl", command=self.start_crawling).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Dừng", command=self.stop_crawling).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Xuất CSV", command=self.export_csv).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Xuất JSON", command=self.export_json).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Xóa dữ liệu", command=self.clear_data).pack(side=tk.LEFT, padx=5)
        
        # Progress bar
        ttk.Label(main_frame, text="Tiến độ:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=5, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Status label
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var)
        self.status_label.grid(row=6, column=0, columnspan=3, pady=5)
        
        # Info frame
        info_frame = ttk.LabelFrame(main_frame, text="Thông tin channel", padding="5")
        info_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.channel_info_text = tk.Text(info_frame, height=3, wrap=tk.WORD)
        self.channel_info_text.pack(fill=tk.BOTH, expand=True)
        
        # Results text area
        ttk.Label(main_frame, text="Video đã crawl:").grid(row=8, column=0, sticky=tk.W, pady=5)
        self.results_text = scrolledtext.ScrolledText(main_frame, width=100, height=20)
        self.results_text.grid(row=9, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(9, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # Stop flag
        self.stop_flag = False
        
    def normalize_channel_url(self, url):
        """Chuẩn hóa URL channel"""
        # Xử lý các định dạng URL khác nhau
        if not url.startswith(('http://', 'https://')):
            if url.startswith('@'):
                url = f"https://www.youtube.com/{url}"
            elif '/' not in url:
                url = f"https://www.youtube.com/@{url}"
            else:
                url = f"https://www.youtube.com/{url}"
        
        return url
        
    def get_channel_info_with_ytdlp(self, channel_url):
        """Lấy thông tin channel bằng yt-dlp"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': True,
                'playlist_items': '1',  # Chỉ lấy 1 video để test
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Thử lấy thông tin channel
                info = ydl.extract_info(channel_url, download=False)
                
                if info:
                    channel_info = {
                        'channel_id': info.get('channel_id', ''),
                        'channel': info.get('channel', ''),
                        'channel_url': info.get('channel_url', ''),
                        'description': info.get('description', ''),
                        'subscriber_count': info.get('channel_follower_count', 'N/A'),
                        'video_count': info.get('playlist_count', 'N/A')
                    }
                    return channel_info
        except Exception as e:
            print(f"Lỗi khi lấy thông tin channel: {e}")
        
        return None
        
    def get_videos_with_ytdlp(self, channel_url, max_videos=50):
        """Lấy danh sách video bằng yt-dlp"""
        all_videos = []
        video_type = self.video_type.get()
        
        try:
            # Thử crawl từ nhiều URL khác nhau
            urls_to_try = [
                channel_url.rstrip('/') + '/videos',
                channel_url.rstrip('/') + '/shorts', 
                channel_url.rstrip('/')
            ]
            
            # Nếu chỉ muốn shorts
            if video_type == "shorts":
                urls_to_try = [channel_url.rstrip('/') + '/shorts']
            # Nếu chỉ muốn video dài
            elif video_type == "long":
                urls_to_try = [channel_url.rstrip('/') + '/videos']
            
            for url in urls_to_try:
                if self.stop_flag or len(all_videos) >= max_videos:
                    break
                    
                try:
                    ydl_opts = {
                        'quiet': True,
                        'no_warnings': True,
                        'extract_flat': True,
                        'playlist_items': f'1:{max_videos}',
                        'ignoreerrors': True,  # Bỏ qua lỗi
                    }
                    
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                        self.status_var.set(f"Đang crawl từ: {url.split('/')[-1]}...")
                        info = ydl.extract_info(url, download=False)
                        
                        if info and 'entries' in info:
                            for entry in info['entries']:
                                if self.stop_flag or len(all_videos) >= max_videos:
                                    break
                                    
                                if entry:
                                    try:
                                        # Ước tính xem có phải shorts không (dưới 60s)
                                        duration = entry.get('duration', 0)
                                        is_short = duration > 0 and duration <= 60
                                        
                                        # Lọc theo loại video
                                        if video_type == "shorts" and not is_short:
                                            continue
                                        elif video_type == "long" and is_short:
                                            continue
                                            
                                        video_info = {
                                            'video_id': entry.get('id', ''),
                                            'title': entry.get('title', ''),
                                            'url': entry.get('url', ''),
                                            'duration': duration,
                                            'upload_date': entry.get('upload_date', ''),
                                            'view_count': entry.get('view_count', 0),
                                            'like_count': entry.get('like_count', 0),
                                            'channel': entry.get('channel', ''),
                                            'thumbnail': entry.get('thumbnail', ''),
                                            'description': '',
                                            'comments': [],
                                            'subtitles': '',
                                            'is_short': is_short,
                                            'video_type': 'Short' if is_short else 'Video'
                                        }
                                        
                                        # Kiểm tra duplicate
                                        if not any(v['video_id'] == video_info['video_id'] for v in all_videos):
                                            all_videos.append(video_info)
                                            
                                    except Exception as e:
                                        print(f"Lỗi xử lý entry: {e}")
                                        continue
                                        
                                    # Cập nhật progress
                                    progress = (len(all_videos) / max_videos) * 50
                                    self.progress_var.set(min(progress, 50))
                                    self.status_var.set(f"Đã tìm thấy {len(all_videos)} video...")
                                    self.root.update_idletasks()
                                    
                except Exception as e:
                    print(f"Lỗi khi crawl từ {url}: {e}")
                    continue
                    
            # Sắp xếp theo ngày nếu được chọn
            if self.sort_by_date.get() and all_videos:
                all_videos.sort(key=lambda x: x.get('upload_date', ''), reverse=True)
                
            return all_videos[:max_videos]
            
        except Exception as e:
            print(f"Lỗi chung khi lấy danh sách video: {e}")
            return all_videos
        
    def get_detailed_video_info(self, video_id):
        """Lấy thông tin chi tiết của một video với xử lý lỗi tốt hơn"""
        try:
            video_url = f"https://www.youtube.com/watch?v={video_id}"
            
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'writesubtitles': self.include_subtitles.get(),
                'writeautomaticsub': self.include_subtitles.get(),
                'skip_download': True,
                'ignoreerrors': True,  # Bỏ qua lỗi
                'extract_flat': False,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(video_url, download=False)
                
                if not info:
                    return self.get_default_video_info()
                
                detailed_info = {
                    'description': info.get('description', '') if self.include_description.get() else '',
                    'view_count': info.get('view_count', 0),
                    'like_count': info.get('like_count', 0),
                    'comment_count': info.get('comment_count', 0),
                    'tags': info.get('tags', []),
                    'upload_date': info.get('upload_date', ''),
                    'duration': info.get('duration', 0),
                    'subtitles': self.extract_subtitles(info) if self.include_subtitles.get() else '',
                    'category': info.get('category', ''),
                    'uploader': info.get('uploader', ''),
                    'resolution': info.get('resolution', 'N/A'),
                    'fps': info.get('fps', 'N/A')
                }
                
                # Lấy comments nếu được yêu cầu
                if self.include_comments.get():
                    try:
                        detailed_info['comments'] = self.get_video_comments(video_id)
                    except Exception as e:
                        print(f"Lỗi khi lấy comments cho video {video_id}: {e}")
                        detailed_info['comments'] = []
                
                return detailed_info
                
        except Exception as e:
            print(f"Lỗi khi lấy thông tin chi tiết video {video_id}: {e}")
            return self.get_default_video_info()
    
    def get_default_video_info(self):
        """Trả về thông tin mặc định khi có lỗi"""
        return {
            'description': '',
            'view_count': 'N/A',
            'like_count': 'N/A',
            'comment_count': 'N/A',
            'tags': [],
            'upload_date': '',
            'duration': 0,
            'subtitles': '',
            'comments': [],
            'category': '',
            'uploader': '',
            'resolution': 'N/A',
            'fps': 'N/A'
        }
        
    def extract_subtitles(self, video_info):
        """Trích xuất phụ đề từ thông tin video với xử lý lỗi"""
        try:
            subtitles_text = ""
            
            # Thử lấy phụ đề tiếng Việt trước
            for lang in ['vi', 'en', 'auto']:
                try:
                    if 'subtitles' in video_info and lang in video_info['subtitles']:
                        # Lấy subtitle đầu tiên có sẵn
                        subtitle_info = video_info['subtitles'][lang][0]
                        if 'url' in subtitle_info:
                            response = requests.get(subtitle_info['url'], timeout=10)
                            if response.status_code == 200:
                                # Xử lý subtitle (có thể là VTT hoặc SRT)
                                subtitles_text = self.parse_subtitle_content(response.text)
                                break
                except Exception as e:
                    print(f"Lỗi khi lấy subtitle {lang}: {e}")
                    continue
                    
            return subtitles_text
        except Exception as e:
            print(f"Lỗi chung khi extract subtitles: {e}")
            return ""
        
    def parse_subtitle_content(self, content):
        """Xử lý nội dung subtitle"""
        # Loại bỏ thẻ HTML và timestamp
        import re
        
        # Loại bỏ thẻ VTT
        content = re.sub(r'WEBVTT\n\n', '', content)
        # Loại bỏ timestamp
        content = re.sub(r'\d{2}:\d{2}:\d{2}\.\d{3} --> \d{2}:\d{2}:\d{2}\.\d{3}\n', '', content)
        # Loại bỏ thẻ HTML
        content = re.sub(r'<[^>]+>', '', content)
        # Loại bỏ dòng trống
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        
        return ' '.join(lines)
        
    def get_video_comments(self, video_id, max_comments=10):
        """Lấy bình luận của video với xử lý lỗi tốt hơn"""
        try:
            # Sử dụng yt-dlp để lấy comments
            video_url = f"https://www.youtube.com/watch?v={video_id}"
            
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'getcomments': True,
                'max_comments': max_comments,
                'skip_download': True,
                'ignoreerrors': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(video_url, download=False)
                
                comments = []
                if info and 'comments' in info:
                    for comment in info['comments'][:max_comments]:
                        try:
                            comment_info = {
                                'author': comment.get('author', 'Unknown'),
                                'text': comment.get('text', ''),
                                'like_count': comment.get('like_count', 0),
                                'timestamp': comment.get('timestamp', 0),
                                'reply_count': comment.get('reply_count', 0)
                            }
                            comments.append(comment_info)
                        except Exception as e:
                            print(f"Lỗi xử lý comment: {e}")
                            continue
                
                return comments
                
        except Exception as e:
            print(f"Lỗi khi lấy comments video {video_id}: {e}")
            return []
        
    def start_crawling(self):
        """Bắt đầu quá trình crawling"""
        if not self.channel_url.get():
            messagebox.showerror("Lỗi", "Vui lòng nhập Channel URL!")
            return
            
        # Reset stop flag
        self.stop_flag = False
        
        # Chạy crawling trong thread riêng
        thread = threading.Thread(target=self.crawl_channel)
        thread.daemon = True
        thread.start()
        
    def stop_crawling(self):
        """Dừng quá trình crawling"""
        self.stop_flag = True
        self.status_var.set("Đang dừng...")
        
    def crawl_channel(self):
        """Thực hiện crawling channel với xử lý lỗi tốt hơn"""
        try:
            # Chuẩn hóa URL
            channel_url = self.normalize_channel_url(self.channel_url.get())
            
            self.status_var.set("Đang lấy thông tin channel...")
            self.progress_var.set(0)
            
            # Lấy thông tin channel
            channel_info = self.get_channel_info_with_ytdlp(channel_url)
            if channel_info:
                info_text = f"Kênh: {channel_info['channel']}\n"
                info_text += f"Subscribers: {channel_info['subscriber_count']}\n"
                info_text += f"Tổng số video: {channel_info['video_count']}"
                
                self.channel_info_text.delete(1.0, tk.END)
                self.channel_info_text.insert(1.0, info_text)
                
            self.status_var.set("Đang lấy danh sách video...")
            
            # Lấy danh sách video
            videos = self.get_videos_with_ytdlp(channel_url, self.max_videos.get())
            
            if not videos:
                messagebox.showwarning("Cảnh báo", "Không tìm thấy video nào!")
                return
                
            self.status_var.set("Đang lấy thông tin chi tiết video...")
            
            # Lấy thông tin chi tiết từng video
            detailed_videos = []
            failed_count = 0
            
            for i, video in enumerate(videos):
                if self.stop_flag:
                    break
                    
                video_title = video['title'][:50] + "..." if len(video['title']) > 50 else video['title']
                self.status_var.set(f"Đang xử lý video {i+1}/{len(videos)}: {video_title}")
                
                try:
                    # Lấy thông tin chi tiết
                    detailed_info = self.get_detailed_video_info(video['video_id'])
                    
                    # Cập nhật thông tin video
                    video.update(detailed_info)
                    detailed_videos.append(video)
                    
                    # Hiển thị video vừa crawl
                    self.add_video_to_display(video)
                    
                except Exception as e:
                    failed_count += 1
                    print(f"Lỗi khi xử lý video {video['video_id']}: {e}")
                    
                    # Vẫn thêm video với thông tin cơ bản
                    video.update(self.get_default_video_info())
                    detailed_videos.append(video)
                    
                    # Hiển thị video với thông tin lỗi
                    video['title'] += " [LỖI - THÔNG TIN KHÔNG ĐẦY ĐỦ]"
                    self.add_video_to_display(video)
                
                # Cập nhật progress
                progress = 50 + (i + 1) / len(videos) * 50
                self.progress_var.set(progress)
                
                # Nghỉ một chút để tránh rate limit
                time.sleep(0.5)
                
            self.videos_data = detailed_videos
            
            # Thông báo kết quả
            success_count = len(detailed_videos) - failed_count
            result_message = f"Hoàn thành! Crawl được {success_count}/{len(detailed_videos)} video thành công"
            if failed_count > 0:
                result_message += f" ({failed_count} video bị lỗi)"
            
            if self.stop_flag:
                self.status_var.set(f"Đã dừng! Crawl được {len(self.videos_data)} video.")
            else:
                self.progress_var.set(100)
                self.status_var.set(result_message)
                
            # Hiển thị thông báo tổng kết
            if failed_count > 0:
                messagebox.showinfo("Hoàn thành với cảnh báo", 
                                  f"Đã crawl xong!\n\n"
                                  f"✅ Thành công: {success_count} video\n"
                                  f"❌ Lỗi: {failed_count} video\n\n"
                                  f"Các video bị lỗi vẫn được lưu với thông tin cơ bản.")
                
        except Exception as e:
            messagebox.showerror("Lỗi", f"Có lỗi nghiêm trọng xảy ra: {str(e)}")
            self.status_var.set("Có lỗi nghiêm trọng xảy ra!")
            
    def add_video_to_display(self, video):
        """Thêm video vào display với thông tin chi tiết hơn"""
        # Format thời gian
        upload_date = video.get('upload_date', '')
        if upload_date and len(upload_date) >= 8:
            formatted_date = f"{upload_date[:4]}-{upload_date[4:6]}-{upload_date[6:8]}"
        else:
            formatted_date = upload_date or "N/A"
            
        # Format duration
        duration = video.get('duration', 0)
        if duration:
            minutes = duration // 60
            seconds = duration % 60
            duration_str = f"{minutes}:{seconds:02d}"
        else:
            duration_str = "N/A"
            
        # Xác định loại video
        video_type = video.get('video_type', 'N/A')
        type_icon = "🩳" if video.get('is_short', False) else "📺"
        
        # Format numbers
        view_count = video.get('view_count', 'N/A')
        if isinstance(view_count, int):
            view_count = f"{view_count:,}"
            
        like_count = video.get('like_count', 'N/A')
        if isinstance(like_count, int):
            like_count = f"{like_count:,}"
            
        comment_count = video.get('comment_count', 'N/A')
        if isinstance(comment_count, int):
            comment_count = f"{comment_count:,}"
            
        # Số lượng comments crawl được
        comments_crawled = len(video.get('comments', []))
        comments_info = f" (crawl: {comments_crawled})" if comments_crawled > 0 else ""
        
        # Tags (chỉ hiển thị 5 đầu tiên)
        tags = video.get('tags', [])
        tags_str = ', '.join(tags[:5])
        if len(tags) > 5:
            tags_str += f" (+{len(tags)-5} more)"
            
        result_text = f"""
{'='*100}
{type_icon} {video['title']}
{'='*100}
ID: {video['video_id']} | Loại: {video_type} | Ngày: {formatted_date} | Thời lượng: {duration_str}
Views: {view_count} | Likes: {like_count} | Comments: {comment_count}{comments_info}
Channel: {video.get('channel', 'N/A')} | Category: {video.get('category', 'N/A')}
Resolution: {video.get('resolution', 'N/A')} | FPS: {video.get('fps', 'N/A')}
URL: https://www.youtube.com/watch?v={video['video_id']}
Tags: {tags_str if tags_str else 'Không có'}
Subtitles: {'Có' if video.get('subtitles') else 'Không'}
Mô tả: {video.get('description', '')[:150]}{'...' if len(video.get('description', '')) > 150 else ''}

"""
        self.results_text.insert(tk.END, result_text)
        self.results_text.see(tk.END)
        self.root.update_idletasks()
        duration_str = "N/A"
            
        result_text = f"""
{'='*100}
{video['title']}
{'='*100}
ID: {video['video_id']} | Ngày: {formatted_date} | Thời lượng: {duration_str}
Views: {video.get('view_count', 'N/A'):,} | Likes: {video.get('like_count', 'N/A'):,} | Comments: {video.get('comment_count', 'N/A'):,}
URL: https://www.youtube.com/watch?v={video['video_id']}
Tags: {', '.join(video.get('tags', [])[:5])}
Mô tả: {video.get('description', '')[:200]}{'...' if len(video.get('description', '')) > 200 else ''}

"""
        self.results_text.insert(tk.END, result_text)
        self.results_text.see(tk.END)
        self.root.update_idletasks()
        
    def export_csv(self):
        """Xuất dữ liệu ra file CSV"""
        if not self.videos_data:
            messagebox.showwarning("Cảnh báo", "Không có dữ liệu để xuất!")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    # Chuẩn bị dữ liệu export
                    export_data = []
                    for video in self.videos_data:
                        # Format comments cho CSV
                        comments_text = ""
                        if video.get('comments'):
                            comments_list = []
                            for comment in video['comments'][:5]:  # Chỉ lấy 5 comments đầu
                                comment_str = f"{comment.get('author', 'Unknown')}: {comment.get('text', '')[:100]}"
                                comments_list.append(comment_str)
                            comments_text = " | ".join(comments_list)
                        
                        export_data.append({
                            'video_id': video.get('video_id', ''),
                            'title': video.get('title', ''),
                            'video_type': video.get('video_type', 'N/A'),
                            'is_short': video.get('is_short', False),
                            'url': f"https://www.youtube.com/watch?v={video.get('video_id', '')}",
                            'upload_date': video.get('upload_date', ''),
                            'duration': video.get('duration', ''),
                            'view_count': video.get('view_count', ''),
                            'like_count': video.get('like_count', ''),
                            'comment_count': video.get('comment_count', ''),
                            'channel': video.get('channel', ''),
                            'category': video.get('category', ''),
                            'resolution': video.get('resolution', 'N/A'),
                            'fps': video.get('fps', 'N/A'),
                            'tags': ', '.join(video.get('tags', [])),
                            'description': video.get('description', ''),
                            'thumbnail': video.get('thumbnail', ''),
                            'subtitles': video.get('subtitles', ''),
                            'top_comments': comments_text,
                            'comments_count_crawled': len(video.get('comments', []))
                        })
                    
                    fieldnames = list(export_data[0].keys()) if export_data else []
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    
                    writer.writeheader()
                    writer.writerows(export_data)
                        
                messagebox.showinfo("Thành công", f"Đã xuất {len(self.videos_data)} video ra file CSV!")
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không thể xuất file CSV: {str(e)}")
                
    def export_json(self):
        """Xuất dữ liệu ra file JSON"""
        if not self.videos_data:
            messagebox.showwarning("Cảnh báo", "Không có dữ liệu để xuất!")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                # Chuẩn bị dữ liệu JSON với thông tin chi tiết
                channel_info_text = self.channel_info_text.get(1.0, tk.END).strip()
                
                export_data = {
                    'crawl_info': {
                        'crawl_time': datetime.now().isoformat(),
                        'channel_url': self.channel_url.get(),
                        'video_type_filter': self.video_type.get(),
                        'max_videos_requested': self.max_videos.get(),
                        'total_videos_found': len(self.videos_data),
                        'options': {
                            'include_description': self.include_description.get(),
                            'include_comments': self.include_comments.get(),
                            'include_subtitles': self.include_subtitles.get(),
                            'sort_by_date': self.sort_by_date.get()
                        }
                    },
                    'channel_info': channel_info_text,
                    'statistics': {
                        'total_videos': len(self.videos_data),
                        'shorts_count': len([v for v in self.videos_data if v.get('is_short', False)]),
                        'long_videos_count': len([v for v in self.videos_data if not v.get('is_short', False)]),
                        'videos_with_subtitles': len([v for v in self.videos_data if v.get('subtitles')]),
                        'videos_with_comments': len([v for v in self.videos_data if v.get('comments')])
                    },
                    'videos': self.videos_data
                }
                
                with open(filename, 'w', encoding='utf-8') as jsonfile:
                    json.dump(export_data, jsonfile, ensure_ascii=False, indent=2)
                    
                messagebox.showinfo("Thành công", f"Đã xuất {len(self.videos_data)} video ra file JSON!")
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không thể xuất file JSON: {str(e)}")
                
    def clear_data(self):
        """Xóa dữ liệu hiện tại"""
        self.videos_data = []
        self.results_text.delete(1.0, tk.END)
        self.channel_info_text.delete(1.0, tk.END)
        self.progress_var.set(0)
        self.status_var.set("Sẵn sàng")

if __name__ == "__main__":
    root = tk.Tk()
    app = YouTubeCrawlerNoAPI(root)
    root.mainloop()