# Script to Video Generator

This application takes a script as input, uses ElevenLabs API to generate voice narration, and OpenAI API to create images for each line of the script. It then synchronizes the images with the audio to create a video.

## Features

- Script input and parsing
- ElevenLabs TTS integration for high-quality voice narration
- OpenAI DALL-E 3 image generation with minimalist black and white stick figure style
- Image-audio synchronization
- Video export with customizable resolution

## Requirements

- Python 3.7+
- Required packages:
  - openai
  - elevenlabs
  - moviepy
  - pillow
  - requests
  - python-dotenv

## Installation

1. Clone or download this repository
2. Install the required packages:

```bash
pip install openai elevenlabs moviepy pillow requests python-dotenv
```

3. Create a `.env` file in the same directory as the script with your API keys (optional):

```
OPENAI_API_KEY=your_openai_api_key
ELEVENLABS_API_KEY=your_elevenlabs_api_key
```

## Usage

1. Run the application:

```bash
python script_to_video_generator.py
```

2. Setup Tab:
   - Enter your OpenAI and ElevenLabs API keys
   - Select an ElevenLabs voice from the dropdown (click "Refresh Voices" to load available voices)
   - Set the output directory where the generated video will be saved
   - Save your settings

3. Script Tab:
   - Enter your script in the text area
   - Each line of the script will be used to generate a separate image
   - You can load a script from a file or save it for later use

4. Generate Tab:
   - Set the image resolution (default: 1920x1080)
   - Click "Generate Video" to start the process
   - Monitor progress in the log area

## How It Works

1. The script is parsed into individual lines
2. ElevenLabs API is used to generate voice narration for the entire script
3. For each line of the script, an image is generated using OpenAI's DALL-E 3 with the prompt:
   ```
   Minimalist black and white stick figure illustration of: {line}
   ```
4. The images are synchronized with the audio based on timing
5. The final video is created by combining the images and audio

## Tips for Best Results

- Write clear, descriptive sentences that can be easily visualized
- Keep each line focused on a single concept or action
- Use simple language that describes visual elements
- Break up long paragraphs into separate lines for more image variety
- For best results, aim for 5-20 lines in your script

## Example Script

```
A stick figure stands at the edge of a cliff, looking at the horizon.
The figure takes a deep breath and jumps off the cliff.
Instead of falling, the figure begins to float upward.
The figure rises higher and higher into the clouds.
Birds fly alongside the floating figure.
The figure reaches out and touches a cloud.
The sun shines brightly above as the figure continues to rise.
The figure looks down and sees the world getting smaller.
With a smile, the figure spreads its arms like wings.
The figure soars through the sky, completely free.
```

## Troubleshooting

- **API Key Issues**: Make sure your API keys are entered correctly in the Setup tab
- **Voice Generation Fails**: Check your ElevenLabs API key and subscription limits
- **Image Generation Fails**: Check your OpenAI API key and credit balance
- **Video Creation Fails**: Ensure MoviePy is installed correctly with all dependencies

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [ElevenLabs](https://elevenlabs.io/) for voice generation
- [OpenAI](https://openai.com/) for image generation
- [MoviePy](https://zulko.github.io/moviepy/) for video editing
